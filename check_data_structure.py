#!/usr/bin/env python3

from pathlib import Path

def check_data_structure():
    """检查数据结构和文件"""
    
    # 检查基础路径
    raw_data_path = "/data/wangyiwen/3d_diffuser_actor/data/peract/raw/train/"
    base_path = Path(raw_data_path)
    
    print(f"检查基础路径: {base_path}")
    if not base_path.exists():
        print(f"✗ 基础路径不存在: {base_path}")
        return False
    print("✓ 基础路径存在")
    
    # 检查任务目录
    task_str = 'phone_on_base'
    task_path = base_path / task_str
    print(f"\n检查任务路径: {task_path}")
    if not task_path.exists():
        print(f"✗ 任务路径不存在: {task_path}")
        return False
    print("✓ 任务路径存在")
    
    # 列出任务目录下的内容
    task_contents = list(task_path.iterdir())
    print(f"任务目录内容: {[p.name for p in task_contents]}")
    
    # 检查all_variations目录
    all_variations_path = task_path / "all_variations"
    print(f"\n检查all_variations路径: {all_variations_path}")
    if not all_variations_path.exists():
        print(f"✗ all_variations路径不存在: {all_variations_path}")
        return False
    print("✓ all_variations路径存在")
    
    # 检查episodes目录
    episodes_path = all_variations_path / "episodes"
    print(f"\n检查episodes路径: {episodes_path}")
    if not episodes_path.exists():
        print(f"✗ episodes路径不存在: {episodes_path}")
        return False
    print("✓ episodes路径存在")
    
    # 列出所有episode目录
    episode_dirs = list(episodes_path.glob("episode*"))
    print(f"\n找到 {len(episode_dirs)} 个episode目录:")
    for ep_dir in episode_dirs[:5]:  # 只显示前5个
        print(f"  - {ep_dir.name}")
    
    if len(episode_dirs) == 0:
        print("✗ 没有找到任何episode目录")
        return False
    
    # 检查第一个episode目录的内容
    first_episode = episode_dirs[0]
    print(f"\n检查第一个episode目录: {first_episode}")
    episode_files = list(first_episode.iterdir())
    print(f"Episode目录内容: {[f.name for f in episode_files]}")
    
    # 检查关键文件
    demo_file = first_episode / "low_dim_obs.pkl"
    print(f"\n检查demo文件: {demo_file}")
    if demo_file.exists():
        print("✓ low_dim_obs.pkl 文件存在")
        print(f"文件大小: {demo_file.stat().st_size} bytes")
    else:
        print("✗ low_dim_obs.pkl 文件不存在")
        return False
    
    # 检查其他可能的文件
    other_files = ['variation_number.pkl', 'variation_descriptions.pkl']
    for filename in other_files:
        file_path = first_episode / filename
        if file_path.exists():
            print(f"✓ {filename} 存在")
        else:
            print(f"- {filename} 不存在")
    
    return True

if __name__ == "__main__":
    print("检查数据结构...")
    success = check_data_structure()
    if success:
        print("\n✓ 数据结构检查通过！")
    else:
        print("\n✗ 数据结构检查失败")
