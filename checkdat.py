import os
import pickle
import blosc
import numpy as np
import torch
from typing import List, Dict

def load_and_inspect_dat_file(file_path: str):
    """加载并检查使用blosc压缩的.dat文件"""
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在")
        return
    
    try:
        # 1. 读取并解压文件
        with open(file_path, 'rb') as f:
            compressed_data = f.read()
        decompressed_data = blosc.decompress(compressed_data)
        
        # 2. 反序列化数据
        state_dict = pickle.loads(decompressed_data)
        
        # 3. 检查数据结构
        if not isinstance(state_dict, list) or len(state_dict) != 6:
            print("错误: 数据结构不符合预期")
            return
        
        print("=== 数据加载成功 ===")
        print(f"数据包含 {len(state_dict)} 个部分")
        
        # 4. 解析各部分数据
        print("\n=== 第1部分: frame_ids ===")
        frame_ids = state_dict[0]
        print(f"类型: {type(frame_ids)}, 长度: {len(frame_ids)}")
        print(f"示例: {frame_ids[:5]}... (共{len(frame_ids)}帧)")
        
        print("\n=== 第2部分: obs_tensors (RGB-D观测) ===")
        obs_tensors = state_dict[1]  # numpy数组
        print(f"类型: {type(obs_tensors)}, 形状: {obs_tensors.shape}")
        print("结构说明: (时间步, 相机数, 模态数[RGB|XYZ], 通道, 高, 宽)")
        print(f"RGB示例 (第0帧, 相机0): {obs_tensors[0, 0, 0].shape}")
        print(f"XYZ示例 (第0帧, 相机0): {obs_tensors[0, 0, 1].shape}")
        
        print("\n=== 第3部分: keyframe_actions (关键帧动作) ===")
        keyframe_actions = state_dict[2]  # 列表中的torch张量
        gripper_pos = state_dict[4] 
        print(f"类型: {type(keyframe_actions)}, 长度: {len(keyframe_actions)}")
        if len(keyframe_actions) > 0:
            print(f"第0个动作形状: {keyframe_actions[0].shape}")
            for i in range(0,5):
                print(f"action数据: {keyframe_actions[i][:5]}...")
                print(f"gripperpos数据: {gripper_pos[i][:5]}...")
        
        print("\n=== 第4部分: attn_indices (注意力索引) ===")
        attn_indices = state_dict[3]  # 列表中的字典
        print(f"类型: {type(attn_indices)}, 长度: {len(attn_indices)}")
        if len(attn_indices) > 0:
            print(f"第0帧的相机键: {attn_indices[0].keys()}")
            for cam, idx in list(attn_indices[0].items())[:1]:
                print(f"相机 '{cam}' 的索引: {idx}")
        
        print("\n=== 第5部分: gripper_positions (夹爪位置) ===")
        gripper_pos = state_dict[4]  # 列表中的torch张量
        print(f"类型: {type(gripper_pos)}, 长度: {len(gripper_pos)}")
        if len(gripper_pos) > 0:
            print(f"第0个位置形状: {gripper_pos[0].shape}")
            print(f"示例数据: {gripper_pos[0][:5]}...")
        
        print("\n=== 第6部分: trajectories (轨迹数据) ===")
        trajectories = state_dict[5]  # 列表中的torch张量
        print(f"类型: {type(trajectories)}, 长度: {len(trajectories)}")
        if len(trajectories) > 0:
            print(f"第0条轨迹形状: {trajectories[0].shape}")
            print(f"示例数据: {trajectories[0][0, :5]}...")
        
        return state_dict
    
    except Exception as e:
        print(f"加载文件时出错: {str(e)}")
        return None

# 使用示例
file_path = "/data/wangyiwen/3d_diffuser_actor/data/peract/packaged_highres/train/phone_on_base+0/ep0.dat"
data = load_and_inspect_dat_file(file_path)

# 可视化示例 (需要安装matplotlib)
if data is not None and input("显示RGB图像示例? (y/n): ").lower() == 'y':
    import matplotlib.pyplot as plt
    obs = data[1]  # 获取观测数据
    
    # 显示第0帧，第0相机的RGB图像
    rgb_image = obs[0, 0, 0]  # (3, 256, 256)
    rgb_image = np.transpose(rgb_image, (1, 2, 0))  # 转为 (256, 256, 3)
    
    plt.figure(figsize=(8, 8))
    plt.imshow(rgb_image)
    plt.title("RGB示例 (第0帧, 相机0)")
    plt.axis('off')
    plt.show()