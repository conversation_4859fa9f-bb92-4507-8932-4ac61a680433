name: 3d_diffuser_actor
channels:
  - pytorch
  - nvidia
  - conda-forge
  - defaults
dependencies:
  - python=3.8
  - pip
  - pytorch=1.13.1
  - torchvision=0.14.1
  - torchaudio=0.13.1
  - pytorch-cuda=11.6
  - pip:
    - git+https://github.com/openai/CLIP.git
    - numpy==1.23.5
    - pillow
    - einops
    - typed-argument-parser
    - tqdm
    - transformers
    - absl-py
    - matplotlib
    - scipy
    - tensorboard
    - opencv-python
    - blosc
    - setuptools==57.5.0
    - beautifulsoup4
    - bleach>=6.0.0
    - defusedxml
    - jinja2>=3.0
    - jupyter-core>=4.7
    - jupyterlab-pygments
    - mistune==2.0.5
    - nbclient>=0.5.0
    - nbformat>=5.7
    - pandocfilters>=1.4.1
    - tinycss2
    - traitlets>=5.1