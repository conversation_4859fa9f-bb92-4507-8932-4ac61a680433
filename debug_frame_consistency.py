#!/usr/bin/env python3
"""
调试脚本：保存训练和测试数据的当前帧与目标帧进行对比
"""

import os
import sys
import random
from pathlib import Path
import numpy as np
import torch
import torch.nn.functional as F
from PIL import Image
import matplotlib.pyplot as plt
import json

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from datasets.dataset_engine import RLBenchDataset
from utils.utils_with_rlbench import RLBenchEnv, Actioner
from utils.common_utils import load_instructions


def tensor_to_image(tensor, channel_first=True, from_training=False):
    """将tensor转换为PIL Image
    
    Args:
        tensor: 输入张量
        channel_first: 是否是通道优先格式
        from_training: 是否来自训练数据（已经反归一化到[0,1]），否则假设在[-1,1]范围需要反归一化
    """
    if isinstance(tensor, torch.Tensor):
        tensor = tensor.detach().cpu().numpy()
    
    if channel_first and len(tensor.shape) == 3:
        # (C, H, W) -> (H, W, C)
        tensor = np.transpose(tensor, (1, 2, 0))
    
    # 处理不同的数据范围
    if from_training:
        # 训练数据已经在[0,1]范围
        if tensor.max() <= 1.0 and tensor.min() >= 0.0:
            tensor = (tensor * 255).astype(np.uint8)
        else:
            tensor = np.clip(tensor, 0, 1)
            tensor = (tensor * 255).astype(np.uint8)
    else:
        # 测试数据在[-1,1]范围，需要反归一化
        if tensor.min() < 0.0:  # 确认是在[-1,1]范围
            tensor = tensor / 2 + 0.5  # 转换到[0,1]范围
        tensor = np.clip(tensor, 0, 1)
        tensor = (tensor * 255).astype(np.uint8)
    
    # 如果只有3个通道，直接转换
    if tensor.shape[-1] == 3:
        return Image.fromarray(tensor)
    # 如果有4个通道（包含attention），只取前3个通道
    elif tensor.shape[-1] == 4:
        return Image.fromarray(tensor[:, :, :3])
    else:
        # 如果是单通道，转换为RGB
        if len(tensor.shape) == 2 or tensor.shape[-1] == 1:
            tensor = np.stack([tensor.squeeze()] * 3, axis=-1)
            return Image.fromarray(tensor)
        else:
            raise ValueError(f"Unsupported tensor shape: {tensor.shape}")


def save_training_frames(output_dir="debug_frames/training", num_demos=5):
    """保存训练数据的当前帧和目标帧"""
    print("=== 保存训练数据的当前帧和目标帧 ===")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置参数（与训练脚本一致）
    dataset_path = "/data/xjd/3d_diffuser_actor/data/peract/packaged_highres/train"
    instructions_path = "/data/wangyiwen/3d_diffuser_actor/instructions/peract/instructions_custom6.pkl"
    
    # 检查路径
    if not Path(dataset_path).exists():
        print(f"警告: 训练数据路径不存在: {dataset_path}")
        return None
    
    try:
        instructions = load_instructions(instructions_path)
    except:
        print(f"警告: 无法加载指令文件: {instructions_path}")
        instructions = None
    
    # 创建数据集
    tasks = ['phone_on_base']  # 与测试脚本一致
    taskvar = [(task, 0) for task in tasks]
    
    dataset = RLBenchDataset(
        root=dataset_path,
        instructions=instructions,
        taskvar=taskvar,
        max_episode_length=10,
        cameras=("front",),
        goal_frame_mode=1,  # 启用目标帧
        training=False,  # 不使用数据增强
        cache_size=0
    )
    
    print(f"训练数据集大小: {len(dataset)}")
    
    saved_data = []
    
    for demo_idx in range(min(num_demos, len(dataset))):
        print(f"\n处理训练demo {demo_idx}...")
        
        try:
            # 获取数据
            data = dataset[demo_idx]
            
            # 获取RGB数据 (B, ncam+1, 3, H, W) 因为goal_frame_mode=1添加了目标帧作为新视角
            rgbs = data['rgbs']  # (seq_len, ncam+1, 3, H, W)
            pcds = data['pcds']
            task = data['task'][0]
            
            print(f"  RGB shape: {rgbs.shape}")
            print(f"  Task: {task}")
            
            # 第一帧作为当前帧
            current_frame_idx = 0
            current_rgb = rgbs[current_frame_idx, 0]  # 第一个相机的RGB (3, H, W)
            goal_rgb = rgbs[current_frame_idx, -1]    # 最后一个相机位置是目标帧 (3, H, W)
            
            # 保存图像（训练数据已经反归一化到[0,1]范围）
            current_img = tensor_to_image(current_rgb, from_training=True)
            goal_img = tensor_to_image(goal_rgb, from_training=True)
            
            current_path = os.path.join(output_dir, f"demo_{demo_idx}_current.png")
            goal_path = os.path.join(output_dir, f"demo_{demo_idx}_goal.png")
            
            current_img.save(current_path)
            goal_img.save(goal_path)
            
            # 保存元数据
            demo_data = {
                'demo_idx': demo_idx,
                'task': task,
                'rgb_shape': list(rgbs.shape),
                'sequence_length': rgbs.shape[0],
                'num_cameras': rgbs.shape[1] - 1,  # 减去目标帧
                'current_path': current_path,
                'goal_path': goal_path,
                'data_source': 'training_dataset'
            }
            saved_data.append(demo_data)
            
            print(f"  保存当前帧: {current_path}")
            print(f"  保存目标帧: {goal_path}")
            
        except Exception as e:
            print(f"  处理demo {demo_idx}时出错: {e}")
            continue
    
    # 保存元数据
    metadata_path = os.path.join(output_dir, "metadata.json")
    with open(metadata_path, 'w') as f:
        json.dump(saved_data, f, indent=2)
    
    print(f"\n训练数据保存完成，共处理了 {len(saved_data)} 个demos")
    print(f"元数据保存至: {metadata_path}")
    
    return saved_data


def save_testing_frames(output_dir="debug_frames/testing", num_demos=5):
    """保存测试数据的当前帧和目标帧"""
    print("\n=== 保存测试数据的当前帧和目标帧 ===")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置参数（与测试脚本一致）
    data_dir = "/data/wangyiwen/3d_diffuser_actor/data/peract/raw/test/"
    instructions_path = "/data/wangyiwen/3d_diffuser_actor/instructions/peract/instructions_custom6.pkl"
    
    # 检查路径
    if not Path(data_dir).exists():
        print(f"警告: 测试数据路径不存在: {data_dir}")
        return None
        
    try:
        instructions = load_instructions(instructions_path)
    except:
        print(f"警告: 无法加载指令文件: {instructions_path}")
        return None
    
    saved_data = []
    
    try:
        # 创建测试环境
        env = RLBenchEnv(
            data_path=data_dir,
            image_size=[256, 256],
            apply_rgb=True,
            apply_pc=True,
            headless=True,
            apply_cameras=("front",),
            collision_checking=False
        )
        
        # 启动环境
        env.env.launch()
        
        # 创建假的模型和Actioner
        class FakeModel:
            def eval(self):
                pass
            def parameters(self):
                return [torch.tensor([1.0])]
        
        actioner = Actioner(
            policy=FakeModel(),
            instructions=instructions,
            apply_cameras=("front",),
            goal_frame_mode=1
        )
        
        task_str = "phone_on_base"
        variation = 0
        
        for demo_idx in range(num_demos):
            print(f"\n处理测试demo {demo_idx}...")
            
            try:
                # 获取演示数据
                demos = env.get_demo(task_str, variation, demo_idx)
                if not demos or len(demos) == 0:
                    print(f"  无法获取demo {demo_idx}")
                    continue
                
                demo = demos[0]
                
                # 加载目标帧到actioner中
                actioner.load_episode(task_str, variation, env, demo_idx)
                
                # 获取第一个观察（当前帧）
                current_obs = demo[0]  # 第一帧作为当前帧
                current_rgb, current_pcd, current_gripper = env.get_rgb_pcd_gripper_from_obs(current_obs)
                
                # 获取最后一个观察（目标帧）
                goal_obs = demo[-1]  # 最后一帧作为目标帧
                goal_rgb, goal_pcd, goal_gripper = env.get_rgb_pcd_gripper_from_obs(goal_obs)
                
                print(f"  Demo长度: {len(demo)}")
                print(f"  当前帧RGB shape: {current_rgb.shape}")
                print(f"  目标帧RGB shape: {goal_rgb.shape}")
                
                # 转换为图像格式 (注意：测试数据包含attention通道)
                # current_rgb: (1, 1, 4, H, W) -> (4, H, W)
                # goal_rgb: (1, 1, 4, H, W) -> (4, H, W)
                current_rgb_img = current_rgb.squeeze(0).squeeze(0)  # (4, H, W)
                goal_rgb_img = goal_rgb.squeeze(0).squeeze(0)        # (4, H, W)
                
                # 保存图像（只取前3个通道，去掉attention通道）
                # 测试数据在[-1,1]范围，需要反归一化
                current_img = tensor_to_image(current_rgb_img[:3], from_training=False)  # 只取RGB通道
                goal_img = tensor_to_image(goal_rgb_img[:3], from_training=False)        # 只取RGB通道
                
                current_path = os.path.join(output_dir, f"demo_{demo_idx}_current.png")
                goal_path = os.path.join(output_dir, f"demo_{demo_idx}_goal.png")
                
                current_img.save(current_path)
                goal_img.save(goal_path)
                
                # 还可以保存attention通道
                if current_rgb_img.shape[0] > 3:
                    attn_current = tensor_to_image(current_rgb_img[3:4].repeat(3, 1, 1), from_training=False)  # 复制attention通道到RGB
                    attn_goal = tensor_to_image(goal_rgb_img[3:4].repeat(3, 1, 1), from_training=False)
                    
                    attn_current_path = os.path.join(output_dir, f"demo_{demo_idx}_current_attention.png")
                    attn_goal_path = os.path.join(output_dir, f"demo_{demo_idx}_goal_attention.png")
                    
                    attn_current.save(attn_current_path)
                    attn_goal.save(attn_goal_path)
                
                # 保存从actioner中获取的目标帧（这是实际在预测时使用的）
                if actioner._goal_rgb is not None:
                    actioner_goal_rgb = actioner._goal_rgb[0]  # (3, H, W)
                    # actioner中的目标帧也是从测试环境来的，在[-1,1]范围
                    actioner_goal_img = tensor_to_image(actioner_goal_rgb, from_training=False)
                    actioner_goal_path = os.path.join(output_dir, f"demo_{demo_idx}_actioner_goal.png")
                    actioner_goal_img.save(actioner_goal_path)
                    print(f"  保存actioner目标帧: {actioner_goal_path}")
                
                # 保存元数据
                demo_data = {
                    'demo_idx': demo_idx,
                    'task': task_str,
                    'variation': variation,
                    'demo_length': len(demo),
                    'rgb_shape': list(current_rgb.shape),
                    'has_attention_channel': current_rgb.shape[2] > 3,
                    'current_path': current_path,
                    'goal_path': goal_path,
                    'data_source': 'testing_environment',
                    'actioner_goal_loaded': actioner._goal_rgb is not None
                }
                
                if current_rgb_img.shape[0] > 3:
                    demo_data.update({
                        'current_attention_path': attn_current_path,
                        'goal_attention_path': attn_goal_path
                    })
                
                if actioner._goal_rgb is not None:
                    demo_data['actioner_goal_path'] = actioner_goal_path
                
                saved_data.append(demo_data)
                
                print(f"  保存当前帧: {current_path}")
                print(f"  保存目标帧: {goal_path}")
                
            except Exception as e:
                print(f"  处理demo {demo_idx}时出错: {e}")
                import traceback
                traceback.print_exc()
                continue
        
        # 关闭环境
        env.env.shutdown()
        
    except Exception as e:
        print(f"创建测试环境时出错: {e}")
        return None
    
    # 保存元数据
    metadata_path = os.path.join(output_dir, "metadata.json")
    with open(metadata_path, 'w') as f:
        json.dump(saved_data, f, indent=2)
    
    print(f"\n测试数据保存完成，共处理了 {len(saved_data)} 个demos")
    print(f"元数据保存至: {metadata_path}")
    
    return saved_data


def create_comparison_visualization(training_data, testing_data, output_dir="debug_frames"):
    """创建训练和测试数据的对比可视化"""
    print("\n=== 创建对比可视化 ===")
    
    comparison_dir = os.path.join(output_dir, "comparison")
    os.makedirs(comparison_dir, exist_ok=True)
    
    # 创建对比图
    num_demos = min(len(training_data), len(testing_data), 5)
    
    for demo_idx in range(num_demos):
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle(f'训练 vs 测试数据对比 - Demo {demo_idx}', fontsize=16)
        
        try:
            # 训练数据
            train_data = training_data[demo_idx]
            train_current = Image.open(train_data['current_path'])
            train_goal = Image.open(train_data['goal_path'])
            
            # 测试数据
            test_data = testing_data[demo_idx]
            test_current = Image.open(test_data['current_path'])
            test_goal = Image.open(test_data['goal_path'])
            
            # 第一行：训练数据
            axes[0, 0].imshow(train_current)
            axes[0, 0].set_title('训练-当前帧')
            axes[0, 0].axis('off')
            
            axes[0, 1].imshow(train_goal)
            axes[0, 1].set_title('训练-目标帧')
            axes[0, 1].axis('off')
            
            # 如果有actioner目标帧，显示它
            if 'actioner_goal_path' in test_data:
                actioner_goal = Image.open(test_data['actioner_goal_path'])
                axes[0, 2].imshow(actioner_goal)
                axes[0, 2].set_title('测试-Actioner目标帧')
                axes[0, 2].axis('off')
            else:
                axes[0, 2].text(0.5, 0.5, 'Actioner目标帧\n未加载', 
                               ha='center', va='center', transform=axes[0, 2].transAxes)
                axes[0, 2].axis('off')
            
            # 第二行：测试数据
            axes[1, 0].imshow(test_current)
            axes[1, 0].set_title('测试-当前帧')
            axes[1, 0].axis('off')
            
            axes[1, 1].imshow(test_goal)
            axes[1, 1].set_title('测试-目标帧')
            axes[1, 1].axis('off')
            
            # 显示attention通道（如果存在）
            if 'current_attention_path' in test_data:
                test_attention = Image.open(test_data['current_attention_path'])
                axes[1, 2].imshow(test_attention)
                axes[1, 2].set_title('测试-Attention通道')
                axes[1, 2].axis('off')
            else:
                axes[1, 2].text(0.5, 0.5, 'Attention通道\n不存在', 
                               ha='center', va='center', transform=axes[1, 2].transAxes)
                axes[1, 2].axis('off')
            
            plt.tight_layout()
            comparison_path = os.path.join(comparison_dir, f"comparison_demo_{demo_idx}.png")
            plt.savefig(comparison_path, dpi=150, bbox_inches='tight')
            plt.close()
            
            print(f"保存对比图: {comparison_path}")
            
        except Exception as e:
            print(f"创建demo {demo_idx}对比图时出错: {e}")
            plt.close()
            continue


def main():
    """主函数"""
    print("开始调试训练和测试数据的帧一致性...")
    
    # 设置随机种子
    torch.manual_seed(0)
    np.random.seed(0)
    random.seed(0)
    
    # 保存训练数据帧
    training_data = save_training_frames(num_demos=5)
    
    # 保存测试数据帧
    testing_data = save_testing_frames(num_demos=5)
    
    # 创建对比可视化
    if training_data and testing_data:
        create_comparison_visualization(training_data, testing_data)
        
        # 创建分析报告
        create_analysis_report(training_data, testing_data)
    
    print("\n" + "="*50)
    print("帧数据保存和对比完成！")
    print("请检查 debug_frames/ 目录中的结果")
    print("="*50)


def create_analysis_report(training_data, testing_data, output_path="debug_frames/analysis_report.txt"):
    """创建分析报告"""
    print("\n=== 创建分析报告 ===")
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write("训练与测试数据帧一致性分析报告\n")
        f.write("="*50 + "\n\n")
        
        f.write("1. 数据源差异:\n")
        f.write("   训练数据: packaged_highres (预处理数据)\n")
        f.write("   测试数据: raw (原始RLBench环境数据)\n\n")
        
        f.write("2. 图像通道差异:\n")
        if training_data:
            f.write(f"   训练数据RGB shape: {training_data[0].get('rgb_shape', 'N/A')}\n")
        if testing_data:
            f.write(f"   测试数据RGB shape: {testing_data[0].get('rgb_shape', 'N/A')}\n")
            f.write(f"   测试数据包含attention通道: {testing_data[0].get('has_attention_channel', False)}\n\n")
        
        f.write("3. 目标帧获取方式:\n")
        f.write("   训练: 从预处理数据的state_dict[6]或关键帧序列最后帧\n")
        f.write("   测试: 从RLBench环境演示的最后观察帧\n\n")
        
        f.write("4. 处理的demos数量:\n")
        f.write(f"   训练数据: {len(training_data) if training_data else 0} demos\n")
        f.write(f"   测试数据: {len(testing_data) if testing_data else 0} demos\n\n")
        
        f.write("5. 关键发现:\n")
        f.write("   - 测试数据多了attention通道，需要在模型中正确处理\n")
        f.write("   - 目标帧来源不同，可能导致训练和测试的目标表示不一致\n")
        f.write("   - 需要验证两种数据源的目标帧是否表示相同的任务完成状态\n")
    
    print(f"分析报告保存至: {output_path}")


if __name__ == "__main__":
    main() 