#!/usr/bin/env python3
"""
测试图像显示功能的脚本
用于验证RGB和深度图像显示是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from experiments.mappings import CONFIG_MAPPING
import time

def test_image_display():
    """测试图像显示功能"""
    print("=== 图像显示功能测试 ===")
    
    # 使用ram_insertion配置进行测试
    exp_name = "ram_insertion"
    
    if exp_name not in CONFIG_MAPPING:
        print(f"错误: 找不到实验配置 {exp_name}")
        return False
    
    print(f"加载配置: {exp_name}")
    config = CONFIG_MAPPING[exp_name]()
    
    try:
        # 测试启用深度显示
        print("测试1: 启用深度显示")
        env = config.get_environment(fake_env=False, save_video=False, classifier=False, show_depth=True)
        
        print("环境初始化成功，开始测试图像显示...")
        print("应该看到RGB和深度图像窗口")
        print("按Ctrl+C退出测试")
        
        obs, info = env.reset()
        print("环境重置完成")
        
        # 运行几步来测试图像显示
        for i in range(50):
            actions = env.action_space.sample() * 0  # 零动作，避免机器人移动
            obs, reward, done, truncated, info = env.step(actions)
            time.sleep(0.1)  # 给图像显示一些时间
            
            if i % 10 == 0:
                print(f"步骤 {i}: 图像显示正常")
        
        env.close()
        print("✅ 启用深度显示测试完成")
        
        # 测试禁用深度显示
        print("\n测试2: 禁用深度显示")
        env = config.get_environment(fake_env=False, save_video=False, classifier=False, show_depth=False)
        
        print("应该只看到RGB图像窗口")
        
        obs, info = env.reset()
        
        # 运行几步
        for i in range(20):
            actions = env.action_space.sample() * 0
            obs, reward, done, truncated, info = env.step(actions)
            time.sleep(0.1)
            
            if i % 5 == 0:
                print(f"步骤 {i}: 只显示RGB图像")
        
        env.close()
        print("✅ 禁用深度显示测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = test_image_display()
        if success:
            print("\n🎉 所有测试通过！")
        else:
            print("\n❌ 测试失败")
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
