import requests
import math
from scipy.spatial.transform import Rotation as R


pose =    [
            0.35336482061068414,
            -0.12505003541645793,
            0.4593388126957117,
            -2.9665855995137393,
            0.005969393927833814,
            -0.7219435243583884
          ]
pose =  [
          -0.22349100045598674,
          0.00048727324082151625,
          0.2855305594648454,
          -2.997961972888163,
          -0.0017933009312772936,
          -0.7273218068328153
        ]

pose = [
          -0.21102168777324495,
          0.011878555846269512,
          0.26647537514908154,
          3.0136312146787563,
          0.08332187925853062,
          0.7495653050334977
        ]

pose = [
          -0.23499646765121543,
          -0.03167573321810517,
          0.21857045016560156,
          2.886363701676302,
          -0.007352604436934235,
          0.9894708852048758
        ]

pose = [
          -0.13038026218612914,
          -0.005500388631138347,
          0.35421811304744853,
          -3.104091228461844,
          -0.0567368301382074,
          -0.37284867831090857
        ]

pose =[
          -0.16026240321778146,
          -0.028480838378833542,
          0.34278250986939957,
          3.082694883467537,
          0.08501870950857997,
          0.5203543709734055
        ]

pose = [
          -0.16467027358762049,
          0.02482335277780521,
          0.3685103535869013,
          3.0765798144160352,
          -0.03708867268359584,
          0.33826382078044137
        ]

pose = [                                #table rearranage
          -0.2015432981916685,
          0.0010594182523531778,
          0.2504187812527446,
          2.9121075229951554,
          -0.03278764208448607,
          0.9366558255158614
        ]

pose = [
          -0.19417694987129963,
          -0.025367372471992118,
          0.28441228900149684,
          3.0390789522742967,
          -0.008377613892987374,
          0.6507901997175756
        ]
pose = [                              ##pour water
          -0.22873216168049926,
          -0.00607768095235469,
          0.29716733866473577,
          -2.9713358174625197,
          0.010009768056643081,
          -0.8802126491274674
        ]
pos = pose[:3]
# pos[0] += 0.5717120673331927
pos[0] += 0.6972006795565986
pos[1] += -0.05511538188320742
pos[2] += 0.49082325668663646 - 0.09790731

# pos[2] = 0.30
rot_vec = pose[3:]
rot_matrix = R.from_rotvec(rot_vec).as_matrix()
q = R.from_matrix(rot_matrix).as_quat()
pose = pos + list(q)

# Flask 服务的地址
# url = "http://127.0.0.1:5000/getpos"
url = "http://127.0.0.1:5000/pose"
# url = "http://127.0.0.1:5000/jointreset"
# url = "http://127.0.0.1:5000/open_gripper"
# url = "http://127.0.0.1:5000/get_gripper"
# 0.23681162774372214,-0.28537898838100606,0.4604986501155301,0.9922551993310874,0.043826843611666895,0.11596885801484827,0.0077492677199022895
# 发送 POST 请求的数据
data = {
    # "arr": [0.40759797875277517,-0.00020267717756727383,0.48627333018134256,0.9999997110196627,0.00011670833018897007,-0.000747619647148427,-7.351612131348465e-05]
    "arr": pose
    # "gripper_pos": 255
}

# 发送请求

response = requests.post(url, json=data)
# response = requests.post(url, json=data)

# print(response.json()['gripper'])

# pose = response.json()['pose']
# q = pose[3:]
# rot_matrix = R.from_quat(q).as_matrix()
# rot_vec = R.from_matrix(rot_matrix).as_rotvec().tolist()
# pose = pose[:3] + rot_vec
# print(pose)

# # 打印响应结果
# print("Response status code:", response.status_code)
# print("Response body:", response.text)

# import time
# time.sleep(2)
data = {"gripper_pos": 20}
url = "http://127.0.0.1:5000/move_gripper"

response = requests.post(url, json=data)

data = {"gripper_pos": 255}
url = "http://127.0.0.1:5000/close_gripper"
response = requests.post(url, json=data)