# cd robot_servers
source ~/anaconda3/etc/profile.d/conda.sh
conda activate hilserl

# source the catkin_ws that contains the serl_franka_controllers package
source ~/workspace/catkin_ws/devel/setup.bash

# Set ROS master URI to localhost
export ROS_MASTER_URI=http://localhost:11311

# script to start http server and ros controller
python serl_robot_infra/robot_servers/franka_server.py \
    --gripper_type=Franka \
    --robot_ip=********** \
    --flask_url=127.0.0.1 \
    --ros_port=11311 \