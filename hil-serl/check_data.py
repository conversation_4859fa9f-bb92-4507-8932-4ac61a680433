import pickle as pkl
import os
import numpy as np
import matplotlib.pyplot as plt
import cv2

def visualize_demo(pkl_path, idx=0, cam_key="agent"):  # 修改cam_key以查看不同相机
    assert os.path.exists(pkl_path), f"{pkl_path} 不存在"
    
    with open(pkl_path, "rb") as f:
        transitions = pkl.load(f)

    print(f"共加载 {len(transitions)} 帧数据")

    # 查看第 idx 帧
    t = transitions[idx]
    obs = t["observations"]
    next_obs = t["next_observations"]
    action = t["actions"]

    print(f"action: {action}")
    print(f"obs keys: {obs.keys()}")

    # 假设图像存储在 obs["images"][cam_key]["rgb"] 和 ["depth"]
    image = obs[cam_key][0]["rgb"]
    depth = obs[cam_key][0]["depth"]

    print(f"RGB shape: {image.shape}, dtype: {image.dtype}")
    print(f"Depth shape: {depth.shape}, dtype: {depth.dtype}")

    # 显示图像
    plt.figure(figsize=(12, 5))
    plt.subplot(1, 2, 1)
    plt.imshow(image)  # RGB 已转好
    plt.title("RGB")

    plt.subplot(1, 2, 2)
    plt.imshow(depth, cmap="gray")
    plt.title("Depth (raw uint16)")
    plt.colorbar()
    plt.tight_layout()
    plt.show()

    # 如果想保存一下当前帧
    cv2.imwrite("rgb.png", cv2.cvtColor(image, cv2.COLOR_RGB2BGR))
    norm_depth = (depth / depth.max() * 255).astype(np.uint8)
    cv2.imwrite("depth.png", norm_depth)
    print("已保存 rgb.png 和 depth.png")

# 示例用法
# 修改为你的 pkl 路径
visualize_demo("/home/<USER>/hil-serl/demo_data/ram_insertion_2_demos_2025-06-16_15-52-41.pkl", idx=50, cam_key="front")

# import pickle
# import os
# import numpy as np

# def describe_array(name, arr):
#     if isinstance(arr, np.ndarray):
#         print(f"{name}: shape={arr.shape}, dtype={arr.dtype}")
#     else:
#         print(f"{name}: type={type(arr)}")

# def print_demo_structure(pkl_path, idx=0):
#     assert os.path.exists(pkl_path), f"{pkl_path} 不存在"

#     with open(pkl_path, "rb") as f:
#         data = pickle.load(f)

#     print(f"总共 {len(data)} 帧")
#     sample = data[idx]
#     print(f"\n第 {idx} 帧的键：{list(sample.keys())}")

#     # 打印 observations
#     print("\n[observations]")
#     for k, v in sample['observations'].items():
#         describe_array(f"observations['{k}']", v)

#     # 打印 next_observations
#     print("\n[next_observations]")
#     for k, v in sample['next_observations'].items():
#         describe_array(f"next_observations['{k}']", v)

#     # 打印 actions
#     print("\n[actions]")
#     describe_array("actions", sample['actions'])

#     # 打印 info
#     print("\n[infos]")
#     for k, v in sample['infos'].items():
#         print(f"infos['{k}']: {type(v)} | value: {v if isinstance(v, (int, float, str)) else '...'}")

# if __name__ == "__main__":
#     pkl_file = "/home/<USER>/hil-serl/examples/experiments/ram_insertion/demo_data/ram_insertion_1_demos_2025-06-16_07-12-36.pkl"
#     print_demo_structure(pkl_file, idx=0)
