#!/bin/bash

# 激活 conda 环境（如果需要）
source ~/anaconda3/etc/profile.d/conda.sh
conda activate hilserl

# ==============================================
SUCCESS_NEEDED=5
DEMO_SAVE_DIR="./demo_data_0622_pick_bread"  # 可自定义路径
SHOW_DEPTH=true  # 是否显示深度图，设为false可禁用
# ==============================================

# 创建保存目录（如果不存在）
mkdir -p "$DEMO_SAVE_DIR"

EXP_NAME="ram_insertion"

# 启动采集
python examples/record_demos.py \
    --exp_name $EXP_NAME \
    --successes_needed $SUCCESS_NEEDED \
    --demo_save_dir $DEMO_SAVE_DIR \
    --show_depth=$SHOW_DEPTH

echo "数据收集完成！"
echo "保存路径: $DEMO_SAVE_DIR"
echo "深度显示: $SHOW_DEPTH"

