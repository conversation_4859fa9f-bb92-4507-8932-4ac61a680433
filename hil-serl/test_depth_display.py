#!/usr/bin/env python3
"""
测试深度显示功能的脚本
用于验证深度图像处理和显示是否正常工作
"""

import os
import sys
import numpy as np
import cv2
import pickle as pkl
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def process_depth_for_display(depth):
    """
    处理深度图用于显示（与record_demos.py中的函数相同）
    """
    depth_processed = depth.copy().astype(np.float32)

    # 自动缩放深度值
    valid_depth_mask = depth_processed > 0
    if np.any(valid_depth_mask):
        depth_min = depth_processed[valid_depth_mask].min()
        depth_max = depth_processed[valid_depth_mask].max()

        # 如果深度值很大，可能是毫米单位，转换为米
        if depth_max > 1000:
            depth_processed = depth_processed / 1000.0

    # 限制深度范围到合理区间
    depth_processed = np.clip(depth_processed, 0.0, 2.5)
    depth_processed[depth_processed < 0.05] = 0.0

    # 归一化到0-255用于显示
    depth_vis = depth_processed.copy()
    valid_mask = depth_vis > 0
    if np.any(valid_mask):
        d_min, d_max = depth_vis[valid_mask].min(), depth_vis[valid_mask].max()
        if d_max > d_min:
            depth_vis = (depth_vis - d_min) / (d_max - d_min)
        else:
            depth_vis = np.zeros_like(depth_vis)

    # 转换为8位图像并应用颜色映射
    depth_vis = (depth_vis * 255).astype(np.uint8)
    depth_colored = cv2.applyColorMap(depth_vis, cv2.COLORMAP_JET)

    return depth_colored, depth_processed, valid_mask


def test_with_demo_data(demo_file_path):
    """
    使用已有的demo数据测试深度显示功能
    """
    if not os.path.exists(demo_file_path):
        print(f"错误: 找不到demo文件 {demo_file_path}")
        return False

    print(f"加载demo文件: {demo_file_path}")
    
    try:
        with open(demo_file_path, 'rb') as f:
            transitions = pkl.load(f)
        
        print(f"成功加载 {len(transitions)} 个transitions")
        
        # 测试第一个transition的深度和RGB显示
        if len(transitions) > 0:
            transition = transitions[0]
            obs = transition["observations"]
            
            if "front" in obs and len(obs["front"]) > 0:
                front_obs = obs["front"][0]
                
                # 检查是否有RGB和深度数据
                has_rgb = "rgb" in front_obs
                has_depth = "depth" in front_obs
                
                print(f"RGB数据: {'存在' if has_rgb else '不存在'}")
                print(f"深度数据: {'存在' if has_depth else '不存在'}")
                
                if has_rgb:
                    rgb = front_obs["rgb"]
                    print(f"RGB形状: {rgb.shape}, 数据类型: {rgb.dtype}")
                    print(f"RGB值范围: [{rgb.min()}, {rgb.max()}]")
                
                if has_depth:
                    depth = front_obs["depth"]
                    print(f"深度形状: {depth.shape}, 数据类型: {depth.dtype}")
                    print(f"深度值范围: [{depth.min():.4f}, {depth.max():.4f}]")
                
                # 显示图像
                if has_rgb or has_depth:
                    print("\n按任意键切换到下一帧，按ESC退出...")
                    
                    for i, transition in enumerate(transitions[:10]):  # 只显示前10帧
                        obs = transition["observations"]["front"][0]
                        
                        # 显示RGB
                        if has_rgb:
                            rgb = obs["rgb"]
                            if rgb.dtype != np.uint8:
                                rgb = (rgb * 255).astype(np.uint8) if rgb.max() <= 1.0 else rgb.astype(np.uint8)
                            rgb_bgr = cv2.cvtColor(rgb, cv2.COLOR_RGB2BGR)
                            cv2.imshow(f'RGB Image - Frame {i}', rgb_bgr)
                        
                        # 显示深度
                        if has_depth:
                            depth = obs["depth"]
                            depth_colored, depth_processed, valid_mask = process_depth_for_display(depth)
                            cv2.imshow(f'Depth Visualization - Frame {i}', depth_colored)
                        
                        # 等待按键
                        key = cv2.waitKey(0) & 0xFF
                        if key == 27:  # ESC键
                            break
                        
                        # 关闭当前帧的窗口
                        cv2.destroyAllWindows()
                    
                    cv2.destroyAllWindows()
                    return True
                else:
                    print("错误: 没有找到RGB或深度数据")
                    return False
            else:
                print("错误: 观测数据格式不正确")
                return False
        else:
            print("错误: demo文件为空")
            return False
            
    except Exception as e:
        print(f"错误: 加载demo文件时出错: {e}")
        return False


def test_with_synthetic_data():
    """
    使用合成数据测试深度显示功能
    """
    print("生成合成测试数据...")
    
    # 创建合成RGB图像
    height, width = 240, 424
    rgb = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
    
    # 创建合成深度图像（模拟真实深度数据）
    depth = np.random.uniform(0.1, 2.0, (height, width)).astype(np.float32)
    # 添加一些无效区域
    depth[depth < 0.2] = 0.0
    
    print(f"合成RGB形状: {rgb.shape}")
    print(f"合成深度形状: {depth.shape}")
    print(f"深度值范围: [{depth.min():.4f}, {depth.max():.4f}]")
    
    # 显示图像
    print("显示合成数据，按任意键退出...")
    
    # 显示RGB
    rgb_bgr = cv2.cvtColor(rgb, cv2.COLOR_RGB2BGR)
    cv2.imshow('Synthetic RGB Image', rgb_bgr)
    
    # 显示深度
    depth_colored, depth_processed, valid_mask = process_depth_for_display(depth)
    cv2.imshow('Synthetic Depth Visualization', depth_colored)
    
    cv2.waitKey(0)
    cv2.destroyAllWindows()
    
    return True


def main():
    print("=== 深度显示功能测试 ===")
    
    # 查找demo数据文件
    demo_dirs = [
        "./demo_data",
        "./demo_data_0622_pick_bread",
        "../demo_data",
    ]
    
    demo_file = None
    for demo_dir in demo_dirs:
        if os.path.exists(demo_dir):
            pkl_files = list(Path(demo_dir).glob("*.pkl"))
            if pkl_files:
                demo_file = str(pkl_files[0])
                break
    
    if demo_file:
        print(f"找到demo文件: {demo_file}")
        success = test_with_demo_data(demo_file)
        if success:
            print("✅ 使用真实demo数据测试成功")
        else:
            print("❌ 使用真实demo数据测试失败")
    else:
        print("未找到demo数据文件，使用合成数据测试...")
        success = test_with_synthetic_data()
        if success:
            print("✅ 使用合成数据测试成功")
        else:
            print("❌ 使用合成数据测试失败")


if __name__ == "__main__":
    main()
