<?xml version="1.0"?>
<package format="2">
  <name>egg_flip_controller</name>
  <version>0.1.1</version>
  <description>Controller used for the egg flipping experiment in the HIL-SERL paper. Do not use this unless you know what you are doing and you accept the risk of damaging your robot. </description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>MIT</license>

  <author><PERSON></author>

  <buildtool_depend>catkin</buildtool_depend>

  <build_depend>message_generation</build_depend>
  <build_depend>eigen</build_depend>

  <build_export_depend>message_runtime</build_export_depend>

  <depend>controller_interface</depend>
  <depend>dynamic_reconfigure</depend>
  <depend>eigen_conversions</depend>
  <depend>franka_hw</depend>
  <depend>franka_gripper</depend>
  <depend>geometry_msgs</depend>
  <depend>hardware_interface</depend>
  <depend>tf</depend>
  <depend>tf_conversions</depend>
  <depend>libfranka</depend>
  <depend>pluginlib</depend>
  <depend>realtime_tools</depend>
  <depend>roscpp</depend>

  <exec_depend>franka_control</exec_depend>
  <exec_depend>franka_description</exec_depend>
  <exec_depend>message_runtime</exec_depend>
  <exec_depend>panda_moveit_config</exec_depend>
  <exec_depend>rospy</exec_depend>

  <export>
    <controller_interface plugin="${prefix}/egg_flip_controller_plugin.xml"/>
  </export>
</package>
