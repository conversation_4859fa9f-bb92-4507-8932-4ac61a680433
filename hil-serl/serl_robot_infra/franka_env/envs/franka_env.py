"""Gym Interface for Frank<PERSON>"""
import os
import numpy as np
import gymnasium as gym
import cv2
import copy
from scipy.spatial.transform import Rotation
import time
import requests
import queue
import threading
from datetime import datetime
from collections import OrderedDict
from typing import Dict

from franka_env.camera.video_capture import VideoCapture
from franka_env.camera.rs_capture import RSCapture
from franka_env.utils.rotations import euler_2_quat, quat_2_euler

import json
import subprocess
from scipy.spatial.transform import Rotation as R


class ImageDisplayer(threading.Thread):
    def __init__(self, queue, name, show_depth=True):
        threading.Thread.__init__(self)
        self.queue = queue
        self.daemon = True  # make this a daemon thread
        self.name = name
        self.show_depth = show_depth

    def process_depth_for_display(self, depth):
        """
        处理深度图用于显示
        """
        depth_processed = depth.copy().astype(np.float32)

        # 自动缩放深度值
        valid_depth_mask = depth_processed > 0
        if np.any(valid_depth_mask):
            depth_min = depth_processed[valid_depth_mask].min()
            depth_max = depth_processed[valid_depth_mask].max()

            # 如果深度值很大，可能是毫米单位，转换为米
            if depth_max > 1000:
                depth_processed = depth_processed / 1000.0

        # 限制深度范围到合理区间
        depth_processed = np.clip(depth_processed, 0.0, 2.5)
        depth_processed[depth_processed < 0.05] = 0.0

        # 归一化到0-255用于显示
        depth_vis = depth_processed.copy()
        valid_mask = depth_vis > 0
        if np.any(valid_mask):
            d_min, d_max = depth_vis[valid_mask].min(), depth_vis[valid_mask].max()
            if d_max > d_min:
                depth_vis = (depth_vis - d_min) / (d_max - d_min)
            else:
                depth_vis = np.zeros_like(depth_vis)

        # 转换为8位图像并应用颜色映射
        depth_vis = (depth_vis * 255).astype(np.uint8)
        depth_colored = cv2.applyColorMap(depth_vis, cv2.COLORMAP_JET)

        return depth_colored

    def run(self):
        while True:
            data = self.queue.get()  # retrieve data from the queue
            if data is None:  # None is our signal to exit
                break

            if isinstance(data, dict):
                rgb_frames = []
                depth_frames = []

                for k, v in data.items():
                    if "rgb" in k and "full" not in k:
                        # RGB数据
                        rgb_frames.append(cv2.resize(v, (128, 128)))
                    elif "depth" in k and "full" not in k and self.show_depth:
                        # 深度数据
                        depth_colored = self.process_depth_for_display(v)
                        depth_frames.append(cv2.resize(depth_colored, (128, 128)))
                    elif "full" not in k and "rgb" not in k and "depth" not in k:
                        # 原有的RGB数据格式（向后兼容）
                        rgb_frames.append(cv2.resize(v, (128, 128)))

                # 显示RGB图像
                if rgb_frames:
                    rgb_frame = np.concatenate(rgb_frames, axis=1)
                    cv2.imshow(self.name, rgb_frame)

                # 显示深度图像
                if depth_frames and self.show_depth:
                    depth_frame = np.concatenate(depth_frames, axis=1)
                    cv2.imshow(self.name + "_Depth", depth_frame)

            cv2.waitKey(1)


##############################################################################


class DefaultEnvConfig:
    """Default configuration for FrankaEnv. Fill in the values below."""

    SERVER_URL: str = "http://127.0.0.1:5000/"
    REALSENSE_CAMERAS: Dict = {
        "wrist_1": "130322274175",
        "wrist_2": "127122270572",
    }
    IMAGE_CROP: dict[str, callable] = {}
    TARGET_POSE: np.ndarray = np.zeros((6,))
    GRASP_POSE: np.ndarray = np.zeros((6,))
    REWARD_THRESHOLD: np.ndarray = np.zeros((6,))
    ACTION_SCALE = np.zeros((3,))
    RESET_POSE = np.zeros((6,))
    RANDOM_RESET = False
    RANDOM_XY_RANGE = (0.0,)
    RANDOM_RZ_RANGE = (0.0,)
    ABS_POSE_LIMIT_HIGH = np.zeros((6,))
    ABS_POSE_LIMIT_LOW = np.zeros((6,))
    COMPLIANCE_PARAM: Dict[str, float] = {}
    RESET_PARAM: Dict[str, float] = {}
    PRECISION_PARAM: Dict[str, float] = {}
    LOAD_PARAM: Dict[str, float] = {
        "mass": 0.0,
        "F_x_center_load": [0.0, 0.0, 0.0],
        "load_inertia": [0, 0, 0, 0, 0, 0, 0, 0, 0]
    }
    DISPLAY_IMAGE: bool = True
    GRIPPER_SLEEP: float = 0.6
    MAX_EPISODE_LENGTH: int = 100
    JOINT_RESET_PERIOD: int = 0


##############################################################################


class FrankaEnv(gym.Env):
    def __init__(
        self,
        hz=10,
        fake_env=False,
        save_video=False,
        config: DefaultEnvConfig = None,
        set_load=False,
        show_depth=True,
    ):
        self.action_scale = config.ACTION_SCALE
        self._TARGET_POSE = config.TARGET_POSE
        self._RESET_POSE = config.RESET_POSE
        self._REWARD_THRESHOLD = config.REWARD_THRESHOLD
        self.url = config.SERVER_URL
        self.config = config
        self.max_episode_length = config.MAX_EPISODE_LENGTH
        self.display_image = config.DISPLAY_IMAGE
        self.show_depth = show_depth
        self.gripper_sleep = config.GRIPPER_SLEEP

        # convert last 3 elements from euler to quat, from size (6,) to (7,)
        self.resetpos = np.concatenate(
            [config.RESET_POSE[:3], euler_2_quat(config.RESET_POSE[3:])]
        )
        self._update_currpos()
        self.last_gripper_act = time.time()
        self.lastsent = time.time()
        self.randomreset = config.RANDOM_RESET
        self.random_xy_range = config.RANDOM_XY_RANGE
        self.random_rz_range = config.RANDOM_RZ_RANGE
        self.hz = hz
        self.joint_reset_cycle = config.JOINT_RESET_PERIOD  # reset the robot joint every 200 cycles

        self.save_video = save_video
        if self.save_video:
            print("Saving videos!")
            self.recording_frames = []

        # boundary box
        self.xyz_bounding_box = gym.spaces.Box(
            config.ABS_POSE_LIMIT_LOW[:3],
            config.ABS_POSE_LIMIT_HIGH[:3],
            dtype=np.float64,
        )
        self.rpy_bounding_box = gym.spaces.Box(
            config.ABS_POSE_LIMIT_LOW[3:],
            config.ABS_POSE_LIMIT_HIGH[3:],
            dtype=np.float64,
        )
        # Action/Observation Space
        self.action_space = gym.spaces.Box(
            np.ones((7,), dtype=np.float32) * -1,
            np.ones((7,), dtype=np.float32),
        )

        self.observation_space = gym.spaces.Dict(
            {
                "state": gym.spaces.Dict(
                    {
                        "tcp_pose": gym.spaces.Box(
                            -np.inf, np.inf, shape=(7,)
                        ),  # xyz + quat
                        "tcp_vel": gym.spaces.Box(-np.inf, np.inf, shape=(6,)),
                        "gripper_pose": gym.spaces.Box(-1, 1, shape=(1,)),
                        "tcp_force": gym.spaces.Box(-np.inf, np.inf, shape=(3,)),
                        "tcp_torque": gym.spaces.Box(-np.inf, np.inf, shape=(3,)),
                    }
                ),
                "images": gym.spaces.Dict(
                    {key: gym.spaces.Box(0, 255, shape=(128, 128, 3), dtype=np.uint8) 
                                for key in config.REALSENSE_CAMERAS}
                ),
            }
        )
        self.cycle_count = 0

        if fake_env:
            return

        self.cap = None
        self.init_cameras(config.REALSENSE_CAMERAS)
        if self.display_image:
            self.img_queue = queue.Queue()
            self.displayer = ImageDisplayer(self.img_queue, self.url, self.show_depth)
            self.displayer.start()

        if set_load:
            input("Put arm into programing mode and press enter.")
            # requests.post(self.url + "set_load", json=self.config.LOAD_PARAM)

            # Convert LOAD_PARAM to JSON string
            load_param_json = json.dumps(self.config.LOAD_PARAM)

            # Build the curl command
            curl_command = [
                "curl",
                "-X", "POST", 
                self.url + "set_load",  # URL to set load parameters
                "-H", "Content-Type: application/json",  # Header
                "-d", load_param_json  # Data
            ]

            # Execute the curl command
            result = subprocess.run(curl_command, capture_output=True, text=True)

            input("Put arm into execution mode and press enter.")
            for _ in range(2):
                self._recover()
                time.sleep(1)

        if not fake_env:
            from pynput import keyboard
            self.terminate = False
            def on_press(key):
                if key == keyboard.Key.esc:
                    self.terminate = True
            self.listener = keyboard.Listener(on_press=on_press)
            self.listener.start()

        print("Initialized Franka")

    def clip_safety_box(self, pose: np.ndarray) -> np.ndarray:
        """Clip the pose to be within the safety box."""
        pose[:3] = np.clip(
            pose[:3], self.xyz_bounding_box.low, self.xyz_bounding_box.high
        )
        euler = Rotation.from_quat(pose[3:]).as_euler("xyz")

        # Clip first euler angle separately due to discontinuity from pi to -pi
        sign = np.sign(euler[0])
        euler[0] = sign * (
            np.clip(
                np.abs(euler[0]),
                self.rpy_bounding_box.low[0],
                self.rpy_bounding_box.high[0],
            )
        )

        euler[1:] = np.clip(
            euler[1:], self.rpy_bounding_box.low[1:], self.rpy_bounding_box.high[1:]
        )
        pose[3:] = Rotation.from_euler("xyz", euler).as_quat()

        return pose

    def step(self, action: np.ndarray) -> tuple:
        """standard gym step function."""
        start_time = time.time()
        action = np.clip(action, self.action_space.low, self.action_space.high)
        xyz_delta = action[:3]

        self.nextpos = self.currpos.copy()
        self.nextpos[:3] = self.nextpos[:3] + xyz_delta * self.action_scale[0]

        # GET ORIENTATION FROM ACTION
        self.nextpos[3:] = (
            Rotation.from_euler("xyz", action[3:6] * self.action_scale[1])
            * Rotation.from_quat(self.currpos[3:])
        ).as_quat()

        gripper_action = action[6] * self.action_scale[2]

        self._send_gripper_command(gripper_action)
        # self._send_pos_command(self.clip_safety_box(self.nextpos))
        self._send_pos_command(self.nextpos)

        self.curr_path_length += 1
        dt = time.time() - start_time
        time.sleep(max(0, (1.0 / self.hz) - dt))

        self._update_currpos()
        ob = self._get_obs()
        # reward = self.compute_reward(ob)、
        reward = 0
        # done = self.curr_path_length >= self.max_episode_length or reward or self.terminate
        done = False
        return ob, int(reward), done, False, {"succeed": reward}

    def compute_reward(self, obs) -> bool:
        current_pose = obs["state"]["tcp_pose"]
        # convert from quat to euler first
        current_rot = Rotation.from_quat(current_pose[3:]).as_matrix()
        target_rot = Rotation.from_euler("xyz", self._TARGET_POSE[3:]).as_matrix()
        diff_rot = current_rot.T  @ target_rot
        diff_euler = Rotation.from_matrix(diff_rot).as_euler("xyz")
        delta = np.abs(np.hstack([current_pose[:3] - self._TARGET_POSE[:3], diff_euler]))
        # print(f"Delta: {delta}")
        if np.all(delta < self._REWARD_THRESHOLD):
            return True
        else:
            # print(f'Goal not reached, the difference is {delta}, the desired threshold is {self._REWARD_THRESHOLD}')
            return False

    def get_im(self) -> Dict[str, np.ndarray]:
        """Get images from the realsense cameras."""
        images = {}
        display_images = {}
        full_res_images = {}  # New dictionary to store full resolution cropped images
        for key, cap in self.cap.items():
            try:
                frame = cap.read()
                rgb = frame[..., :3].astype(np.uint8)
                depth = frame[..., 3]
                cropped_rgb = self.config.IMAGE_CROP[key](rgb) if key in self.config.IMAGE_CROP else rgb
                cv2.imwrite("1.png", cropped_rgb)
                cropped_depth = self.config.IMAGE_CROP[key](depth) if key in self.config.IMAGE_CROP else depth


                target_shape = self.observation_space["images"][key].shape[:2][::-1]
                # print(target_shape)
                resized_rgb = cv2.resize(cropped_rgb, target_shape)
                resized_depth = cv2.resize(cropped_depth, target_shape).astype(np.uint16)
                cv2.imwrite("2.png", resized_rgb)
                images[key] = {
                    "rgb": resized_rgb[..., ::-1],  # BGR to RGB
                    "depth": resized_depth,
                }
                # 保持原有的RGB显示格式
                display_images[key] = resized_rgb
                display_images[key + "_full"] = cropped_rgb
                # 添加深度数据用于显示
                if self.show_depth:
                    display_images[key + "_depth"] = resized_depth

                full_res_images[key] = copy.deepcopy(cropped_rgb)  # Store the full resolution cropped image

            except queue.Empty:
                input(
                    f"{key} camera frozen. Check connect, then press enter to relaunch..."
                )
                cap.close()
                self.init_cameras(self.config.REALSENSE_CAMERAS)
                return self.get_im()

        # Store full resolution cropped images separately
        if self.save_video:
            self.recording_frames.append(full_res_images)

        if self.display_image:
            self.img_queue.put(display_images)
        return images

    def interpolate_move(self, goal: np.ndarray, timeout: float):
        """Move the robot to the goal position with linear interpolation."""
        if goal.shape == (6,):
            goal = np.concatenate([goal[:3], euler_2_quat(goal[3:])])
        steps = int(timeout * self.hz)
        self._update_currpos()
        path = np.linspace(self.currpos, goal, steps)
        for p in path:
            self._send_pos_command(p)
            time.sleep(1 / self.hz)
        self.nextpos = p
        self._update_currpos()

    def go_to_reset(self, joint_reset=False):
        """
        The concrete steps to perform reset should be
        implemented each subclass for the specific task.
        Should override this method if custom reset procedure is needed.
        """
        # Change to precision mode for reset        # Use compliance mode for coupled reset
        self._update_currpos()
        self._send_pos_command(self.currpos)
        time.sleep(0.3)
        # requests.post(self.url + "update_param", json=self.config.PRECISION_PARAM)

        # Convert COMPLIANCE_PARAM to JSON string
        compliance_param_json = json.dumps(self.config.PRECISION_PARAM)

        # Build the curl command
        curl_command = [
            "curl",
            "-X", "POST", 
            self.url + "update_param",  # URL to update parameter
            "-H", "Content-Type: application/json",  # Header
            "-d", compliance_param_json  # Data
        ]

        # Execute the curl command
        subprocess.run(curl_command)

        time.sleep(0.5)

        # Perform joint reset if needed
        if joint_reset:
            print("JOINT RESET")
            # requests.post(self.url + "jointreset")

            # 生成初始姿态数据
            init_pose = [
                0.5221814631974463,
                -0.07676048186555894,
                0.27573895856152086,
                3.137820383893128,
                -0.008951810369916657,
                -0.046907610068310124
            ]
            
            # 将初始姿态分为位置（前 3）和旋转（后 4）
            init_pos = init_pose[:3]
            init_rot_vec = init_pose[3:]
            
            # 使用旋转向量生成旋转矩阵，再转为四元数
            init_rot_matrix = R.from_rotvec(init_rot_vec).as_matrix()
            init_q = R.from_matrix(init_rot_matrix).as_quat()
            
            # 合并位置和四元数为一个列表，代表完整的初始位姿
            init_pose = init_pos + list(init_q)

            # 将数据转换为 JSON 格式
            pose_json = json.dumps({"arr": init_pose})

            # 构建 curl 命令发送到相应 URL
            curl_command = [
                "curl",
                "-X", "POST",
                "http://127.0.0.1:5000/pose",  # 目标 URL
                "-H", "Content-Type: application/json",
                "-d", pose_json  # 发送的数据
            ]

            # 执行 curl 命令
            result = subprocess.run(curl_command, capture_output=True, text=True)
            
            time.sleep(0.5)

        # Perform Carteasian reset
        if self.randomreset:  # randomize reset position in xy plane
            reset_pose = self.resetpos.copy()
            reset_pose[:2] += np.random.uniform(
                -self.random_xy_range, self.random_xy_range, (2,)
            )
            euler_random = self._RESET_POSE[3:].copy()
            euler_random[-1] += np.random.uniform(
                -self.random_rz_range, self.random_rz_range
            )
            reset_pose[3:] = euler_2_quat(euler_random)
            self.interpolate_move(reset_pose, timeout=1)
        else:
            reset_pose = self.resetpos.copy()
            self.interpolate_move(reset_pose, timeout=1)

        # Change to compliance mode
        # requests.post(self.url + "update_param", json=self.config.COMPLIANCE_PARAM)

        # Convert COMPLIANCE_PARAM to JSON string
        compliance_param_json = json.dumps(self.config.COMPLIANCE_PARAM)

        # Build the curl command
        curl_command = [
            "curl",
            "-X", "POST", 
            self.url + "update_param",  # URL to update parameter
            "-H", "Content-Type: application/json",  # Header
            "-d", compliance_param_json  # Data
        ]

        # Execute the curl command
        subprocess.run(curl_command)

    def reset(self, joint_reset=False, **kwargs):
        self.last_gripper_act = time.time()
        # requests.post(self.url + "update_param", json=self.config.COMPLIANCE_PARAM)

        # Convert COMPLIANCE_PARAM to JSON string
        compliance_param_json = json.dumps(self.config.COMPLIANCE_PARAM)

        # Build the curl command
        curl_command = [
            "curl",
            "-X", "POST", 
            self.url + "update_param",  # URL to update parameter
            "-H", "Content-Type: application/json",  # Header
            "-d", compliance_param_json  # Data
        ]

        # Execute the curl command
        subprocess.run(curl_command)

        if self.save_video:
            self.save_video_recording()

        self.cycle_count += 1
        if self.joint_reset_cycle!=0 and self.cycle_count % self.joint_reset_cycle == 0:
            self.cycle_count = 0
            joint_reset = True

        self._recover()
        self.go_to_reset(joint_reset=joint_reset)
        self._recover()
        self.curr_path_length = 0

        curl_command = [
                    "curl",
                    "-X", "POST", 
                    self.url + "open_gripper",  # URL to open gripper
                    "-H", "Content-Type: application/json"
                ]
        subprocess.run(curl_command)  # Execute the curl command

        self._update_currpos()
        obs = self._get_obs()
        self.terminate = False
        return obs, {"succeed": False}

    def save_video_recording(self):
        try:
            if len(self.recording_frames):
                if not os.path.exists('./videos'):
                    os.makedirs('./videos')
                
                timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
                
                for camera_key in self.recording_frames[0].keys():
                    if self.url == "http://127.0.0.1:5000/":
                        video_path = f'./videos/left_{camera_key}_{timestamp}.mp4'
                    else:
                        video_path = f'./videos/right_{camera_key}_{timestamp}.mp4'
                    
                    # Get the shape of the first frame for this camera
                    first_frame = self.recording_frames[0][camera_key]
                    height, width = first_frame.shape[:2]
                    
                    video_writer = cv2.VideoWriter(
                        video_path,
                        cv2.VideoWriter_fourcc(*"mp4v"),
                        10,
                        (width, height),
                    )
                    
                    for frame_dict in self.recording_frames:
                        video_writer.write(frame_dict[camera_key])
                    
                    video_writer.release()
                    print(f"Saved video for camera {camera_key} at {video_path}")
                
            self.recording_frames.clear()
        except Exception as e:
            print(f"Failed to save video: {e}")

    def init_cameras(self, name_serial_dict=None):
        """Init both wrist cameras."""
        if self.cap is not None:  # close cameras if they are already open
            self.close_cameras()

        self.cap = OrderedDict()
        for cam_name, kwargs in name_serial_dict.items():
            cap = VideoCapture(
                RSCapture(name=cam_name, **kwargs)
            )
            self.cap[cam_name] = cap

    def close_cameras(self):
        """Close both wrist cameras."""
        try:
            for cap in self.cap.values():
                cap.close()
        except Exception as e:
            print(f"Failed to close cameras: {e}")

    def _recover(self):
        """Internal function to recover the robot from error state."""
        # requests.post(self.url + "clearerr")
        # Build the curl command
        # curl_command = [
        #     "curl",
        #     "-X", "POST", 
        #     self.url + "clearerr",  # URL
        #     "-H", "Content-Type: application/json"  # Header
        # ]

        # # Execute the curl command
        # subprocess.run(curl_command)

    def _send_pos_command(self, pos: np.ndarray):
        """Internal function to send position command to the robot."""
        self._recover()
        arr = np.array(pos).astype(np.float32)
        data = {"arr": arr.tolist()}
        # requests.post(self.url + "pose", json=data)

        # Convert the data to a JSON string
        json_data = json.dumps(data)

        # Build the curl command
        curl_command = [
            "curl",
            "-X", "POST", 
            self.url + "pose",  # URL
            "-H", "Content-Type: application/json",  # Header
            "-d", json_data  # Data
        ]

        # Execute the curl command
        subprocess.run(curl_command)

    def _send_gripper_command(self, pos: float, mode="binary", force_open=False):
        """Internal function to send gripper command to the robot.

        Args:
            pos: gripper position command (-1 to 1)
            mode: control mode ("binary" or "continuous")
            force_open: if True, immediately execute open command regardless of current state
        """
        if mode == "binary":
            if (pos <= -0.5) and (self.curr_gripper_pos > 0.85) and (time.time() - self.last_gripper_act > self.gripper_sleep):  # close gripper
                pose_json = json.dumps({"gripper_pos": 0})
                curl_command = [
                    "curl",
                    "-X", "POST",
                    self.url + "move_gripper",  # URL to close gripper
                    "-H", "Content-Type: application/json",
                    "-d", pose_json
                ]
                subprocess.run(curl_command)  # Execute the curl command
                self.last_gripper_act = time.time()
                time.sleep(self.gripper_sleep)
            elif (pos >= 0.5) and ((self.curr_gripper_pos < 0.85 and time.time() - self.last_gripper_act > self.gripper_sleep) or force_open):  # open gripper
                pose_json = json.dumps({"gripper_pos": 255})
                curl_command = [
                    "curl",
                    "-X", "POST",
                    self.url + "move_gripper",  # URL to open gripper
                    "-H", "Content-Type: application/json",
                    "-d", pose_json
                ]
                subprocess.run(curl_command)  # Execute the curl command
                self.last_gripper_act = time.time()
                time.sleep(self.gripper_sleep)
            else:
                return
        elif mode == "continuous":
            raise NotImplementedError("Continuous gripper control is optional")

    def force_open_gripper(self):
        """Force open the gripper immediately, bypassing all state checks."""
        print("Force opening gripper...")
        pose_json = json.dumps({"gripper_pos": 255})
        curl_command = [
            "curl",
            "-X", "POST",
            self.url + "move_gripper",  # URL to open gripper
            "-H", "Content-Type: application/json",
            "-d", pose_json
        ]
        subprocess.run(curl_command)  # Execute the curl command
        self.last_gripper_act = time.time()
        print("Gripper force opened")

    def _update_currpos(self):
        """
        Internal function to get the latest state of the robot and its gripper.
        """
        # ps = requests.post(self.url + "getstate").json()
        
        # Build the curl command
        curl_command = [
            "curl",
            "-X", "POST", 
            self.url + "getstate",  # URL to get robot state
            "-H", "Content-Type: application/json",  # Header
            "-s"  # Suppress curl output
        ]

        # Execute the curl command and capture the response
        result = subprocess.run(curl_command, capture_output=True, text=True)
        
        if result.returncode != 0:
            raise Exception("Failed to get robot state. Curl command failed.")
        
        # Parse the JSON response
        ps = json.loads(result.stdout)

        self.currpos = np.array(ps["pose"])
        self.currvel = np.array(ps["vel"])

        self.currforce = np.array(ps["force"])
        self.currtorque = np.array(ps["torque"])
        self.currjacobian = np.reshape(np.array(ps["jacobian"]), (6, 7))

        self.q = np.array(ps["q"])
        self.dq = np.array(ps["dq"])

        self.curr_gripper_pos = np.array(ps["gripper_pos"])

    def update_currpos(self):
        """
        Internal function to get the latest state of the robot and its gripper.
        """
        # ps = requests.post(self.url + "getstate").json()

        # Build the curl command
        curl_command = [
            "curl",
            "-X", "POST", 
            self.url + "getstate",  # URL to get robot state
            "-H", "Content-Type: application/json",  # Header
            "-s"  # Suppress curl output
        ]

        # Execute the curl command and capture the response
        result = subprocess.run(curl_command, capture_output=True, text=True)
        
        if result.returncode != 0:
            raise Exception("Failed to get robot state. Curl command failed.")
        
        # Parse the JSON response
        ps = json.loads(result.stdout)

        self.currpos = np.array(ps["pose"])
        self.currvel = np.array(ps["vel"])

        self.currforce = np.array(ps["force"])
        self.currtorque = np.array(ps["torque"])
        self.currjacobian = np.reshape(np.array(ps["jacobian"]), (6, 7))

        self.q = np.array(ps["q"])
        self.dq = np.array(ps["dq"])

        self.curr_gripper_pos = np.array(ps["gripper_pos"])

    def _get_obs(self) -> dict:
        images = self.get_im()
        state_observation = {
            "tcp_pose": self.currpos,
            "tcp_vel": self.currvel,
            "gripper_pose": self.curr_gripper_pos,
            "tcp_force": self.currforce,
            "tcp_torque": self.currtorque,
        }
        return copy.deepcopy(dict(images=images, state=state_observation))

    def close(self):
        if hasattr(self, 'listener'):
            self.listener.stop()
        self.close_cameras()
        if self.display_image:
            self.img_queue.put(None)
            cv2.destroyAllWindows()
            self.displayer.join()