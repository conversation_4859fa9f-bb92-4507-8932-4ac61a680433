import requests
import math
from scipy.spatial.transform import Rotation as R
import subprocess
import json

# pose = [0.5557, -0.0561, 0.3830, 0.707, 0, 0.707, 0]  # 你计算好的pose

pose =    [
            0.35336482061068414,
            -0.12505003541645793,
            0.4593388126957117,
            -2.9665855995137393,
            0.005969393927833814,
            -0.7219435243583884
          ]
pose =  [
          -0.22349100045598674,
          0.00048727324082151625,
          0.2855305594648454,
          -2.997961972888163,
          -0.0017933009312772936,
          -0.7273218068328153
        ]

pose = [
          -0.21102168777324495,
          0.011878555846269512,
          0.26647537514908154,
          3.0136312146787563,
          0.08332187925853062,
          0.7495653050334977
        ]

pose = [
          -0.23499646765121543,
          -0.03167573321810517,
          0.21857045016560156,
          2.886363701676302,
          -0.007352604436934235,
          0.9894708852048758
        ]

pose = [
          -0.13038026218612914,
          -0.005500388631138347,
          0.35421811304744853,
          -3.104091228461844,
          -0.0567368301382074,
          -0.37284867831090857
        ]

pose =[
          -0.16026240321778146,
          -0.028480838378833542,
          0.34278250986939957,
          3.082694883467537,
          0.08501870950857997,
          0.5203543709734055
        ]

pose = [
          -0.16467027358762049,
          0.02482335277780521,
          0.3685103535869013,
          3.0765798144160352,
          -0.03708867268359584,
          0.33826382078044137
        ]

pose = [                                #table rearranage
          -0.2015432981916685,
          0.0010594182523531778,
          0.2504187812527446,
          2.9121075229951554,
          -0.03278764208448607,
          0.9366558255158614
        ]

pose = [
          -0.19417694987129963,
          -0.025367372471992118,
          0.28441228900149684,
          3.0390789522742967,
          -0.008377613892987374,
          0.6507901997175756
        ]
pose = [                              ##pour water
          -0.22873216168049926,
          -0.00607768095235469,
          0.29716733866473577,
          -2.9713358174625197,
          0.010009768056643081,
          -0.8802126491274674
        ]

init_pose = [
              0.5221814631974463,
              -0.07676048186555894,
              0.27573895856152086,
              3.137820383893128,
              -0.008951810369916657,
              -0.046907610068310124
            ]
pos = pose[:3]
# pos[0] += 0.5717120673331927
pos[0] += 0.4972006795565986
pos[1] += -0.05511538188320742
pos[2] += 0.39082325668663646 - 0.09790731

# pos[2] = 0.30
rot_vec = pose[3:]
rot_matrix = R.from_rotvec(rot_vec).as_matrix()
q = R.from_matrix(rot_matrix).as_quat()
pose = pos + list(q)

init_pos = init_pose[:3]
init_pos[0] += 0
init_rot_vec = init_pose[3:]
init_rot_matrix = R.from_rotvec(init_rot_vec).as_matrix()
init_q = R.from_matrix(init_rot_matrix).as_quat()
init_pose = init_pos + list(init_q)
# init_pose[2] += 0.1


pose_json = json.dumps({"gripper_pos": 255})
# pose_json = json.dumps({"arr": init_pose})
cmd = [
    "curl",
    "-X", "POST",
    "http://127.0.0.1:5000/move_gripper",
    "-H", "Content-Type: application/json",
    "-d", pose_json
]
result = subprocess.run(cmd, capture_output=True, text=True)
print(pose_json)
print(result.stdout)
