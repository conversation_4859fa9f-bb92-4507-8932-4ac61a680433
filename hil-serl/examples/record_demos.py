import os
from tqdm import tqdm
import numpy as np
import copy
import pickle as pkl
import datetime
from absl import app, flags
import time

from experiments.mappings import CONFIG_MAPPING

FLAGS = flags.FLAGS
flags.DEFINE_string("exp_name", None, "Name of experiment corresponding to folder.")
flags.DEFINE_integer("successes_needed", 20, "Number of successful demos to collect.")
flags.DEFINE_string("demo_save_dir", "./demo_data", "Directory to save collected demos.")


from pynput import keyboard

user_confirmed_success = False

def on_press(key):
        global user_confirmed_success
        if key == keyboard.Key.f2:   # 比如按F2标记成功
            user_confirmed_success = True

listener = keyboard.Listener(on_press=on_press)
listener.start()


def main(_):
    global user_confirmed_success

    assert FLAGS.exp_name in CONFIG_MAPPING, 'Experiment folder not found.'
    config = CONFIG_MAPPING[FLAGS.exp_name]()
    env = config.get_environment(fake_env=False, save_video=True, classifier=True)
    
    obs, info = env.reset()
    print("Reset done")
    transitions = []
    success_count = 0
    success_needed = FLAGS.successes_needed
    pbar = tqdm(total=success_needed)
    trajectory = []
    returns = 0
    
    while success_count < success_needed:
        actions = np.zeros(env.action_space.sample().shape) 
        next_obs, rew, done, truncated, info = env.step(actions)
        returns += rew
        if "intervene_action" in info:
            actions = info["intervene_action"]
        transition = copy.deepcopy(
            dict(
                observations=obs,
                actions=actions,
                next_observations=next_obs,
                # rewards=rew,
                # masks=1.0 - done,
                # dones=done,
                infos=info,
            )
        )
        trajectory.append(transition)
        
        pbar.set_description(f"Return: {returns}")

        obs = next_obs
        # if done:
        #     if info["succeed"]:
        #         for transition in trajectory:
        #             transitions.append(copy.deepcopy(transition))
        #         success_count += 1
        #         pbar.update(1)
        #     trajectory = []
        #     returns = 0
        #     obs, info = env.reset()
        if user_confirmed_success:
            for transition in trajectory:
                    transitions.append(copy.deepcopy(transition))
            success_count += 1
            pbar.update(1)
            print(f"Saved demo #{success_count}")
            user_confirmed_success = False  # 重置状态
            trajectory = []
            returns = 0
            obs, info = env.reset()
            input("press ENTER to go on......")

            
    if not os.path.exists("./demo_data"):
        os.makedirs("./demo_data")
    uuid = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    file_name = os.path.join(FLAGS.demo_save_dir, f"{FLAGS.exp_name}_{success_needed}_demos_{uuid}.pkl") 
    with open(file_name, "wb") as f:
        pkl.dump(transitions, f)
        print(f"saved {success_needed} demos to {file_name}")
    env.expert.close()

if __name__ == "__main__":
    app.run(main)