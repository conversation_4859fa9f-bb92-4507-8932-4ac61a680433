import os
import time
from absl import app, flags
from experiments.mappings import CONFIG_MAPPING  # 确保这个导入路径正确

FLAGS = flags.FLAGS
flags.DEFINE_string("exp_name", "ram_insertion", "Name of experiment corresponding to folder.")
flags.DEFINE_integer("successes_needed", 20, "Number of successful demos to collect.")
flags.DEFINE_string("demo_save_dir", "./demo_data", "Directory to save collected demos.")
def main(_):
    # 1. 验证实验名称
    assert FLAGS.exp_name in CONFIG_MAPPING, 'Experiment folder not found.'
    
    # 2. 创建环境
    config = CONFIG_MAPPING[FLAGS.exp_name]()
    env = config.get_environment(fake_env=False, save_video=False, classifier=False)
    
    # 3. 归位操作
    def reset_arm():
        print("Resetting arm to home position...")
        start_time = time.time()
        obs, info = env.reset()
        elapsed = time.time() - start_time
        print(f"Reset completed in {elapsed:.2f} seconds")
        print(f"Current state: {info.get('state_description', 'No state info')}")
        return True
    
    # 4. 执行归位
    reset_arm()
    
    # 5. 清理资源
    if hasattr(env, 'expert'):
        env.expert.close()
    print("Arm homing complete")

if __name__ == "__main__":
    app.run(main)
