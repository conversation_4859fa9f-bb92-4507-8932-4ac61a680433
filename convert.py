import pickle
import numpy as np
import torch
from pathlib import Path
from utils.utils_with_rlbench import (
    RLBenchEnv,
    keypoint_discovery,
    obs_to_attn,
    transform,
)
import einops
import argparse
import torch.nn.functional as F
from scipy.spatial.transform import Rotation
import os
import time
import cv2

# 真正的state的构成是：gripper_pose(0),tcp_force(1,2,3),tcp_pose(4,5,6,7,8,9),tcp_torque(10,11,12),tcp_vel(13,14,15,16,17,18)


def crop_and_resize_image(image, target_size=(256, 256), debug=False):
    """
    将424x240的图像先裁剪成正方形，再缩放到目标尺寸

    Args:
        image (np.ndarray): 输入图像，形状为 (H, W) 或 (H, W, C)
        target_size (tuple): 目标尺寸 (height, width)
        debug (bool): 是否输出调试信息

    Returns:
        np.ndarray: 处理后的图像
        dict: 包含裁剪和缩放信息的字典
    """
    original_shape = image.shape
    H, W = original_shape[:2]

    if debug:
        print(f"原始图像尺寸: {H}x{W}")

    # 计算裁剪参数以获得正方形
    if H < W:
        # 高度较小，以高度为准裁剪宽度
        crop_size = H
        crop_left = (W - H) // 2
        crop_top = 0
        crop_right = crop_left + H
        crop_bottom = H
    else:
        # 宽度较小，以宽度为准裁剪高度
        crop_size = W
        crop_left = 0
        crop_top = (H - W) // 2
        crop_right = W
        crop_bottom = crop_top + W

    # 执行裁剪
    if len(original_shape) == 3:  # RGB图像
        cropped_image = image[crop_top:crop_bottom, crop_left:crop_right, :]
    else:  # 深度图
        cropped_image = image[crop_top:crop_bottom, crop_left:crop_right]

    # 缩放到目标尺寸
    if len(original_shape) == 3:  # RGB图像
        resized_image = cv2.resize(cropped_image, target_size, interpolation=cv2.INTER_LINEAR)
    else:  # 深度图，使用最近邻插值避免深度值被平滑
        resized_image = cv2.resize(cropped_image, target_size, interpolation=cv2.INTER_NEAREST)

    # 返回处理信息
    transform_info = {
        'original_size': (H, W),
        'crop_region': (crop_top, crop_bottom, crop_left, crop_right),
        'crop_size': crop_size,
        'target_size': target_size,
        'scale_factor': target_size[0] / crop_size  # 假设target_size是正方形
    }

    if debug:
        print(f"裁剪区域: [{crop_top}:{crop_bottom}, {crop_left}:{crop_right}] -> {crop_size}x{crop_size}")
        print(f"缩放到: {target_size[0]}x{target_size[1]}")
        print(f"缩放比例: {transform_info['scale_factor']:.4f}")

    return resized_image, transform_info


def crop_and_resize_pointcloud(pointcloud, transform_info, debug=False):
    """
    根据图像变换信息对点云进行相应的裁剪和缩放

    Args:
        pointcloud (np.ndarray): 原始点云，形状为 (H, W, 3)
        transform_info (dict): 从crop_and_resize_image返回的变换信息
        debug (bool): 是否输出调试信息

    Returns:
        np.ndarray: 处理后的点云，形状为 (target_H, target_W, 3)
    """
    crop_top, crop_bottom, crop_left, crop_right = transform_info['crop_region']
    target_size = transform_info['target_size']

    # 裁剪点云（与图像裁剪保持一致）
    cropped_pointcloud = pointcloud[crop_top:crop_bottom, crop_left:crop_right, :]

    # 对点云进行缩放
    # 注意：这里我们使用最近邻插值来避免点云坐标被平滑
    resized_pointcloud = np.zeros((target_size[0], target_size[1], 3), dtype=pointcloud.dtype)

    for i in range(3):  # 对每个坐标轴分别处理
        resized_pointcloud[:, :, i] = cv2.resize(
            cropped_pointcloud[:, :, i],
            target_size,
            interpolation=cv2.INTER_NEAREST
        )

    if debug:
        print(f"点云裁剪: {pointcloud.shape} -> {cropped_pointcloud.shape} -> {resized_pointcloud.shape}")

    return resized_pointcloud


def convert_action_to_8d(action, state=None, next_state=None, auto_scale=[0.01,0.06,1.0]):
    """
    将7维动作(Δx,Δy,Δz,roll,pitch,yaw,gripper)转换为8维绝对动作(x,y,z,qx,qy,qz,qw,gripper)
    参数:
        action: np.ndarray (7,), 原始动作指令
        state: np.ndarray (19,), 当前状态(用于计算绝对位姿)
        next_state: np.ndarray (19,), 下一帧状态(用于获取实际gripper状态)
    返回:
        np.ndarray (8,)
    """
    assert action.shape == (7,)

    action = np.clip(action, -1, 1)

    current_pos = state[4:7]  # [x,y,z]
    abs_pos = current_pos+action[:3]*auto_scale[0]  # 保持相对

    # 2. 欧拉角转四元数
    euler_angles = action[3:6]  # roll, pitch, yaw
    current_rot = state[7:10]  # roll, pitch, yaw
    # current转四元数
    current_quat = Rotation.from_euler('xyz', current_rot).as_quat()
    quat= (
            Rotation.from_euler("xyz", euler_angles * auto_scale[1])
            * Rotation.from_quat(current_quat)
    ).as_quat()

    # 3. 处理夹爪状态 - 使用下一帧的实际gripper状态
    if next_state is not None:
        # 使用下一帧state中的实际gripper状态 (state[0]是gripper_pose，范围[0,1])
        gripper = next_state[0]  # 0=闭合，1=打开
        gripper = np.clip(gripper, 0, 1)  # 确保在[0,1]范围内
    else:
        print("next_state is None")
        # 回退到原来的逻辑（用于兼容性）
        gripper = action[6]
        # 范围是[-1,1]，调整为[0,1]，0表示闭合，1表示张开，0.5不动。
        gripper = (gripper + 1) / 2.0
        gripper = np.clip(gripper, 0, 1)

    return np.concatenate([abs_pos, quat, [gripper]])
    
def obs_to_attn_from_transition(gripper_pose, extrinsics=None, intrinsics=None, target_size=(256, 256), original_size=(240, 424)):
    """
    计算gripper在缩放后图像中的像素坐标

    Args:
        gripper_pose: gripper的世界坐标
        extrinsics: 外参矩阵（未使用）
        intrinsics: 相机内参，如果为None则使用默认值
        target_size: 目标图像尺寸 (height, width)
        original_size: 原始图像尺寸 (height, width)

    Returns:
        tuple: (u, v) 在缩放后图像中的像素坐标
    """
    # 原始相机内参（对应424x240图像）
    if intrinsics is None:
        original_intrinsics = {
            'fx': 216.809,
            'fy': 216.809,
            'cx': 212.494,
            'cy': 118.398
        }
    else:
        original_intrinsics = intrinsics.copy()

    # 计算图像变换参数
    H_orig, W_orig = original_size
    H_target, W_target = target_size

    # 计算裁剪参数（裁剪成正方形）
    if H_orig < W_orig:
        # 高度较小，以高度为准裁剪宽度
        crop_size = H_orig
        crop_left = (W_orig - H_orig) // 2
        crop_top = 0
    else:
        # 宽度较小，以宽度为准裁剪高度
        crop_size = W_orig
        crop_left = 0
        crop_top = (H_orig - W_orig) // 2

    # 调整内参以适应图像变换
    # 步骤1: 调整内参以适应裁剪
    cropped_intrinsics = {
        'fx': original_intrinsics['fx'],
        'fy': original_intrinsics['fy'],
        'cx': original_intrinsics['cx'] - crop_left,
        'cy': original_intrinsics['cy'] - crop_top
    }

    # 步骤2: 调整内参以适应缩放
    scale_factor = H_target / crop_size  # 假设target_size是正方形

    adjusted_intrinsics = {
        'fx': cropped_intrinsics['fx'] * scale_factor,
        'fy': cropped_intrinsics['fy'] * scale_factor,
        'cx': cropped_intrinsics['cx'] * scale_factor,
        'cy': cropped_intrinsics['cy'] * scale_factor
    }

    K = torch.tensor([
        [adjusted_intrinsics['fx'], 0, adjusted_intrinsics['cx'], 0],
        [0, adjusted_intrinsics['fy'], adjusted_intrinsics['cy'], 0],
        [0, 0, 1, 0]
    ])  # shape (3, 4)

    cam_T_world = torch.tensor([
        [2.039413322245856031e-02, 9.997689532984076477e-01, 6.791122934426185864e-03, 2.849851955899636649e-02],
        [3.398053113376858780e-01, -5.431387454349623223e-04, -9.404956434705059598e-01, -1.249019213795134475e-01],
        [-9.402746565322296357e-01, 2.148825309114454907e-02, -3.397378772852505113e-01, 1.223038606263365580e+00],
        [0.0, 0.0, 0.0, 1.0]
    ], dtype=torch.float32)

    # gripper 世界系坐标 → 相机像素坐标
    gripper_pos_3 = gripper_pose[:3].clone().detach().float()

    gripper_pos_4 = F.pad(gripper_pos_3, (0, 1), value=1).unsqueeze(1)  # (4,1)
    gripper_cam = cam_T_world @ gripper_pos_4  # (4,1)

    uvw = K @ gripper_cam  # shape (3,1)
    u = int((uvw[0] / uvw[2]).round().item())
    v = int((uvw[1] / uvw[2]).round().item())

    return u, v



def keypoint_discovery_from_transitions(transitions, 
                                     pos_threshold=0.05, # pos_threshold=0.005,  # 位移阈值5mm
                                     rot_threshold=0.52): # 旋转阈值30度
                                     # rot_threshold=0.087): # 旋转阈值5度（0.087弧度）
    """
    改进版关键帧检测：
    1. 排除全零动作帧
    2. 检测有效位移/旋转变化
    3. 保留夹爪状态变化帧
    """

    '''
    xjd: 第一个有动作帧是否应该强制设为关键帧？或者应该有更合理的选取？一个文件包含多个epidode，应该保证每个episode拥有这种处理，否则就分开处理。
    有没有办法确定分割位置？
    最后一个有动作的帧是否应该设为关键帧？（包括夹抓动作）
    '''
    episode_keypoints = []
    prev_valid_pos = transitions[0]["observations"]["state"][0, 4:7]
    prev_valid_rot = transitions[0]['actions'][3:6]  # 欧拉角
    stopped_buffer = 0

    for i in range(1, len(transitions)):
        obs = transitions[i]["observations"]
        action = transitions[i]["actions"]
        current_pos = obs["state"][0, 4:7]
        current_rot = action[3:6]
        current_gripper = action[6]

        # 条件1：排除全零动作（包括夹爪）
        is_zero_action = np.all(np.abs(action[:6]) < 1e-6)  # 忽略夹爪状态

        # 条件2：计算位移变化
        pos_delta = np.linalg.norm(current_pos - prev_valid_pos)
        pos_moved = (pos_delta > pos_threshold)

        # 条件3：计算旋转变化（欧拉角差值）
        rot_delta = np.linalg.norm(current_rot - prev_valid_rot)
        rot_moved = (rot_delta > rot_threshold)

        # 条件4：夹爪动作指令 - 判断是否有gripper动作（非保持状态）
        gripper_changed = abs(current_gripper) > 0.8  # 大于0.15表示有明确的开/关指令，而不是保持不变

        # 关键帧条件：非零动作且（有移动或夹爪变化）
        # if (not is_zero_action) and (pos_moved or rot_moved or gripper_changed): # 为啥这么设计啊
        # if ((not is_zero_action and (pos_moved or rot_moved)) or gripper_changed):
        if not is_zero_action or gripper_changed: # 只要求非零动作即可
            episode_keypoints.append(i)
            prev_valid_pos = current_pos
            prev_valid_rot = current_rot
            stopped_buffer = 0
        else:
            stopped_buffer += 1

    return _filter_adjacent_keyframes(episode_keypoints, transitions)



def _filter_adjacent_keyframes(keypoints, transitions, min_interval=3):
    """
    修改后的关键帧过滤：
    1. 每三帧取一帧（不考虑变化幅度）
    2. 如果一帧在3帧间隔之内，且夹抓出现变化，则也被保留
    """
    if not keypoints:
        return []

    filtered = []
    print(f"原始关键帧数量: {len(keypoints)}")

    # 第一帧总是保留
    filtered.append(keypoints[0])
    last_kept_idx = keypoints[0]

    for i, idx in enumerate(keypoints[1:], 1):
        # 检查与上一个保留帧的间隔
        interval = idx - last_kept_idx

        if interval >= min_interval:
            # 间隔足够，直接保留
            filtered.append(idx)
            last_kept_idx = idx
        else:
            # 间隔不足，检查是否有夹抓动作指令
            curr_gripper = transitions[idx]["actions"][6]

            if abs(curr_gripper) > 0.8:
                # 有明确的gripper动作指令，保留此帧
                filtered.append(idx)
                last_kept_idx = idx
            # 否则跳过此帧（不保留）

    print(f"过滤后关键帧数量: {len(filtered)}")
    return filtered

def save_pointcloud_as_ply(xyz, depth, debug=False, save_dir="pointclouds"):
    """
    保存点云为PLY格式文件
    Args:
        xyz (np.ndarray): (H, W, 3) 世界坐标系下的点云
        depth (np.ndarray): (H, W) 原始深度图
        debug (bool): 是否输出调试信息
        save_dir (str): 保存目录
    """
    try:
        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)

        # 生成文件名（使用时间戳）
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"pointcloud_{timestamp}.ply"
        filepath = os.path.join(save_dir, filename)

        # 获取有效点云（深度值大于0的点）
        valid_mask = depth > 0
        valid_xyz = xyz[valid_mask]

        if len(valid_xyz) == 0:
            if debug:
                print("⚠️ 没有有效的点云数据，跳过保存")
            return

        # 为点云分配颜色（基于Z坐标）
        z_values = valid_xyz[:, 2]
        z_min, z_max = z_values.min(), z_values.max()
        if z_max > z_min:
            # 将Z值映射到[0,1]范围，然后转换为RGB
            z_normalized = (z_values - z_min) / (z_max - z_min)
            colors = np.zeros((len(valid_xyz), 3))
            colors[:, 0] = z_normalized  # R通道
            colors[:, 1] = 1.0 - z_normalized  # G通道
            colors[:, 2] = 0.5  # B通道固定值
        else:
            # 如果所有点的Z值相同，使用固定颜色
            colors = np.full((len(valid_xyz), 3), [0.5, 0.5, 0.5])

        # 将颜色值转换到[0,255]范围
        colors = (colors * 255).astype(np.uint8)

        # 写入PLY文件
        with open(filepath, 'w') as f:
            # PLY文件头
            f.write("ply\n")
            f.write("format ascii 1.0\n")
            f.write(f"element vertex {len(valid_xyz)}\n")
            f.write("property float x\n")
            f.write("property float y\n")
            f.write("property float z\n")
            f.write("property uchar red\n")
            f.write("property uchar green\n")
            f.write("property uchar blue\n")
            f.write("end_header\n")

            # 写入点云数据
            for i in range(len(valid_xyz)):
                x, y, z = valid_xyz[i]
                r, g, b = colors[i]
                f.write(f"{x:.6f} {y:.6f} {z:.6f} {r} {g} {b}\n")

        if debug:
            print(f"✅ 点云已保存到: {filepath}")
            print(f"   有效点数: {len(valid_xyz)}")
            print(f"   坐标范围: X[{valid_xyz[:, 0].min():.3f}, {valid_xyz[:, 0].max():.3f}]")
            print(f"            Y[{valid_xyz[:, 1].min():.3f}, {valid_xyz[:, 1].max():.3f}]")
            print(f"            Z[{valid_xyz[:, 2].min():.3f}, {valid_xyz[:, 2].max():.3f}]")

    except Exception as e:
        if debug:
            print(f"❌ 保存点云时出错: {e}")

def depth_to_xyz(depth, debug=False, validate_params=True, auto_scale_intrinsics=True, auto_scale_depth=True):
    """
    将深度图转换为世界坐标系下的点云。

    Args:
        depth (np.ndarray): (H, W) 深度图，可能需要缩放到米单位
        debug (bool): 是否输出调试信息
        validate_params (bool): 是否验证参数合理性
        auto_scale_intrinsics (bool): 是否自动缩放内参以匹配图像尺寸
        auto_scale_depth (bool): 是否自动缩放深度值到合理范围（默认True）
    Returns:
        pointcloud (np.ndarray): (H, W, 3) 世界系下的 XYZ 点云

    注意：
        - 使用标准的计算机视觉坐标系约定
        - 相机坐标系：X向右，Y向下，Z向前
        - 与obs_to_attn_from_transition函数保持一致
        - 深度值自动缩放：处理16位深度图、毫米单位等情况
    """
    # ==================== 深度值预处理和缩放 ====================
    # 问题背景：
    # 1. 有些深度图是16位整数格式，值范围可能是0-65535
    # 2. 有些深度图单位是毫米而不是米
    # 3. 有些深度图值异常大，导致世界坐标超出合理范围
    #
    # 解决方案：自动检测深度值范围并进行适当缩放
    depth_processed = depth.copy().astype(np.float32)

    if auto_scale_depth:
        # 获取有效深度值（大于0的像素）
        valid_depth_mask = depth_processed > 0

        if np.any(valid_depth_mask):
            depth_min = depth_processed[valid_depth_mask].min()
            depth_max = depth_processed[valid_depth_mask].max()

            if debug:
                print(f"原始深度值范围: [{depth_min:.4f}, {depth_max:.4f}]")

            # 深度值缩放策略：
            # - 如果最大深度 > 1000：可能是毫米单位，除以1000转为米
            # - 如果最大深度 > 10：可能是16位深度图，线性缩放到0-2米范围
            # - 如果最大深度 <= 10：认为已经是米单位，不缩放

            if depth_max > 1000:
                depth_processed = depth_processed / 1000.0
                if debug:
                    print("✓ 检测到毫米单位深度，转换为米 (除以1000)")

            # 重新计算缩放后的深度范围
            valid_depth_mask = depth_processed > 0
            if np.any(valid_depth_mask):
                new_min = depth_processed[valid_depth_mask].min()
                new_max = depth_processed[valid_depth_mask].max()
                if debug:
                    print(f"缩放后深度值范围: [{new_min:.4f}, {new_max:.4f}] 米")

        # 最终安全检查：限制深度范围到物理合理区间
        # - 最小深度：0.05米 (5厘米，避免过近的噪声点)
        # - 最大深度：3.0米 (机器人工作空间的合理上限)
        depth_processed = np.clip(depth_processed, 0.0, 2.5)
        depth_processed[depth_processed < 0.05] = 0.0  # 过近的点设为无效

        if debug and np.any(depth_processed > 0):
            final_min = depth_processed[depth_processed > 0].min()
            final_max = depth_processed[depth_processed > 0].max()
            print(f"最终深度值范围: [{final_min:.4f}, {final_max:.4f}] 米")

    # ==================== 相机内参设置 ====================
    # 原始相机内参（标定于424x240图像）
    intrinsics = {
        'fx': 216.809,
        'fy': 216.809,
        'cx': 212.494,
        'cy': 118.398
    }

    # # 图像处理流程：1280x720 → 裁剪[0:720, 200:1080] → 缩放到目标尺寸
    # original_width, original_height = 1280, 720
    # crop_left, crop_top = 200, 0
    # crop_width, crop_height = 880, 720  # 1080-200=880

    H, W = depth_processed.shape

    # # 根据实际的图像处理流程调整内参
    # if auto_scale_intrinsics:
    #     # 步骤1: 调整内参以适应裁剪
    #     # 裁剪后，主点坐标需要减去裁剪的偏移量
    #     cropped_intrinsics = {
    #         'fx': original_intrinsics['fx'],
    #         'fy': original_intrinsics['fy'],
    #         'cx': original_intrinsics['cx'] - crop_left,  # 635.578 - 200 = 435.578
    #         'cy': original_intrinsics['cy'] - crop_top    # 400.158 - 0 = 400.158
    #     }
    #
    #     # 步骤2: 调整内参以适应缩放
    #     # 从裁剪后的880x720缩放到当前的WxH
    #     scale_x = W / crop_width   # W / 880
    #     scale_y = H / crop_height  # H / 720
    #
    #     intrinsics = {
    #         'fx': cropped_intrinsics['fx'] * scale_x,
    #         'fy': cropped_intrinsics['fy'] * scale_y,
    #         'cx': cropped_intrinsics['cx'] * scale_x,
    #         'cy': cropped_intrinsics['cy'] * scale_y
    #     }

    #     if debug:
    #         print(f"内参调整流程:")
    #         print(f"  原始图像: {original_width}x{original_height}")
    #         print(f"  裁剪区域: [{crop_top}:{crop_top+crop_height}, {crop_left}:{crop_left+crop_width}] → {crop_width}x{crop_height}")
    #         print(f"  最终尺寸: {W}x{H}")
    #         print(f"  缩放比例: X={scale_x:.4f}, Y={scale_y:.4f}")
    #         print(f"  原始内参: fx={original_intrinsics['fx']:.2f}, fy={original_intrinsics['fy']:.2f}")
    #         print(f"            cx={original_intrinsics['cx']:.2f}, cy={original_intrinsics['cy']:.2f}")
    #         print(f"  裁剪后:   cx={cropped_intrinsics['cx']:.2f}, cy={cropped_intrinsics['cy']:.2f}")
    #         print(f"  最终内参: fx={intrinsics['fx']:.2f}, fy={intrinsics['fy']:.2f}")
    #         print(f"            cx={intrinsics['cx']:.2f}, cy={intrinsics['cy']:.2f}")
    # else:
    #     intrinsics = original_intrinsics.copy()

    # 验证内参与图像尺寸的合理性
    if validate_params:
        if intrinsics['cx'] > W or intrinsics['cy'] > H:
            print(f"⚠️ 警告: 主点坐标 ({intrinsics['cx']:.1f}, {intrinsics['cy']:.1f}) 超出图像尺寸 ({W}, {H})")

        if intrinsics['cx'] < W/4 or intrinsics['cx'] > 3*W/4:
            print(f"⚠️ 警告: 主点X坐标 {intrinsics['cx']:.1f} 偏离图像中心 {W/2:.1f} 较远")

        if intrinsics['cy'] < H/4 or intrinsics['cy'] > 3*H/4:
            print(f"⚠️ 警告: 主点Y坐标 {intrinsics['cy']:.1f} 偏离图像中心 {H/2:.1f} 较远")

    K = np.array([
        [intrinsics['fx'], 0, intrinsics['cx']],
        [0, intrinsics['fy'], intrinsics['cy']],
        [0, 0, 1]
    ])

    # 使用与obs_to_attn_from_transition函数相同的高精度变换矩阵
    T_world2cam = np.array([
        [2.039413322245856031e-02, 9.997689532984076477e-01, 6.791122934426185864e-03, 2.849851955899636649e-02],
        [3.398053113376858780e-01, -5.431387454349623223e-04, -9.404956434705059598e-01, -1.249019213795134475e-01],
        [-9.402746565322296357e-01, 2.148825309114454907e-02, -3.397378772852505113e-01, 1.223038606263365580e+00],
        [0.0, 0.0, 0.0, 1.0]
    ])
    T_cam2world = np.linalg.inv(T_world2cam)

    # 验证变换矩阵的正交性
    if validate_params:
        R = T_world2cam[:3, :3]
        det_R = np.linalg.det(R)
        if abs(det_R - 1.0) > 0.01:
            print(f"⚠️ 警告: 旋转矩阵行列式 {det_R:.6f} 偏离1.0，可能不是正交矩阵")

    u, v = np.meshgrid(np.arange(W), np.arange(H), indexing='xy')

    # ==================== 深度图转点云计算 ====================
    # 检查处理后深度值的有效性
    if debug:
        print(f"图像尺寸: {H}x{W}")
        print(f"处理后深度值范围: {depth_processed.min():.4f} ~ {depth_processed.max():.4f} 米")
        print(f"有效深度像素: {np.sum(depth_processed > 0)} / {depth_processed.size} ({100*np.sum(depth_processed > 0)/depth_processed.size:.1f}%)")
        print(f"异常值: NaN={np.sum(np.isnan(depth_processed))}, Inf={np.sum(np.isinf(depth_processed))}")

    # 深度图转相机坐标系（标准针孔相机模型）
    # 使用处理后的深度值进行计算
    z = depth_processed
    x = (u - K[0, 2]) * z / K[0, 0]
    y = (v - K[1, 2]) * z / K[1, 1]

    # 检查相机坐标系的有效性
    if debug:
        valid_mask = depth_processed > 0
        if np.sum(valid_mask) > 0:
            print(f"相机坐标系范围:")
            print(f"  X: {x[valid_mask].min():.4f} ~ {x[valid_mask].max():.4f} 米")
            print(f"  Y: {y[valid_mask].min():.4f} ~ {y[valid_mask].max():.4f} 米")
            print(f"  Z: {z[valid_mask].min():.4f} ~ {z[valid_mask].max():.4f} 米")

    pcd_camera = np.stack((x, y, z), axis=-1)  # (H, W, 3)

    # 转换为齐次坐标
    ones = np.ones_like(z)[..., None]
    pcd_camera_homo = np.concatenate([pcd_camera, ones], axis=-1)  # (H, W, 4)

    # 相机坐标系 → 世界坐标系
    pcd_world = np.einsum('ij,hwj->hwi', T_cam2world, pcd_camera_homo)  # (H, W, 4)

    # ==================== 世界坐标系转换结果检查 ====================
    # 检查世界坐标系的有效性
    world_xyz = pcd_world[..., :3]
    if debug:
        valid_mask = depth_processed > 0
        if np.sum(valid_mask) > 0:
            print(f"世界坐标系范围:")
            print(f"  X: {world_xyz[valid_mask, 0].min():.4f} ~ {world_xyz[valid_mask, 0].max():.4f} 米")
            print(f"  Y: {world_xyz[valid_mask, 1].min():.4f} ~ {world_xyz[valid_mask, 1].max():.4f} 米")
            print(f"  Z: {world_xyz[valid_mask, 2].min():.4f} ~ {world_xyz[valid_mask, 2].max():.4f} 米")
            print(f"异常值: NaN={np.sum(np.isnan(world_xyz))}, Inf={np.sum(np.isinf(world_xyz))}")

            # 坐标合理性检查
            max_coord = max(abs(world_xyz[valid_mask, 0].max()), abs(world_xyz[valid_mask, 0].min()),
                           abs(world_xyz[valid_mask, 1].max()), abs(world_xyz[valid_mask, 1].min()),
                           abs(world_xyz[valid_mask, 2].max()), abs(world_xyz[valid_mask, 2].min()))
            if max_coord > 10:
                print(f"⚠️ 警告: 最大坐标值 {max_coord:.2f} 米可能过大，请检查深度缩放")
            else:
                print(f"✓ 坐标范围合理，最大坐标值: {max_coord:.2f} 米")

    # 保存点云（使用处理后的深度图）
    # save_pointcloud_as_ply(world_xyz, depth_processed)
    # ==================== 可选：保存深度图为PNG文件 ====================
    # 注意：这部分代码仅用于调试，正常使用时可以注释掉
    # import imageio
    # import cv2

    # # 方法1：归一化到0-255，保存为8位PNG（适合可视化）
    # depth_vis = depth_processed.copy()
    # depth_vis = np.nan_to_num(depth_vis, nan=0.0, posinf=0.0, neginf=0.0)
    # d_min, d_max = depth_vis.min(), depth_vis.max()
    # if d_max > d_min:
    #     depth_vis = (depth_vis - d_min) / (d_max - d_min)
    # else:
    #     depth_vis = np.zeros_like(depth_vis)
    # depth_vis = (depth_vis * 255).astype(np.uint8)
    # imageio.imwrite("depth_vis.png", depth_vis)

    # # 方法2：保存为16位PNG（保留处理后的深度，适合后续处理）
    # depth_16 = (depth_processed * 1000).astype(np.uint16)  # 米转毫米
    # imageio.imwrite("depth_processed.png", depth_16)

    return world_xyz

def convert_transitions_to_obs_tensors(transitions, target_size=(256, 256), debug=False):
    """
    将transitions转换为obs_tensors，包含图像裁剪和缩放功能

    Args:
        transitions: 输入的transition数据
        target_size: 目标图像尺寸 (height, width)
        debug: 是否输出调试信息

    Returns:
        torch.Tensor: 处理后的观测张量，形状为 (T, n_cam=1, 2, 3, target_H, target_W)
    """
    T = len(transitions)
    n_cam = 1  # 只考虑 'front'

    rgb_sample = transitions[0]["observations"]["front"][0]["rgb"]
    H, W = rgb_sample.shape[:2]
    obs_tensors = []

    if debug:
        print(f"🔍 处理 {T} 个transitions，原始图像尺寸: {H}x{W}")
        print(f"🎯 目标尺寸: {target_size[0]}x{target_size[1]}")

    for i, t in enumerate(transitions):
        obs = t["observations"]["front"][0]
        rgb = obs["rgb"]  # (H, W, 3)
        depth = obs["depth"]  # (H, W)

        # 步骤1: 先生成完整的点云（使用原始深度图）
        xyz = depth_to_xyz(depth, debug=False, validate_params=False, auto_scale_intrinsics=True, auto_scale_depth=True)  # (H, W, 3)

        # 步骤2: 对RGB图像进行裁剪和缩放
        rgb_resized, transform_info = crop_and_resize_image(rgb, target_size, debug=(debug and i == 0))

        # 步骤3: 对点云进行相同的裁剪和缩放
        xyz_resized = crop_and_resize_pointcloud(xyz, transform_info, debug=(debug and i == 0))

        # 步骤4: 转换为张量格式
        rgb_tensor = torch.from_numpy(rgb_resized.transpose(2, 0, 1)).float() / 255.0  # (3, H, W)
        # rgb_tensor = (rgb_tensor - 0.5) * 2  # [-1, 1]

        xyz_tensor = torch.from_numpy(xyz_resized).float().permute(2, 0, 1)  # (3, H, W)

        # 检查异常值
        if torch.isnan(xyz_tensor).any() or torch.isinf(xyz_tensor).any():
            print(f"⚠️ Warning: xyz contains NaN or Inf in frame {i}")

        # 组合成 obs_tensor: shape (1, 2, 3, H, W)
        frame_tensor = torch.stack([rgb_tensor, xyz_tensor], dim=0).unsqueeze(0)  # (1, 2, 3, H, W)
        obs_tensors.append(frame_tensor)

    obs_tensors = torch.cat(obs_tensors, dim=0)  # (T, 2, 3, H, W)
    obs_tensors = obs_tensors.unsqueeze(1)       # (T, n_cam=1, 2, 3, H, W)

    if debug:
        print(f"✅ 最终obs_tensors形状: {obs_tensors.shape}")

    return obs_tensors

from scipy.spatial.transform import Rotation



def convert_transitions_to_episode(transitions):
    # 使用全帧模式：直接把所有帧作为关键帧
    n_frames = len(transitions)
    print(f"🔍 Processing episode with {n_frames} transitions")

    if n_frames <= 1:
        print(f"⚠️ Episode has only {n_frames} frames, skipping...")
        return None

    keyframes = keypoint_discovery_from_transitions(transitions)

    frame_ids = list(range(len(keyframes)-1))

    obs_tensors = convert_transitions_to_obs_tensors(transitions, target_size=(256, 256), debug=True)  # shape: (T, n_cam, 2, 3, H, W)
    T = obs_tensors.shape[0]

    action_list = []
    gripper_list = []
    attn_indices = []
    keyframe_state_ls = []
    trajectory_list = []

    for i in range(len(keyframes)):
        idx = keyframes[i]
        t = transitions[idx]
        state = t["observations"]["state"][0]

        # 获取下一帧状态（如果存在）
        next_state = None
        if idx + 1 < len(transitions):
            next_state = transitions[idx + 1]["observations"]["state"][0]

        # 处理action - 使用下一帧的实际gripper状态
        action = t['actions']

        action = convert_action_to_8d(action, state, next_state)
        if np.isnan(action).any():
            print(f"⚠️ NaN detected in action at idx {idx}")
            continue

        gripper_open = t["actions"][-1]  # float
        if np.isnan(gripper_open):
            print(f"⚠️ NaN in gripper_open at idx {idx}")
            continue

        action_tensor = torch.from_numpy(action).float().unsqueeze(0)  # (1, 8)
        action_list.append(action_tensor)

        # gripper 是完整 action（作为起始状态）
        gripper_list.append(action_tensor.clone())

        # 关键帧模式：为每个frame构建trajectory历史
        # trajectory_list[i] 对应 frame_ids[i+1] 的历史（从第一个关键帧到当前关键帧的轨迹）
        # 注意：trajectory需要与action_list保持相同的8维格式 [x,y,z,qx,qy,qz,qw,gripper]
        if i < len(keyframes) - 1:
            inter_traj = []

            # 收集从第一个关键帧到当前关键帧的所有关键帧state作为历史
            for k in range(0, i + 1):
                keyframe_idx = keyframes[k]  # 获取关键帧在transitions中的实际索引
                state_j = transitions[keyframe_idx]["observations"]["state"][0]

                # 提取位置和姿态
                pos = state_j[4:7]  # [x,y,z]
                euler = state_j[7:10]  # [roll,pitch,yaw] - 欧拉角格式
                gripper = state_j[0]  # 夹爪状态
                gripper = np.clip(gripper, -1, 1)

                # 欧拉角转四元数，保持与action_list一致的格式
                quat = Rotation.from_euler('xyz', euler).as_quat()  # [qx,qy,qz,qw]

                traj_action = np.concatenate([
                    pos,      # [x,y,z]
                    quat,     # [qx,qy,qz,qw]
                    [gripper] # 夹爪状态
                ])
                inter_traj.append(torch.from_numpy(traj_action).float().unsqueeze(0))

            trajectory_list.append(torch.cat(inter_traj, dim=0))

        obs_front = t["observations"]
        gripper_pos_3 = torch.from_numpy(t["observations"]["state"][0, 4:7]).float()
        if torch.isnan(gripper_pos_3).any():
            print(f"⚠️ NaN in gripper_pos_3 at idx {idx}")
            continue

        # 计算注意力点（使用缩放后的图像尺寸）
        u, v = obs_to_attn_from_transition(gripper_pos_3, target_size=(256, 256), original_size=(240, 424))
        attn_indices.append({"front": (u, v)})

    keyframe_obs_tensors = obs_tensors[keyframes[:-1]]

    episode = [
        frame_ids,          # list[int]
        keyframe_obs_tensors.numpy(),       # 实际 shape: (T-1, n, m, ch, H, W)
        action_list[1:],        # list of (1, 8) tensors
        attn_indices[:-1],   # list[Dict]
        gripper_list[:-1],       # list of (1, 8) tensors
        trajectory_list
    ]
    for i in frame_ids:
        if torch.all(episode[4][i] == 0):
            print(f"⚠️ Frame {i} contains all zero gripper data")

    '''   
        # 验证 gripper_list 和 transitions 的一致性
    for i, idx in enumerate(frame_ids):
        print(
            f"Frame {idx}: "
            f"transitions动作[3]={episode[2][i][:7]}, "
            f"gripper_list[3]={episode[4][i][0, :7]}"
        )
    
        # ===================== DEBUG 检查各字段结构 =====================
    
    print("\n===== Episode 结构检查 =====")
    print(f"🔹 frame_ids: {frame_ids}")
    print(f"🔹 keyframe_obs_tensors.shape: {keyframe_obs_tensors.shape}")
    
    for i, a in enumerate(action_list[1:]):
        print(f"🔹 action[{i}].shape: {a.shape}, value range: {a.min():.3f} ~ {a.max():.3f}")

    for i, g in enumerate(gripper_list[:-1]):
        print(f"🔹 gripper[{i}].shape: {g.shape}, value range: {g.min():.3f} ~ {g.max():.3f}")

    print(f"🔹 attn_indices ({len(attn_indices[:-1])}): {[list(v.values())[0] for v in attn_indices[:-1]]}")
    
    for i, traj in enumerate(trajectory_list):
        if not isinstance(traj, torch.Tensor):
            print(f"❌ trajectory[{i}] 不是 tensor")
        elif traj.ndim != 2 or traj.shape[1] != 8:
            print(f"❌ trajectory[{i}] 维度错误: {traj.shape}")
        elif traj.shape[0] == 0:
            print(f"❌ trajectory[{i}] 是空 trajectory，loss 可能 NaN")
        else:
            print(f"✅ trajectory[{i}].shape: {traj.shape}, value range: {traj.min():.3f} ~ {traj.max():.3f}")
    print("=====================================\n")
    '''
    return episode

def process_folder(input_dir, output_dir):
    input_dir = Path(input_dir)
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    pkl_files = sorted(input_dir.glob("*.pkl"))
    for idx, pkl_file in enumerate(pkl_files):
        with open(pkl_file, "rb") as f:
            transitions = pickle.load(f)
        episode = convert_transitions_to_episode(transitions)

        new_name = f"ep{idx+1:04d}.pkl"
        out_path = output_dir / new_name

        with open(out_path, "wb") as f:
            pickle.dump(episode, f)
        print(f"Converted {pkl_file.name} -> {new_name}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--task", type=str, required=True, help="任务名，如 plug_charger_in_power_supply+0")
    parser.add_argument("--input_dir", type=str, default="raw_data_try", help="原始数据根目录")
    parser.add_argument("--output_dir", type=str, default="converted_data_try", help="输出目录根路径")
    args = parser.parse_args()

    input_dir = Path(args.input_dir) / args.task
    output_dir = Path(args.output_dir) / f"{args.task}+0"

    process_folder(input_dir=input_dir, output_dir=output_dir)

