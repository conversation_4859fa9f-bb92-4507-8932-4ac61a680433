#!/bin/bash

# 修改版训练脚本，输出到日志文件
# 基于原始的train_keypose_peract.sh

main_dir=Actor_18Peract_96Demo_6tasks_front

export CUDA_VISIBLE_DEVICES=1

dataset=/data/wangyiwen/3d_diffuser_actor/data/peract/packaged_highres/train
valset=/data/wangyiwen/3d_diffuser_actor/data/peract/packaged_highres/val

lr=1e-4
dense_interpolation=1
interpolation_length=2
num_history=3
diffusion_timesteps=100
B=5
C=120
ngpus=1
quaternion_format=xyzw

# 创建日志目录
LOG_DIR="train_logs/$main_dir/diffusion_multitask-C$C-B$B-lr$lr-DI$dense_interpolation-$interpolation_length-H$num_history-DT$diffusion_timesteps"
mkdir -p "$LOG_DIR"

# 日志文件路径
TRAIN_LOG="$LOG_DIR/training.log"
STDOUT_LOG="$LOG_DIR/stdout.log"
STDERR_LOG="$LOG_DIR/stderr.log"

echo "开始训练..."
echo "日志目录: $LOG_DIR"
echo "训练日志: $TRAIN_LOG"
echo "标准输出: $STDOUT_LOG"
echo "错误输出: $STDERR_LOG"

# 启动训练并重定向输出
CUDA_LAUNCH_BLOCKING=0 torchrun --nproc_per_node $ngpus --master_port $RANDOM \
    main_trajectory.py \
    --tasks stack_wine put_plate_in_colored_dish_rack put_knife_in_knife_block put_toilet_roll_on_stand plug_charger_in_power_supply phone_on_base\
    --dataset $dataset \
    --valset $valset \
    --gripper_loc_bounds /data/wangyiwen/3d_diffuser_actor/tasks/18_peract_tasks_location_bounds.json \
    --num_workers 5 \
    --train_iters 600000 \
    --embedding_dim $C \
    --use_instruction 1 \
    --instructions /data/wangyiwen/3d_diffuser_actor/instructions/peract/instructions_custom6.pkl \
    --rotation_parametrization 6D \
    --diffusion_timesteps $diffusion_timesteps \
    --val_freq 2000 \
    --dense_interpolation $dense_interpolation \
    --interpolation_length $interpolation_length \
    --exp_log_dir $main_dir \
    --batch_size $B \
    --batch_size_val 8 \
    --cache_size 600 \
    --cache_size_val 0 \
    --keypose_only 1 \
    --variations 0  \
    --lr $lr\
    --num_history $num_history \
    --cameras  front\
    --max_episodes_per_task -1 \
    --quaternion_format $quaternion_format \
    --goal_frame_mode 1 \
    --run_log_dir diffusion_multitask-C$C-B$B-lr$lr-DI$dense_interpolation-$interpolation_length-H$num_history-DT$diffusion_timesteps \
    2>&1 | tee "$TRAIN_LOG"

echo "训练完成!"
echo "查看日志: tail -f $TRAIN_LOG" 