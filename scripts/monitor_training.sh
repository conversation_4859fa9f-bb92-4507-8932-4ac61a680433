#!/bin/bash

# 实时监控训练脚本
# 使用方法: bash monitor_training.sh [日志目录]

LOG_DIR=${1:-"train_logs"}
REFRESH_INTERVAL=10  # 刷新间隔（秒）

echo "=== 训练监控工具 ==="
echo "日志目录: $LOG_DIR"
echo "刷新间隔: ${REFRESH_INTERVAL}秒"
echo "按 Ctrl+C 停止监控"
echo "=========================="

# 查找最新的日志目录
find_latest_log_dir() {
    if [ -d "$LOG_DIR" ]; then
        find "$LOG_DIR" -name "*.pth" -exec dirname {} \; | sort | uniq | tail -1
    fi
}

# 分析检查点文件
analyze_checkpoints() {
    local log_dir=$1
    if [ -f "$log_dir/best.pth" ]; then
        echo "📁 最佳模型: $log_dir/best.pth"
        echo "   大小: $(du -h "$log_dir/best.pth" | cut -f1)"
        echo "   修改时间: $(stat -c %y "$log_dir/best.pth" 2>/dev/null || stat -f %Sm "$log_dir/best.pth")"
    fi
    
    if [ -f "$log_dir/last.pth" ]; then
        echo "📁 最新模型: $log_dir/last.pth" 
        echo "   大小: $(du -h "$log_dir/last.pth" | cut -f1)"
        echo "   修改时间: $(stat -c %y "$log_dir/last.pth" 2>/dev/null || stat -f %Sm "$log_dir/last.pth")"
    fi
}

# 分析TensorBoard事件文件
analyze_events() {
    local log_dir=$1
    local event_files=$(find "$log_dir" -name "events.out.tfevents.*" 2>/dev/null)
    
    if [ -n "$event_files" ]; then
        echo "📊 TensorBoard事件文件:"
        for file in $event_files; do
            echo "   $(basename $file) ($(du -h "$file" | cut -f1))"
        done
    fi
}

# 监控GPU使用情况
monitor_gpu() {
    if command -v nvidia-smi &> /dev/null; then
        echo "🖥️  GPU状态:"
        nvidia-smi --query-gpu=index,name,utilization.gpu,memory.used,memory.total,temperature.gpu --format=csv,noheader,nounits | \
        while IFS=, read -r idx name util mem_used mem_total temp; do
            printf "   GPU%s: %s | 利用率:%s%% | 内存:%s/%sMB | 温度:%s°C\n" \
                "$idx" "$name" "$util" "$mem_used" "$mem_total" "$temp"
        done
    fi
}

# 检查训练进程
check_training_process() {
    echo "🔄 训练进程:"
    local python_processes=$(ps aux | grep -E "(main_trajectory|main_keypose)" | grep -v grep)
    if [ -n "$python_processes" ]; then
        echo "$python_processes" | while read line; do
            echo "   $line"
        done
    else
        echo "   没有发现训练进程"
    fi
}

# 主监控循环
while true; do
    clear
    echo "=== 训练监控 $(date) ==="
    echo
    
    # 查找最新日志目录
    LATEST_LOG_DIR=$(find_latest_log_dir)
    
    if [ -n "$LATEST_LOG_DIR" ]; then
        echo "📂 当前日志目录: $LATEST_LOG_DIR"
        echo
        
        # 分析检查点
        analyze_checkpoints "$LATEST_LOG_DIR"
        echo
        
        # 分析事件文件
        analyze_events "$LATEST_LOG_DIR"
        echo
        
        # 显示超参数信息
        if [ -f "$LATEST_LOG_DIR/hparams.json" ]; then
            echo "⚙️  训练配置:"
            if command -v jq &> /dev/null; then
                jq -r 'to_entries[] | "   \(.key): \(.value)"' "$LATEST_LOG_DIR/hparams.json" | head -10
            else
                echo "   $(head -5 "$LATEST_LOG_DIR/hparams.json")"
            fi
            echo
        fi
    else
        echo "❌ 未找到日志目录"
        echo
    fi
    
    # 检查训练进程
    check_training_process
    echo
    
    # 监控GPU
    monitor_gpu
    echo
    
    echo "下次刷新: ${REFRESH_INTERVAL}秒后 | 按Ctrl+C停止"
    sleep $REFRESH_INTERVAL
done 