#!/usr/bin/env python3
"""
命令行日志分析工具
用于在没有图形界面的情况下分析训练日志
"""

import os
import glob
import argparse
from pathlib import Path
import pandas as pd
import numpy as np

try:
    from tensorboard.backend.event_processing.event_accumulator import EventAccumulator
    TENSORBOARD_AVAILABLE = True
except ImportError:
    TENSORBOARD_AVAILABLE = False
    print("警告: 未安装tensorboard，只能分析终端输出日志")


def analyze_tensorboard_logs(log_dir):
    """分析TensorBoard日志文件"""
    if not TENSORBOARD_AVAILABLE:
        print("TensorBoard未安装，跳过事件文件分析")
        return
    
    # 查找事件文件
    event_files = glob.glob(os.path.join(log_dir, "events.out.tfevents.*"))
    if not event_files:
        print(f"未找到TensorBoard事件文件: {log_dir}")
        return
    
    print(f"\n=== TensorBoard日志分析 ===")
    print(f"日志目录: {log_dir}")
    
    # 加载事件
    ea = EventAccumulator(log_dir)
    ea.Reload()
    
    # 获取所有标量数据
    scalar_tags = ea.Tags()['scalars']
    
    print(f"\n📊 可用指标 ({len(scalar_tags)}个):")
    for i, tag in enumerate(scalar_tags, 1):
        print(f"  {i:2d}. {tag}")
    
    # 分析关键指标
    key_metrics = [
        'val-losses/mean/traj_pos_l2',
        'val-losses/mean/traj_pos_acc_001', 
        'val-losses/mean/traj_rot_l1',
        'val-losses/mean/traj_gripper',
        'train-loss/noise_mse'
    ]
    
    print(f"\n📈 关键指标趋势:")
    for metric in key_metrics:
        if metric in scalar_tags:
            scalar_events = ea.Scalars(metric)
            if scalar_events:
                values = [event.value for event in scalar_events]
                steps = [event.step for event in scalar_events]
                
                print(f"\n  {metric}:")
                print(f"    最新值: {values[-1]:.4f} (步骤 {steps[-1]})")
                print(f"    最佳值: {min(values) if 'loss' in metric or 'l2' in metric or 'l1' in metric else max(values):.4f}")
                print(f"    变化趋势: {values[-1] - values[0]:+.4f}")
                
                # 显示最近10个值
                recent_values = values[-10:]
                recent_steps = steps[-10:]
                print(f"    最近10个值:")
                for step, val in zip(recent_steps, recent_values):
                    print(f"      步骤 {step}: {val:.4f}")


def analyze_terminal_logs(log_file):
    """分析终端输出日志"""
    if not os.path.exists(log_file):
        print(f"日志文件不存在: {log_file}")
        return
    
    print(f"\n=== 终端日志分析 ===")
    print(f"日志文件: {log_file}")
    
    with open(log_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 提取训练步骤信息
    step_info = []
    current_step = None
    
    for line in lines:
        line = line.strip()
        if line.startswith("Step "):
            current_step = int(line.split()[1].rstrip(':'))
        elif ': ' in line and current_step is not None:
            metric, value = line.split(': ')
            try:
                value = float(value)
                step_info.append((current_step, metric, value))
            except ValueError:
                pass
    
    if step_info:
        # 转换为DataFrame进行分析
        df = pd.DataFrame(step_info, columns=['Step', 'Metric', 'Value'])
        
        print(f"\n📊 找到 {len(df)} 条指标记录")
        print(f"步骤范围: {df['Step'].min()} - {df['Step'].max()}")
        
        # 按指标分组分析
        unique_metrics = df['Metric'].unique()
        print(f"\n📈 指标摘要:")
        
        for metric in sorted(unique_metrics):
            metric_data = df[df['Metric'] == metric]
            if len(metric_data) > 0:
                latest_value = metric_data.iloc[-1]['Value']
                latest_step = metric_data.iloc[-1]['Step']
                best_value = (metric_data['Value'].min() 
                             if any(x in metric for x in ['loss', 'l2', 'l1']) 
                             else metric_data['Value'].max())
                
                print(f"\n  {metric}:")
                print(f"    最新: {latest_value:.4f} (步骤 {latest_step})")
                print(f"    最佳: {best_value:.4f}")
                
                # 显示趋势
                if len(metric_data) >= 2:
                    trend = latest_value - metric_data.iloc[0]['Value']
                    print(f"    趋势: {trend:+.4f}")


def find_log_directories():
    """查找可能的日志目录"""
    log_dirs = []
    
    # 查找train_logs目录
    if os.path.exists("train_logs"):
        for root, dirs, files in os.walk("train_logs"):
            # 查找包含TensorBoard事件文件或检查点的目录
            has_events = any(f.startswith("events.out.tfevents") for f in files)
            has_checkpoint = any(f.endswith(".pth") for f in files)
            
            if has_events or has_checkpoint:
                log_dirs.append(root)
    
    return log_dirs


def main():
    parser = argparse.ArgumentParser(description="分析训练日志")
    parser.add_argument("--log_dir", help="日志目录路径")
    parser.add_argument("--log_file", default="train_logs/Actor_18Peract_96Demo_6tasks_front/diffusion_multitask-C120-B5-lr1e-4-DI1-2-H3-DT100/events.out.tfevents.1748833548.ubuntu.3645775.0", help="终端日志文件路径") 
    parser.add_argument("--auto", action="store_false", help="自动查找日志目录")
    
    args = parser.parse_args()
    
    if args.auto:
        print("🔍 自动查找日志目录...")
        log_dirs = find_log_directories()
        if log_dirs:
            print(f"找到 {len(log_dirs)} 个日志目录:")
            for i, log_dir in enumerate(log_dirs, 1):
                print(f"  {i}. {log_dir}")
            
            # 分析最新的日志目录
            latest_dir = max(log_dirs, key=lambda x: os.path.getmtime(x))
            print(f"\n分析最新目录: {latest_dir}")
            analyze_tensorboard_logs(latest_dir)
        else:
            print("未找到日志目录")
    
    if args.log_dir:
        analyze_tensorboard_logs(args.log_dir)
    
    if args.log_file:
        analyze_terminal_logs(args.log_file)
    
    if not any([args.log_dir, args.log_file, args.auto]):
        print("使用示例:")
        print("  python analyze_logs.py --auto")
        print("  python analyze_logs.py --log_dir train_logs/Actor_18Peract_96Demo_6tasks_front/diffusion_multitask-C120-B5-lr1e-4-DI1-2-H3-DT100")
        print("  python analyze_logs.py --log_file training.log")


if __name__ == "__main__":
    main() 