#!/bin/bash

# Script to save point clouds as PLY files
# References the same data paths as train_keypose_real.sh

# Data paths (same as in train_keypose_real.sh)
dataset=data/converted_data_try/train
valset=data/converted_data_try/val
task=pick_bread

# Output directory
output_dir=output/pointclouds_ply

# Processing parameters
max_episodes=10
max_frames=20

# Data processing mode
# Set to true to match training-time data augmentation (recommended)
# Set to false to use raw data without augmentation
use_training_augmentation=true

echo "=== Saving Point Clouds as PLY Files ==="
echo "Task: $task"
echo "Training data: $dataset"
echo "Validation data: $valset"
echo "Output directory: $output_dir"
echo "Max episodes: $max_episodes"
echo "Max frames per episode: $max_frames"
echo "Use training augmentation: $use_training_augmentation"
echo ""

# Run the Python script
if [ "$use_training_augmentation" = true ]; then
    python save_pointclouds_as_ply.py \
        --task $task \
        --train_data $dataset \
        --val_data $valset \
        --output_dir $output_dir \
        --max_episodes $max_episodes \
        --max_frames $max_frames \
        --process_val \
        --use_training_augmentation
else
    python save_pointclouds_as_ply.py \
        --task $task \
        --train_data $dataset \
        --val_data $valset \
        --output_dir $output_dir \
        --max_episodes $max_episodes \
        --max_frames $max_frames \
        --process_val \
        --no_augmentation
fi

echo ""
echo "=== Complete ==="
echo "PLY files saved to: $output_dir"
