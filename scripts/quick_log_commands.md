# 无图形界面下的训练日志快速查看指南

## 🚀 快速命令

### 1. 查看最新训练输出
```bash
# 实时查看训练日志（如果重定向了输出）
tail -f train_logs/*/training.log

# 查看最近100行
tail -100 train_logs/*/training.log

# 搜索特定指标
grep "Step" train_logs/*/training.log | tail -10
```

### 2. 检查模型检查点
```bash
# 查看所有检查点文件
find train_logs -name "*.pth" -exec ls -lh {} \;

# 查看最新修改的检查点
find train_logs -name "*.pth" -exec ls -lt {} \; | head -5
```

### 3. 分析TensorBoard日志
```bash
# 使用我们的分析脚本
python scripts/analyze_logs.py --auto

# 分析特定目录
python scripts/analyze_logs.py --log_dir train_logs/Actor_18Peract_96Demo_6tasks_front/diffusion_multitask-*
```

### 4. 实时监控训练
```bash
# 启动监控脚本
bash scripts/monitor_training.sh

# 监控GPU使用情况
watch -n 2 nvidia-smi
```

### 5. 提取关键指标
```bash
# 提取验证精度
grep "traj_pos_acc_001" train_logs/*/training.log | tail -20

# 提取损失值
grep "traj_pos_l2" train_logs/*/training.log | tail -20

# 查看训练进度
grep "Step" train_logs/*/training.log | tail -10
```

### 6. 检查训练配置
```bash
# 查看超参数配置
cat train_logs/*/hparams.json | jq .

# 或者不用jq
cat train_logs/*/hparams.json
```

### 7. 远程TensorBoard访问
```bash
# 在服务器上启动
tensorboard --logdir=train_logs --host=0.0.0.0 --port=6006 &

# 在本地执行SSH隧道
ssh -L 6006:localhost:6006 用户名@服务器IP
# 然后在本地浏览器打开 http://localhost:6006
```

## 📊 关键指标解释

- **traj_pos_l2**: 位置误差 (越小越好，< 0.05为良好)
- **traj_pos_acc_001**: 位置精度 (越大越好，> 0.7为良好)  
- **traj_rot_l1**: 旋转误差 (越小越好，< 0.1为良好)
- **traj_gripper**: 抓手精度 (越大越好，> 0.8为良好)

## 🔧 故障排除

### 找不到日志文件
```bash
# 查找所有可能的日志位置
find . -name "*.log" -o -name "events.out.tfevents.*" -o -name "*.pth" 2>/dev/null
```

### TensorBoard访问问题
```bash
# 检查端口是否被占用
netstat -tlnp | grep 6006

# 杀死占用进程
pkill -f tensorboard
```

### 内存不足
```bash
# 监控系统资源
htop
free -h
df -h
``` 