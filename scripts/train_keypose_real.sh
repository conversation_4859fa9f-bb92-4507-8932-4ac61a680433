main_dir=Actor_18Peract_96Demo_plug_front_optimized_debug

# 优化设置：使用所有可用GPU
export CUDA_VISIBLE_DEVICES=0,1  # 根据你的GPU数量调整

# CUDA优化设置
export CUDNN_ENABLED=1
export CUDNN_BENCHMARK=1
export CUDNN_DETERMINISTIC=0  # 为了性能，关闭确定性
export CUDA_LAUNCH_BLOCKING=0  # 异步执行
export NCCL_P2P_DISABLE=0  # 启用P2P通信
export NCCL_IB_DISABLE=0   # 启用InfiniBand（如果有）

dataset=data/converted_data_try/train
valset=data/converted_data_try/val

# 优化的训练参数
lr=1e-4  # 稍微提高学习率，因为批次更大
dense_interpolation=1
interpolation_length=2
num_history=3
diffusion_timesteps=100
B=8     # 大幅增加批次大小，充分利用显存
C=120
ngpus=2  # 使用多GPU训练
quaternion_format=xyzw

# 设置输入输出路径
input_train_dir="data/raw_data_try/train"
output_train_dir="data/converted_data_try/train"
input_val_dir="data/raw_data_try/val"
output_val_dir="data/converted_data_try/val"
# 调用转换脚本
python convert.py --task 0814_pick_bread --input_dir $input_train_dir --output_dir $output_train_dir
python convert.py --task 0814_pick_bread --input_dir $input_val_dir --output_dir $output_val_dir

# 优化的训练启动命令
torchrun --nproc_per_node $ngpus --master_port $RANDOM \
    main_trajectory1.py \
    --tasks 0814_pick_bread\
    --dataset $dataset \
    --valset $valset \
    --num_workers 8 \
    --train_iters 600000 \
    --embedding_dim $C \
    --use_instruction 0 \
    --rotation_parametrization 6D \
    --diffusion_timesteps $diffusion_timesteps \
    --val_freq 2000 \
    --dense_interpolation $dense_interpolation \
    --interpolation_length $interpolation_length \
    --exp_log_dir $main_dir \
    --batch_size $B \
    --batch_size_val 32 \
    --cache_size 2000 \
    --cache_size_val 500 \
    --keypose_only 0 \
    --variations 0  \
    --lr $lr\
    --num_history $num_history \
    --cameras front\
    --max_episodes_per_task -1 \
    --quaternion_format $quaternion_format \
    --accumulate_grad_batches 1 \
    --image_size "256,256" \
    --save_freq 2000 \
    --run_log_dir 0814_optimized_diffusion_multitask-C$C-B$B-lr$lr-DI$dense_interpolation-$interpolation_length-H$num_history-DT$diffusion_timesteps \
    # --checkpoint /data/xjd/3d_diffuser_actor_2/train_logs/Actor_18Peract_96Demo_plug_front_optimized/备份optimized_diffusion_multitask-C120-B8-lr1e-4-DI1-2-H3-DT100/last.pth
 