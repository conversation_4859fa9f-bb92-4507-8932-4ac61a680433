#!/bin/bash

# 3D Diffuser Actor 高性能训练脚本
# 针对大显存GPU优化版本

main_dir=Actor_18Peract_96Demo_plug_front_ultra_optimized

# =============================================================================
# GPU和CUDA优化设置
# =============================================================================

# 根据你的GPU数量调整（假设有4张高端GPU）
export CUDA_VISIBLE_DEVICES=0,1,2,3

# CUDA性能优化
export CUDNN_ENABLED=1
export CUDNN_BENCHMARK=1           # 自动寻找最优算法
export CUDNN_DETERMINISTIC=0       # 关闭确定性以提升性能
export CUDA_LAUNCH_BLOCKING=0      # 异步执行
export NCCL_P2P_DISABLE=0         # 启用GPU间P2P通信
export NCCL_IB_DISABLE=0          # 启用InfiniBand（如果有）
export NCCL_TREE_THRESHOLD=0      # 优化NCCL通信
export NCCL_SOCKET_IFNAME=^docker0,lo  # 网络接口优化

# 内存优化
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
export CUDA_DEVICE_MAX_CONNECTIONS=1

# =============================================================================
# 数据路径设置
# =============================================================================
dataset=data/converted_data_try/train
valset=data/converted_data_try/val

# 设置输入输出路径
input_train_dir="data/raw_data_try/train"
output_train_dir="data/converted_data_try/train"
input_val_dir="data/raw_data_try/val"
output_val_dir="data/converted_data_try/val"

# =============================================================================
# 高性能训练参数
# =============================================================================

# 基础参数
lr=3e-4                    # 更高的学习率配合大批次
dense_interpolation=1
interpolation_length=2
num_history=3
diffusion_timesteps=100
quaternion_format=xyzw

# 高性能配置
B=128                      # 大批次大小，充分利用显存
C=256                      # 更大的嵌入维度
ngpus=4                    # 多GPU训练
accumulate_grad=4          # 梯度累积，等效批次大小 = 128*4*4 = 2048

# 数据加载优化
num_workers=32             # 大量数据加载进程
cache_size=5000           # 大缓存
cache_size_val=1000

# 验证和保存频率
val_freq=1000             # 更频繁的验证
save_freq=2000            # 定期保存

# =============================================================================
# 数据预处理（如果需要）
# =============================================================================
echo "开始数据转换..."
python convert.py --task pick_bread --input_dir $input_train_dir --output_dir $output_train_dir
python convert.py --task pick_bread --input_dir $input_val_dir --output_dir $output_val_dir
echo "数据转换完成"

# =============================================================================
# 启动高性能训练
# =============================================================================
echo "启动高性能训练..."
echo "配置信息："
echo "  - GPU数量: $ngpus"
echo "  - 批次大小: $B"
echo "  - 嵌入维度: $C"
echo "  - 学习率: $lr"
echo "  - 等效批次大小: $((B * ngpus * accumulate_grad))"

torchrun --nproc_per_node $ngpus --master_port $RANDOM \
    main_trajectory1.py \
    --tasks pick_bread \
    --dataset $dataset \
    --valset $valset \
    --num_workers $num_workers \
    --train_iters 600000 \
    --embedding_dim $C \
    --use_instruction 0 \
    --rotation_parametrization 6D \
    --diffusion_timesteps $diffusion_timesteps \
    --val_freq $val_freq \
    --dense_interpolation $dense_interpolation \
    --interpolation_length $interpolation_length \
    --exp_log_dir $main_dir \
    --batch_size $B \
    --batch_size_val 64 \
    --cache_size $cache_size \
    --cache_size_val $cache_size_val \
    --keypose_only 0 \
    --variations 0 \
    --lr $lr \
    --num_history $num_history \
    --cameras front \
    --max_episodes_per_task -1 \
    --quaternion_format $quaternion_format \
    --accumulate_grad_batches $accumulate_grad \
    --image_size "256,256" \
    --save_freq $save_freq \
    --image_rescale "0.9,1.1" \
    --run_log_dir ultra_optimized_diffusion-C$C-B$B-lr$lr-GA$accumulate_grad-W$num_workers-DI$dense_interpolation-$interpolation_length-H$num_history-DT$diffusion_timesteps

echo "训练完成！"

# =============================================================================
# 性能监控建议
# =============================================================================
echo ""
echo "性能监控建议："
echo "1. 使用 nvidia-smi -l 1 监控GPU使用率"
echo "2. 使用 htop 监控CPU和内存使用"
echo "3. 检查数据加载是否成为瓶颈"
echo "4. 监控训练日志中的步数/秒指标"
