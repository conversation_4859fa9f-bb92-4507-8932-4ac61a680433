3dda训练脚本

bash scripts/train_keypose_real.sh
'''
#每个 Transition 结构:
{
    "observations":{
"front":[ #只有一个相机时，就是 length=1 的 list
{
    "rgb":np.n<PERSON>ray(H,W,3),
    "depth":np.n<PERSON><PERSON>(H，W)
}
],
"state":np.n<PERSON><PERSON>(1，D)#e.g.(1，10)
    },
    'actions":np.n<PERSON><PERSON>(7,)#机器人未端位姿
}
'''
'''
对应的.dat中：
episode =[
frame ids: List[int],  # len = N
obs_tensors:List[Tensor(n_cam,2,3,H, W)],# len = N
action tensors:List[Tensor(1,8)], # len = N
camera dicts:List[Dict[str, None]], # len = N
gripper tensors:List[Tensor(1,8)],# len = N
intermediate acts:List[Tensor(M,8),# len = keyframe-1
'''
一些debug输出：
'''
        # Sample one instruction feature
        if self._instructions:
            instr = random.choice(self._instructions[task][variation])
            instr = instr[None].repeat(len(rgbs), 1, 1)
        else:
            instr = torch.zeros((rgbs.shape[0], 53, 512))

        # Get gripper tensors for respective frame ids
        gripper = torch.cat([episode[4][i] for i in frame_ids])
        #print("gripper",gripper)
        print("num",len(frame_ids))
        
        # gripper history
        gripper_history = torch.stack([
            torch.cat([episode[4][max(0, i-2)] for i in frame_ids]),
            torch.cat([episode[4][max(0, i-1)] for i in frame_ids]),
            gripper
        ], dim=1)
        print("history",gripper_history)

'''
下面的curr_gripper是上面的gripper_history
'''

    def compute_trajectory(
        self,
        trajectory_mask,
        rgb_obs,
        pcd_obs,
        instruction,
        curr_gripper
    ):

        # ===== 输入检查 =====
        print("\n=== Input Debug ===")
        print(f"RGB range: {rgb_obs.min():.3f} ~ {rgb_obs.max():.3f} (should be [0,1])")
        print(f"PCD NaN/Inf: {torch.isnan(pcd_obs).any()} / {torch.isinf(pcd_obs).any()}")
        print(f"Instr norm: {instruction.norm(dim=-1).mean():.3f} (should not be zero)")
        print(f"Gripper pos range: {curr_gripper[..., :3].min():.3f} ~ {curr_gripper[..., :3].max():.3f}")
        print("initial",curr_gripper)
        if torch.isnan(pcd_obs).any():
            print("⚠️ PCD contains NaN!")
        if instruction.norm(dim=-1).mean() < 0.01:
            print("⚠️ Instruction embeddings are near-zero!")
'''
'''        if torch.isnan(curr_gripper).any():
            print("⚠️ NaN detected in curr_gripper after normalization!")
        #print("normed",curr_gripper)

        curr_gripper = self.convert_rot(curr_gripper)

        # Check if NaN in the gripper after rotation conversion
        if torch.isnan(curr_gripper).any():
            print("rotated",curr_gripper)
            print("⚠️ NaN detected in curr_gripper after rotation conversion!")
            '''
上面的convert_rot中的curr_gripper，也即下面的signal中会出现全0，导致normalise_quat那一步出现nan，但在keypoint_discovery_from_transitions中已经写了对action筛选的代码了
'''   def convert_rot(self, signal):
        if torch.isnan(signal[..., 3:7]).any():
            print("⚠️ Invalid quaternion detected!")
        print("signal",signal)

        signal[..., 3:7] = normalise_quat(signal[..., 3:7])


        if self._rotation_parametrization == '6D':
            # The following code expects wxyz quaternion format!
            if self._quaternion_format == 'xyzw':
                signal[..., 3:7] = signal[..., (6, 3, 4, 5)]
            rot = quaternion_to_matrix(signal[..., 3:7])
            res = signal[..., 7:] if signal.size(-1) > 7 else None
            if len(rot.shape) == 4:
                B, L, D1, D2 = rot.shape
                rot = rot.reshape(B * L, D1, D2)
                rot_6d = get_ortho6d_from_rotation_matrix(rot)
                rot_6d = rot_6d.reshape(B, L, 6)
            else:
                rot_6d = get_ortho6d_from_rotation_matrix(rot)
            signal = torch.cat([signal[..., :3], rot_6d], dim=-1)
            if res is not None:
                signal = torch.cat((signal, res), -1)
        return signal
'''
（1）keypoint_discovery_from_transitions(transitions)
作用：从连续的动作序列中检测关键帧（如机械臂停止或夹爪状态变化时）。
(2) depth_to_cloud(depth)
作用：将深度图转换为世界坐标系下的点云。
(3) convert_transitions_to_obs_tensors(transitions)
作用：将原始观测（RGB-D图像）转换为张量格式。
(4) obs_to_attn_from_transition(gripper_pose, extrinsics=None, intrinsics=None)
功能：将机械手在世界坐标系中的位置转换为相机像素坐标
(5)convert_transitions_to_episode(transitions)
作用：整合关键帧、观测、动作等数据，构建最终训练所需的 episode。

dat数据中的一些数据（动作是绝对值）
数据包含 6 个部分

=== 第1部分: frame_ids ===
类型: <class 'list'>, 长度: 5
示例: [0, 1, 2, 3, 4]... (共5帧)

=== 第2部分: obs_tensors (RGB-D观测) ===
类型: <class 'numpy.ndarray'>, 形状: (5, 4, 2, 3, 256, 256)
结构说明: (时间步, 相机数, 模态数[RGB|XYZ], 通道, 高, 宽)
RGB示例 (第0帧, 相机0): (3, 256, 256)
XYZ示例 (第0帧, 相机0): (3, 256, 256)

=== 第3部分: keyframe_actions (关键帧动作) ===
类型: <class 'list'>, 长度: 5
第0个动作形状: torch.Size([1, 8])
示例数据: tensor([[ 0.2239, -0.0030,  0.8719, -0.1385,  0.9901, -0.0039, -0.0235,  1.0000]])...
示例数据: [tensor([[ 0.2239, -0.0030,  0.8719, -0.1385,  0.9901, -0.0039, -0.0235,  1.0000]]), tensor([[ 0.2245, -0.0028,  0.7963, -0.1385,  0.9901, -0.0038, -0.0239,  0.0000]]), tensor([[ 0.2219,  0.0022,  0.8705, -0.1383,  0.9899, -0.0257, -0.0184,  0.0000]]), tensor([[ 2.7195e-01, -1.7039e-01,  9.0919e-01, -1.3922e-01,  9.9026e-01,
          1.4993e-04, -7.3272e-04,  0.0000e+00]]), tensor([[ 2.7227e-01, -1.7103e-01,  8.6244e-01, -1.3933e-01,  9.9025e-01,
          2.4717e-04, -5.1737e-04,  1.0000e+00]])]...
绝对值

=== 第4部分: attn_indices (注意力索引) ===
类型: <class 'list'>, 长度: 6
第0帧的相机键: dict_keys(['left_shoulder', 'right_shoulder', 'wrist', 'front'])
相机 'left_shoulder' 的索引: (195, 10)

=== 第5部分: gripper_positions (夹爪位置) ===
类型: <class 'list'>, 长度: 5
第0个位置形状: torch.Size([1, 8])
示例数据: tensor([[ 2.7847e-01, -8.1520e-03,  1.4720e+00, -2.4322e-06,  9.9267e-01,
         -2.2479e-06,  1.2089e-01,  1.0000e+00]])...

=== 第6部分: trajectories (轨迹数据) ===
类型: <class 'list'>, 长度: 5
第0条轨迹形状: torch.Size([54, 8])
示例数据: tensor([ 2.7847e-01, -8.1520e-03,  1.4720e+00, -2.4322e-06,  9.9267e-01])...

故action取1：，gripper取：-1
action数据: tensor([[ 0.2239, -0.0030,  0.8719, -0.1385,  0.9901, -0.0039, -0.0235,  1.0000]])...
gripperpos数据: tensor([[ 2.7847e-01, -8.1520e-03,  1.4720e+00, -2.4322e-06,  9.9267e-01,
         -2.2479e-06,  1.2089e-01,  1.0000e+00]])...
action数据: tensor([[ 0.2245, -0.0028,  0.7963, -0.1385,  0.9901, -0.0038, -0.0239,  0.0000]])...
gripperpos数据: tensor([[ 0.2239, -0.0030,  0.8719, -0.1385,  0.9901, -0.0039, -0.0235,  1.0000]])...
action数据: tensor([[ 0.2219,  0.0022,  0.8705, -0.1383,  0.9899, -0.0257, -0.0184,  0.0000]])...
gripperpos数据: tensor([[ 0.2245, -0.0028,  0.7963, -0.1385,  0.9901, -0.0038, -0.0239,  0.0000]])...
action数据: tensor([[ 2.7195e-01, -1.7039e-01,  9.0919e-01, -1.3922e-01,  9.9026e-01,
          1.4993e-04, -7.3272e-04,  0.0000e+00]])...
gripperpos数据: tensor([[ 0.2219,  0.0022,  0.8705, -0.1383,  0.9899, -0.0257, -0.0184,  0.0000]])...
action数据: tensor([[ 2.7227e-01, -1.7103e-01,  8.6244e-01, -1.3933e-01,  9.9025e-01,
          2.4717e-04, -5.1737e-04,  1.0000e+00]])...
gripperpos数据: tensor([[ 2.7195e-01, -1.7039e-01,  9.0919e-01, -1.3922e-01,  9.9026e-01,
          1.4993e-04, -7.3272e-04,  0.0000e+00]])...
真机中的一些数据（动作是相对值）
(3d_diffuser_actor) user@ubuntu:/data/wangyiwen/3d_diffuser_actor$ python checkpkl.py
=== 文件加载成功 ===
总Transition数量: 1634

=== Transition结构 ===
Keys: dict_keys(['observations', 'actions', 'next_observations', 'infos'])

=== Observations内容 ===
相机视角: ['front', 'state']
front相机数据类型: <class 'numpy.ndarray'>, 长度: 1
RGB图像形状: (128, 128, 3)
深度图形状: (128, 128)
state数据形状: (1, 19)
示例state值: [[ 1.0061693  -2.2470253   0.05549416 -2.585358    0.        ]]...

=== Actions内容 ===
动作形状: (7,)
动作内容: [x,y,z: [0. 0. 0.] | euler: [0. 0. 0.] | gripper: 0.0]
动作内容: [x,y,z: [0. 0. 0.] | euler: [0. 0. 0.] | gripper: 0.0]
动作内容: [x,y,z: [0. 0. 0.] | euler: [0. 0. 0.] | gripper: 0.0]
动作内容: [x,y,z: [0. 0. 0.] | euler: [0. 0. 0.] | gripper: 0.0]
动作内容: [x,y,z: [0. 0. 0.] | euler: [0. 0. 0.] | gripper: 0.0]
动作内容: [x,y,z: [0. 0. 0.] | euler: [0. 0. 0.] | gripper: 0.0]
动作内容: [x,y,z: [0. 0. 0.] | euler: [0. 0. 0.] | gripper: 0.0]
动作内容: [x,y,z: [0. 0. 0.] | euler: [0. 0. 0.] | gripper: 0.0]
动作内容: [x,y,z: [0. 0. 0.] | euler: [0. 0. 0.] | gripper: 0.0]
动作内容: [x,y,z: [0. 0. 0.] | euler: [0. 0. 0.] | gripper: 0.0]
动作内容: [x,y,z: [-0.000463    0.00021102  0.0171353 ] | euler: [0.08796947 0.00982839 0.00225592] | gripper: 0.0]
动作内容: [x,y,z: [0. 0. 0.] | euler: [0. 0. 0.] | gripper: 0.0]
动作内容: [x,y,z: [0. 0. 0.] | euler: [0. 0. 0.] | gripper: 0.0]
动作内容: [x,y,z: [ 0.02686106 -0.28836639  0.02712108] | euler: [0.0889038  0.02462001 0.17372221] | gripper: 0.0]
动作内容: [x,y,z: [-0.00365549 -0.7137696   0.04586148] | euler: [0.25385284 0.02949481 0.69447921] | gripper: 0.0]
动作内容: [x,y,z: [-0.00292821 -0.82278734  0.01030986] | euler: [0.2954051  0.00811243 0.73132099] | gripper: 0.0]
动作内容: [x,y,z: [-0.00547125 -0.85704402  0.01181067] | euler: [0.29025681 0.00783213 0.70280129] | gripper: 0.0]
动作内容: [x,y,z: [-0.00836612 -0.87986637  0.01285235] | euler: [0.2698139  0.00734765 0.6786499 ] | gripper: 0.0]
动作内容: [x,y,z: [-0.00951275 -0.90555823  0.01386242] | euler: [0.27868326 0.00717241 0.65977539] | gripper: 0.0]
动作内容: [x,y,z: [-0.01065624 -0.90553074  0.01479526] | euler: [0.27866453 0.00759339 0.66545422] | gripper: 0.0]
相对值

state [[ 1.00616932e+00 -2.24702525e+00  5.54941632e-02 -2.58535790e+00
   0.00000000e+00  1.04083409e-17  0.00000000e+00 -1.73472348e-18
   0.00000000e+00  2.71050543e-19 -5.38907051e-01  1.16560601e-01
  -1.00428395e-01 -1.25776001e-04  1.21490320e-03  2.23077441e-04
  -1.06641452e-03 -3.69192159e-04 -9.76057258e-04]]
state [[ 1.0061775e+00 -2.3444495e+00  1.3843010e-01 -2.5922873e+00
  -4.4161320e-06  3.0218926e-06  1.5539750e-05 -9.9926792e-06
  -2.8290860e-05  1.3515661e-06 -4.9768943e-01  1.4070779e-01
  -8.8922150e-02  1.0045604e-05  1.2165985e-04  5.8345910e-04
  -1.1033767e-04 -1.9039586e-04  2.9176266e-03]]
state [[ 1.0061775e+00 -1.7103937e+00 -6.8048082e-02 -2.6745975e+00
  -9.4766056e-06  5.0213707e-06  1.4728658e-04 -1.6511218e-05
  -2.5285265e-04 -2.9889191e-06 -4.1490847e-01  6.7333219e-04
  -4.2055801e-02 -2.9701565e-04  1.0291496e-03  1.9540442e-03
  -8.5119327e-04 -1.9566263e-03  1.2213264e-03]]
state [[ 1.0061775e+00 -9.0556479e-01 -2.4529232e-02 -2.7560728e+00
  -1.9554598e-05  5.6640902e-06  3.9199233e-04 -1.7370570e-05
  -6.6999160e-04  4.2244656e-06 -2.7296212e-01 -1.6426447e-01
  -1.6815975e-02  5.8665621e-05 -1.1777255e-04  1.8839254e-03
  -9.5279526e-04 -2.1054395e-03 -1.5486948e-03]]
state [[ 1.0061693e+00 -4.4543236e-01 -1.2131088e-01 -2.9299746e+00
  -2.7758175e-05  5.3913086e-06  6.2569603e-04 -7.9581559e-06
  -1.0644083e-03  2.2100130e-05 -2.3135610e-01 -2.6937258e-01
  -4.5615523e-03  1.6900151e-04  2.0810174e-04  1.4380385e-03
  -4.0877188e-04 -7.1108935e-04  8.7005994e-04]]
state [[ 1.0061693e+00 -1.8208784e-01 -1.3139097e-01 -2.9793794e+00
  -3.3369037e-05  6.2961703e-06  7.9341914e-04 -5.4813177e-06
  -1.3472247e-03  2.5538729e-05 -2.0850505e-01 -3.2759452e-01
  -4.0145824e-03 -2.5730085e-04 -1.4092664e-04  1.8322807e-03
   2.5926394e-04 -3.4662776e-03 -4.0423751e-04]]
state [[ 1.0061693e+00  1.3314992e-02 -1.0152408e-01 -2.9913445e+00
  -4.2832613e-05  9.1813108e-06  1.0253955e-03 -6.0179555e-06
  -1.7408027e-03  3.1251046e-05 -1.8806395e-01 -3.6789021e-01
  -1.2402053e-04 -1.6250960e-05 -3.6344308e-04  1.6931507e-03
  -6.6493385e-06 -2.0354518e-03 -1.8812745e-03]]
state [[ 1.00616932e+00  1.73685193e-01 -1.06941864e-01 -2.99924660e+00
  -4.84556585e-05  9.05867364e-06  1.19033316e-03 -5.74266232e-06
  -2.01930129e-03  3.67006578e-05 -1.82113469e-01 -4.02339518e-01
   3.18112643e-03 -6.98470918e-04  4.26885454e-05  1.64750253e-03
  -8.39349057e-04 -3.15056206e-03 -2.77787261e-03]]
state [[ 1.0061693e+00  1.9585545e-01 -1.4657778e-01 -3.0118053e+00
  -5.8223672e-05  1.0926628e-05  1.4819637e-03 -4.1498752e-06
  -2.5083907e-03  5.1611110e-05 -1.8387327e-01 -4.0893593e-01
   3.1475897e-03  1.8851172e-04 -1.2871579e-03  7.6634495e-04
   9.4295020e-04 -5.4616766e-04  2.6879899e-04]]
state [[ 1.0061693e+00  1.9831248e-01 -9.1238514e-02 -3.0101027e+00
  -6.3133077e-05  1.4536556e-05  1.5833250e-03 -6.0891857e-06
  -2.6832437e-03  5.5986809e-05 -1.7436032e-01 -4.0892392e-01
   3.7045702e-03 -2.5843594e-05 -3.7412051e-04  2.0102237e-03
  -5.0242066e-05 -2.3653822e-03 -1.5165789e-04]]
state [[ 1.00616932e+00  1.91393554e-01 -1.50125355e-01 -3.03319240e+00
  -7.19745949e-05  1.83832071e-05  1.84270227e-03 -1.28839265e-05
  -3.11847823e-03  6.05626410e-05 -1.78252593e-01 -4.09206927e-01
   2.47974810e-03  9.27111905e-05  2.90117634e-04  2.15612105e-04
  -1.24976493e-03  3.44021901e-05 -1.25592254e-04]]
state [[ 1.0061693e+00  2.2704199e-01 -1.9953167e-01 -3.0614383e+00
  -7.7906509e-05  1.6061025e-05  1.9813923e-03 -1.0041479e-05
  -3.3544104e-03  5.9151585e-05 -1.8317126e-01 -4.1996601e-01
   5.3487304e-03  3.0641662e-04  4.5603952e-05  1.7524346e-03
  -4.8077089e-04 -1.2394929e-03 -6.3831359e-04]]
state [[ 1.0061775e+00  2.3000397e-01 -1.6683997e-01 -2.9453788e+00
  -8.7772096e-05  1.7955839e-05  2.2300652e-03 -2.9000562e-06
  -3.7758825e-03  6.9685455e-05 -1.4491123e-01 -4.1342235e-01
   2.8600940e-03  1.1849917e-04 -2.6865848e-04  9.9057697e-06
   7.0132106e-04 -3.7014723e-04 -1.8493439e-03]]
state [[ 1.0061775e+00  2.1936253e-01 -9.6598767e-02 -2.9600811e+00
  -9.5792711e-05  2.0030226e-05  2.4016094e-03 -3.2312651e-06
  -4.0659872e-03  7.9684163e-05 -1.4471228e-01 -4.1135651e-01
   2.4936309e-03 -4.4163471e-04  6.1551873e-05  2.0477911e-03
   2.7897471e-04 -3.1609277e-03  1.5270735e-04]]
state [[ 1.0061775e+00  3.2780629e-01 -1.8378717e-01 -3.0139117e+00
  -1.0465846e-04 -5.3639460e-06  2.6399158e-03 -1.6389722e-06
  -4.4702808e-03  3.9695824e-05 -1.4702210e-01 -4.2966396e-01
  -1.2857247e-03 -1.7516696e-04 -1.5981266e-03  2.1460084e-03
   6.7104172e-04 -2.3496591e-03 -8.3387218e-04]]
state [[ 1.0061775e+00  3.9071023e-01 -7.2836590e-01 -3.0322831e+00
  -5.3033014e-05 -1.1755053e-03  2.9930624e-03  2.9950289e-04
  -5.0491565e-03 -1.7816031e-04 -1.6395035e-01 -4.3390805e-01
   2.5097389e-02  2.1636613e-04 -1.7033175e-02  4.7991965e-03
   9.7743114e-03 -5.9742639e-03  3.1822085e-02]]
state [[ 1.0061775e+00  3.6600235e-01 -8.5664958e-01 -2.9430265e+00
   4.5389068e-05 -3.3084997e-03  3.3782183e-03  1.5314231e-03
  -5.6262501e-03  2.6537322e-03 -6.6776745e-02 -4.1880253e-01
   7.0215188e-02  2.8910136e-04 -2.2236966e-02  4.8081526e-03
   1.5884446e-02 -5.9392657e-03  4.3683670e-02]]
state [[ 1.0061775e+00  4.7225040e-01 -1.1289014e+00 -2.9780505e+00
   1.6731990e-04 -5.9959064e-03  3.6842450e-03  2.3351884e-03
  -6.1114952e-03  5.7838932e-03 -9.4572991e-02 -4.5584738e-01
   1.0236926e-01  1.1096736e-03 -2.1267567e-02  2.3591258e-03
   1.4765180e-02 -1.0076532e-03  2.8894218e-02]]
state [[ 1.0061775e+00  4.8459432e-01 -1.1473585e+00 -3.0235405e+00
   2.5230634e-04 -8.0489973e-03  3.8889335e-03  3.0263541e-03
  -6.4374278e-03  6.7841280e-03 -8.4557094e-02 -4.7204009e-01
   1.2153429e-01  9.1981579e-04 -2.0125203e-02  2.7795881e-03
   1.4379151e-02 -1.7106800e-03  1.7722538e-02]]
state [[ 1.0061775e+00  4.5355359e-01 -1.2447239e+00 -3.0195136e+00
   3.8333959e-04 -1.1708848e-02  4.2352062e-03  4.0416187e-03
  -7.0113358e-03  8.0539016e-03 -1.1557055e-01 -4.6731323e-01
   1.4273539e-01  1.6697691e-03 -2.2185944e-02  2.0023178e-04
   1.2275730e-02  2.5763449e-03  1.7428361e-02]]

=== Infos内容 ===
成功标记: 0
左干预状态: 0
右干预状态: 0
checkpkl.py:61: UserWarning: Matplotlib is currently using agg, which is a non-GUI backend, so cannot show the figure.
  plt.show()

=== 动作统计 ===
X范围: [-0.681, 0.999]
Y范围: [-0.936, 1.076]
Z范围: [-1.088, 1.083]
夹爪操作次数: 10

动作内容: [x,y,z: [0. 0. 0.] | euler: [0. 0. 0.] | gripper: 0.0]
state [[ 1.00616932e+00  1.73685193e-01 -1.06941864e-01 -2.99924660e+00
  -4.84556585e-05  9.05867364e-06  1.19033316e-03 -5.74266232e-06
  -2.01930129e-03  3.67006578e-05 -1.82113469e-01 -4.02339518e-01
   3.18112643e-03 -6.98470918e-04  4.26885454e-05  1.64750253e-03
  -8.39349057e-04 -3.15056206e-03 -2.77787261e-03]]
动作内容: [x,y,z: [0. 0. 0.] | euler: [0. 0. 0.] | gripper: 0.0]
state [[ 1.0061693e+00  1.9585545e-01 -1.4657778e-01 -3.0118053e+00
  -5.8223672e-05  1.0926628e-05  1.4819637e-03 -4.1498752e-06
  -2.5083907e-03  5.1611110e-05 -1.8387327e-01 -4.0893593e-01
   3.1475897e-03  1.8851172e-04 -1.2871579e-03  7.6634495e-04
   9.4295020e-04 -5.4616766e-04  2.6879899e-04]]
动作内容: [x,y,z: [0. 0. 0.] | euler: [0. 0. 0.] | gripper: 0.0]
state [[ 1.0061693e+00  1.9831248e-01 -9.1238514e-02 -3.0101027e+00
  -6.3133077e-05  1.4536556e-05  1.5833250e-03 -6.0891857e-06
  -2.6832437e-03  5.5986809e-05 -1.7436032e-01 -4.0892392e-01
   3.7045702e-03 -2.5843594e-05 -3.7412051e-04  2.0102237e-03
  -5.0242066e-05 -2.3653822e-03 -1.5165789e-04]]
动作内容: [x,y,z: [-0.000463    0.00021102  0.0171353 ] | euler: [0.08796947 0.00982839 0.00225592] | gripper: 0.0]
state [[ 1.00616932e+00  1.91393554e-01 -1.50125355e-01 -3.03319240e+00
  -7.19745949e-05  1.83832071e-05  1.84270227e-03 -1.28839265e-05
  -3.11847823e-03  6.05626410e-05 -1.78252593e-01 -4.09206927e-01
   2.47974810e-03  9.27111905e-05  2.90117634e-04  2.15612105e-04
  -1.24976493e-03  3.44021901e-05 -1.25592254e-04]]
动作内容: [x,y,z: [0. 0. 0.] | euler: [0. 0. 0.] | gripper: 0.0]
state [[ 1.0061693e+00  2.2704199e-01 -1.9953167e-01 -3.0614383e+00
  -7.7906509e-05  1.6061025e-05  1.9813923e-03 -1.0041479e-05
  -3.3544104e-03  5.9151585e-05 -1.8317126e-01 -4.1996601e-01
   5.3487304e-03  3.0641662e-04  4.5603952e-05  1.7524346e-03
  -4.8077089e-04 -1.2394929e-03 -6.3831359e-04]]
动作内容: [x,y,z: [0. 0. 0.] | euler: [0. 0. 0.] | gripper: 0.0]
state [[ 1.0061775e+00  2.3000397e-01 -1.6683997e-01 -2.9453788e+00
  -8.7772096e-05  1.7955839e-05  2.2300652e-03 -2.9000562e-06
  -3.7758825e-03  6.9685455e-05 -1.4491123e-01 -4.1342235e-01
   2.8600940e-03  1.1849917e-04 -2.6865848e-04  9.9057697e-06
   7.0132106e-04 -3.7014723e-04 -1.8493439e-03]]
动作内容: [x,y,z: [ 0.02686106 -0.28836639  0.02712108] | euler: [0.0889038  0.02462001 0.17372221] | gripper: 0.0]
state [[ 1.0061775e+00  2.1936253e-01 -9.6598767e-02 -2.9600811e+00
  -9.5792711e-05  2.0030226e-05  2.4016094e-03 -3.2312651e-06
  -4.0659872e-03  7.9684163e-05 -1.4471228e-01 -4.1135651e-01
   2.4936309e-03 -4.4163471e-04  6.1551873e-05  2.0477911e-03
   2.7897471e-04 -3.1609277e-03  1.5270735e-04]]
动作内容: [x,y,z: [-0.00365549 -0.7137696   0.04586148] | euler: [0.25385284 0.02949481 0.69447921] | gripper: 0.0]
state [[ 1.0061775e+00  3.2780629e-01 -1.8378717e-01 -3.0139117e+00
  -1.0465846e-04 -5.3639460e-06  2.6399158e-03 -1.6389722e-06
  -4.4702808e-03  3.9695824e-05 -1.4702210e-01 -4.2966396e-01
  -1.2857247e-03 -1.7516696e-04 -1.5981266e-03  2.1460084e-03
   6.7104172e-04 -2.3496591e-03 -8.3387218e-04]]
动作内容: [x,y,z: [-0.00292821 -0.82278734  0.01030986] | euler: [0.2954051  0.00811243 0.73132099] | gripper: 0.0]
state [[ 1.0061775e+00  3.9071023e-01 -7.2836590e-01 -3.0322831e+00
  -5.3033014e-05 -1.1755053e-03  2.9930624e-03  2.9950289e-04
  -5.0491565e-03 -1.7816031e-04 -1.6395035e-01 -4.3390805e-01
   2.5097389e-02  2.1636613e-04 -1.7033175e-02  4.7991965e-03
   9.7743114e-03 -5.9742639e-03  3.1822085e-02]]
动作内容: [x,y,z: [-0.00547125 -0.85704402  0.01181067] | euler: [0.29025681 0.00783213 0.70280129] | gripper: 0.0]
state [[ 1.0061775e+00  3.6600235e-01 -8.5664958e-01 -2.9430265e+00
   4.5389068e-05 -3.3084997e-03  3.3782183e-03  1.5314231e-03
  -5.6262501e-03  2.6537322e-03 -6.6776745e-02 -4.1880253e-01
   7.0215188e-02  2.8910136e-04 -2.2236966e-02  4.8081526e-03
   1.5884446e-02 -5.9392657e-03  4.3683670e-02]]
动作内容: [x,y,z: [-0.00836612 -0.87986637  0.01285235] | euler: [0.2698139  0.00734765 0.6786499 ] | gripper: 0.0]
state [[ 1.0061775e+00  4.7225040e-01 -1.1289014e+00 -2.9780505e+00
   1.6731990e-04 -5.9959064e-03  3.6842450e-03  2.3351884e-03
  -6.1114952e-03  5.7838932e-03 -9.4572991e-02 -4.5584738e-01
   1.0236926e-01  1.1096736e-03 -2.1267567e-02  2.3591258e-03
   1.4765180e-02 -1.0076532e-03  2.8894218e-02]]
动作内容: [x,y,z: [-0.00951275 -0.90555823  0.01386242] | euler: [0.27868326 0.00717241 0.65977539] | gripper: 0.0]
state [[ 1.0061775e+00  4.8459432e-01 -1.1473585e+00 -3.0235405e+00
   2.5230634e-04 -8.0489973e-03  3.8889335e-03  3.0263541e-03
  -6.4374278e-03  6.7841280e-03 -8.4557094e-02 -4.7204009e-01
   1.2153429e-01  9.1981579e-04 -2.0125203e-02  2.7795881e-03
   1.4379151e-02 -1.7106800e-03  1.7722538e-02]]
动作内容: [x,y,z: [-0.01065624 -0.90553074  0.01479526] | euler: [0.27866453 0.00759339 0.66545422] | gripper: 0.0]
state [[ 1.0061775e+00  4.5355359e-01 -1.2447239e+00 -3.0195136e+00
   3.8333959e-04 -1.1708848e-02  4.2352062e-03  4.0416187e-03
  -7.0113358e-03  8.0539016e-03 -1.1557055e-01 -4.6731323e-01
   1.4273539e-01  1.6697691e-03 -2.2185944e-02  2.0023178e-04
   1.2275730e-02  2.5763449e-03  1.7428361e-02]]
所以上面那段你数据中为何相邻两帧的state中前六位的差值和action不一致？