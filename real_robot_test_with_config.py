#!/usr/bin/env python3
"""
真机测试脚本（支持配置文件）- 整合3D Diffuser Actor模型与真实机器人
"""

import os
import sys
import json
import time
import queue
import threading
import subprocess
import argparse
from pathlib import Path
from typing import Dict, Any, Optional, Tuple

import cv2
import numpy as np
import torch
from scipy.spatial.transform import Rotation

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from diffuser_actor import DiffuserActor


class VideoCapture:
    """线程安全的视频捕获类"""
    
    def __init__(self, cap, name=None):
        if name is None:
            name = cap.name
        self.name = name
        self.q = queue.Queue()
        self.cap = cap
        self.t = threading.Thread(target=self._reader)
        self.t.daemon = False
        self.enable = True
        self.t.start()

    def _reader(self):
        while self.enable:
            ret, frame = self.cap.read()
            if not ret:
                break
            if not self.q.empty():
                try:
                    self.q.get_nowait()  # 丢弃未处理的帧
                except queue.Empty:
                    pass
            self.q.put(frame)

    def read(self):
        return self.q.get(timeout=5)

    def close(self):
        self.enable = False
        self.t.join()
        self.cap.close()


class RSCapture:
    """RealSense相机捕获类"""
    
    def get_device_serial_numbers(self):
        import pyrealsense2 as rs
        devices = rs.context().devices
        return [d.get_info(rs.camera_info.serial_number) for d in devices]

    def __init__(self, name, serial_number, dim=(640, 480), fps=15, depth=True, exposure=40000):
        import pyrealsense2 as rs
        self.name = name
        assert serial_number in self.get_device_serial_numbers()
        self.serial_number = serial_number
        self.depth = depth
        self.pipe = rs.pipeline()
        self.cfg = rs.config()
        self.cfg.enable_device(self.serial_number)
        self.cfg.enable_stream(rs.stream.color, dim[0], dim[1], rs.format.bgr8, fps)
        if self.depth:
            self.cfg.enable_stream(rs.stream.depth, dim[0], dim[1], rs.format.z16, fps)
        self.profile = self.pipe.start(self.cfg)
        self.s = self.profile.get_device().query_sensors()[0]
        self.s.set_option(rs.option.exposure, exposure)

        # 创建对齐对象
        align_to = rs.stream.color
        self.align = rs.align(align_to)

    def read(self):
        import pyrealsense2 as rs
        frames = self.pipe.wait_for_frames()
        aligned_frames = self.align.process(frames)
        color_frame = aligned_frames.get_color_frame()
        if self.depth:
            depth_frame = aligned_frames.get_depth_frame()

        if color_frame.is_video_frame():
            image = np.asarray(color_frame.get_data())
            if self.depth and depth_frame.is_depth_frame():
                depth = np.expand_dims(np.asarray(depth_frame.get_data()), axis=2)
                return True, np.concatenate((image, depth), axis=-1)
            else:
                return True, image
        else:
            return False, None

    def close(self):
        self.pipe.stop()
        self.cfg.disable_all_streams()


class RobotController:
    """机器人控制器"""
    
    def __init__(self, url: str, action_scale: list, gripper_mode: str):
        self.url = url
        self.ACTION_SCALE = action_scale
        self.gripper_mode = gripper_mode
        
    def get_currpos(self) -> np.ndarray:
        """获取机器人当前状态"""
        curl_command = [
            "curl",
            "-X", "POST", 
            self.url + "getstate",
            "-H", "Content-Type: application/json",
            "-s"
        ]
        
        result = subprocess.run(curl_command, capture_output=True, text=True)
        
        if result.returncode != 0:
            raise Exception("Failed to get robot state. Curl command failed.")
        
        ps = json.loads(result.stdout)
        currpos = np.array(ps["pose"])
        return currpos

    def send_gripper_command(self, pos: float):
        """发送夹爪命令"""
        if self.gripper_mode == "binary":
            if pos <= -0.5:  # 关闭夹爪
                curl_command = [
                    "curl",
                    "-X", "POST", 
                    self.url + "close_gripper",
                    "-H", "Content-Type: application/json"
                ]
                subprocess.run(curl_command)
            elif pos >= 0.5:  # 打开夹爪
                curl_command = [
                    "curl",
                    "-X", "POST", 
                    self.url + "open_gripper",
                    "-H", "Content-Type: application/json"
                ]
                subprocess.run(curl_command)
            else: 
                return
        elif self.gripper_mode == "continuous":
            raise NotImplementedError("Continuous gripper control is optional")

    def send_pos_command(self, pos: np.ndarray):
        """发送位置命令"""
        arr = np.array(pos).astype(np.float32)
        data = {"arr": arr.tolist()}
        json_data = json.dumps(data)
        
        curl_command = [
            "curl",
            "-X", "POST", 
            self.url + "pose",
            "-H", "Content-Type: application/json",
            "-d", json_data
        ]
        
        subprocess.run(curl_command)

    def step(self, action: np.ndarray):
        """执行动作"""
        xyz_delta = action[:3]
        currpos = self.get_currpos()

        nextpos = currpos.copy()
        nextpos[:3] = nextpos[:3] + xyz_delta * self.ACTION_SCALE[0]

        # 从动作获取方向
        nextpos[3:] = (
            Rotation.from_euler("xyz", action[3:6] * self.ACTION_SCALE[1])
            * Rotation.from_quat(currpos[3:])
        ).as_quat()

        gripper_action = action[6] * self.ACTION_SCALE[2]

        self.send_gripper_command(gripper_action)
        self.send_pos_command(nextpos)


class RealRobotTester:
    """真机测试器"""
    
    def __init__(self, config_path: str):
        self.config = self._load_config(config_path)
        self.device = torch.device(self.config["hardware"]["device"])
        
        # 初始化模型
        self.model = self._load_model()
        self.model.eval()
        
        # 初始化相机
        self.camera = self._init_camera()
        
        # 初始化机器人控制器
        robot_config = self.config["robot"]
        self.robot = RobotController(
            self.config["hardware"]["robot_url"],
            robot_config["action_scale"],
            robot_config["gripper_mode"]
        )
        
        # 图像预处理参数
        testing_config = self.config["testing"]
        self.image_crop = lambda img: img[
            testing_config["image_crop"][0]:testing_config["image_crop"][1],
            testing_config["image_crop"][2]:testing_config["image_crop"][3]
        ]
        self.target_shape = tuple(testing_config["target_shape"])
        
        print("真机测试器初始化完成")

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        with open(config_path, 'r') as f:
            config = json.load(f)
        return config

    def _load_model(self) -> DiffuserActor:
        """加载预训练模型"""
        model_config = self.config["model"]
        print(f"加载模型: {model_config['checkpoint']}")
        
        # 加载位置边界
        with open(model_config["gripper_loc_bounds"], 'r') as f:
            gripper_loc_bounds = json.load(f)
        
        # 创建模型
        model = DiffuserActor(
            backbone=model_config["backbone"],
            image_size=tuple(int(x) for x in model_config["image_size"].split(",")),
            embedding_dim=model_config["embedding_dim"],
            num_vis_ins_attn_layers=model_config["num_vis_ins_attn_layers"],
            use_instruction=bool(model_config["use_instruction"]),
            fps_subsampling_factor=model_config["fps_subsampling_factor"],
            gripper_loc_bounds=gripper_loc_bounds,
            rotation_parametrization=model_config["rotation_parametrization"],
            quaternion_format=model_config["quaternion_format"],
            diffusion_timesteps=model_config["diffusion_timesteps"],
            nhist=model_config["num_history"],
            relative=bool(model_config["relative_action"]),
            lang_enhanced=bool(model_config["lang_enhanced"])
        )
        
        # 加载权重
        checkpoint = torch.load(model_config["checkpoint"], map_location="cpu")
        if "weight" in checkpoint:
            state_dict = checkpoint["weight"]
            # 移除模块前缀
            model_weights = {}
            for key in state_dict:
                if key.startswith("module."):
                    _key = key[7:]
                else:
                    _key = key
                model_weights[_key] = state_dict[key]
            model.load_state_dict(model_weights)
        else:
            model.load_state_dict(checkpoint)
        
        model = model.to(self.device)
        return model

    def _init_camera(self) -> VideoCapture:
        """初始化相机"""
        hardware_config = self.config["hardware"]
        print(f"初始化RealSense相机: {hardware_config['camera_serial']}")
        
        rs_cap = RSCapture(
            name="front",
            serial_number=hardware_config["camera_serial"],
            dim=tuple(hardware_config["camera_dim"]),
            fps=hardware_config["camera_fps"],
            exposure=hardware_config["camera_exposure"]
        )
        
        return VideoCapture(rs_cap)

    def _preprocess_observation(self, frame: np.ndarray) -> Dict[str, torch.Tensor]:
        """预处理观测数据"""
        # 分离RGB和深度
        rgb = frame[..., :3].astype(np.uint8)
        depth = frame[..., 3]
        
        # 裁剪图像
        cropped_rgb = self.image_crop(rgb)
        cropped_depth = self.image_crop(depth)
        
        # 调整大小
        resized_rgb = cv2.resize(cropped_rgb, self.target_shape)
        resized_depth = cv2.resize(cropped_depth, self.target_shape).astype(np.float32)
        
        # 转换为张量格式
        rgb_tensor = torch.from_numpy(resized_rgb[..., ::-1]).float() / 255.0  # BGR to RGB, [0,1]
        depth_tensor = torch.from_numpy(resized_depth).float()
        
        # 添加批次和相机维度
        rgb_tensor = rgb_tensor.unsqueeze(0).unsqueeze(0)  # (1, 1, H, W, 3)
        depth_tensor = depth_tensor.unsqueeze(0).unsqueeze(0).unsqueeze(-1)  # (1, 1, H, W, 1)
        
        # 转换为模型期望的格式
        rgb_tensor = rgb_tensor.permute(0, 1, 4, 2, 3)  # (1, 1, 3, H, W)
        depth_tensor = depth_tensor.permute(0, 1, 4, 2, 3)  # (1, 1, 3, H, W)
        
        return {
            "rgb": rgb_tensor.to(self.device),
            "depth": depth_tensor.to(self.device)
        }

    def _get_robot_state(self) -> torch.Tensor:
        """获取机器人状态"""
        currpos = self.robot.get_currpos()
        
        # 转换为张量格式
        gripper_state = torch.from_numpy(currpos).float()
        gripper_state = gripper_state.unsqueeze(0).unsqueeze(0)  # (1, 1, 7)
        
        return gripper_state.to(self.device)

    def _create_dummy_instruction(self) -> torch.Tensor:
        """创建虚拟指令（当不使用指令时）"""
        model_config = self.config["model"]
        if model_config["use_instruction"]:
            raise NotImplementedError("指令编码需要实现")
        else:
            # 创建零张量作为虚拟指令
            return torch.zeros(1, 53, 512).to(self.device)

    def _predict_action(self, observation: Dict[str, torch.Tensor], 
                       robot_state: torch.Tensor) -> np.ndarray:
        """预测动作"""
        with torch.no_grad():
            # 创建轨迹掩码
            model_config = self.config["model"]
            trajectory_mask = torch.full(
                [1, model_config["interpolation_length"] - 1], False
            ).to(self.device)
            
            # 创建虚拟轨迹
            fake_trajectory = torch.full(
                [1, model_config["interpolation_length"] - 1, 7], 0
            ).to(self.device)
            
            # 创建虚拟指令
            instruction = self._create_dummy_instruction()
            
            # 模型推理
            trajectory = self.model(
                fake_trajectory,
                trajectory_mask,
                observation["rgb"],
                observation["depth"],
                instruction,
                robot_state,
                run_inference=True
            )
            
            # 提取第一个动作
            action = trajectory[0, 0].cpu().numpy()
            return action

    def _check_safety_limits(self, action: np.ndarray) -> bool:
        """检查安全限制"""
        safety_config = self.config["safety"]
        
        # 检查位置变化
        pos_change = np.linalg.norm(action[:3])
        if pos_change > safety_config["max_position_change"]:
            print(f"警告: 位置变化过大: {pos_change:.3f}")
            return False
        
        # 检查旋转变化
        rot_change = np.linalg.norm(action[3:6])
        if rot_change > safety_config["max_rotation_change"]:
            print(f"警告: 旋转变化过大: {rot_change:.3f}")
            return False
        
        return True

    def run_test(self):
        """运行测试"""
        print("开始真机测试...")
        print(f"最大步数: {self.config['testing']['max_steps']}")
        print(f"动作延迟: {self.config['testing']['action_delay']}")
        print(f"安全限制: 位置变化 < {self.config['safety']['max_position_change']}, "
              f"旋转变化 < {self.config['safety']['max_rotation_change']}")
        print("按 'q' 键紧急停止")
        print("")
        
        try:
            step_count = 0
            max_steps = self.config["testing"]["max_steps"]
            
            while step_count < max_steps:
                print(f"\n步骤 {step_count + 1}/{max_steps}")
                
                # 1. 获取观测
                print("获取相机观测...")
                ret, frame = self.camera.read()
                if not ret:
                    print("警告：无法获取相机帧")
                    time.sleep(0.1)
                    continue
                
                # 2. 预处理观测
                print("预处理观测数据...")
                observation = self._preprocess_observation(frame)
                
                # 3. 获取机器人状态
                print("获取机器人状态...")
                robot_state = self._get_robot_state()
                
                # 4. 预测动作
                print("模型推理...")
                action = self._predict_action(observation, robot_state)
                
                print(f"预测动作: {action}")
                
                # 5. 安全检查
                if not self._check_safety_limits(action):
                    print("动作超出安全限制，跳过执行")
                    step_count += 1
                    continue
                
                # 6. 执行动作
                print("执行动作...")
                self.robot.step(action)
                
                # 7. 等待动作完成
                time.sleep(self.config["testing"]["action_delay"])
                
                step_count += 1
                
                # 检查是否完成任务（这里需要根据具体任务实现）
                if self._check_task_completion():
                    print("任务完成！")
                    break
                    
        except KeyboardInterrupt:
            print("\n测试被用户中断")
        except Exception as e:
            print(f"测试过程中出现错误: {e}")
            raise
        finally:
            self.cleanup()

    def _check_task_completion(self) -> bool:
        """检查任务是否完成（需要根据具体任务实现）"""
        # 这里需要根据具体任务实现完成条件检查
        return False

    def cleanup(self):
        """清理资源"""
        print("清理资源...")
        if hasattr(self, 'camera'):
            self.camera.close()


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="真机测试脚本（支持配置文件）")
    parser.add_argument('--config', type=str, default='real_robot_config.json',
                      help='配置文件路径')
    return parser.parse_args()


def main():
    """主函数"""
    args = parse_arguments()
    
    # 检查配置文件是否存在
    if not os.path.exists(args.config):
        print(f"错误: 配置文件不存在: {args.config}")
        print("请创建配置文件或指定正确的配置文件路径")
        return
    
    # 创建测试器
    tester = RealRobotTester(args.config)
    
    # 运行测试
    tester.run_test()


if __name__ == "__main__":
    main() 