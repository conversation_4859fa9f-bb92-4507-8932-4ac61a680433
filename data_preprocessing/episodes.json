{"max_episode_length": {"basketball_in_hoop": 4, "beat_the_buzz": 4, "block_pyramid": 35, "change_channel": 8, "close_drawer": 2, "close_box": 5, "close_jar": 7, "close_grill": 4, "change_clock": 4, "close_microwave": 2, "close_laptop_lid": 4, "close_door": 3, "close_fridge": 2, "empty_dishwasher": 13, "get_ice_from_fridge": 5, "hang_frame_on_hanger": 4, "hit_ball_with_queue": 7, "hockey": 7, "insert_onto_square_peg": 5, "insert_usb_in_computer": 5, "lamp_off": 2, "lamp_on": 4, "light_bulb_out": 5, "light_bulb_in": 7, "lift_numbered_block": 3, "move_hanger": 5, "meat_off_grill": 5, "meat_on_grill": 5, "open_door": 4, "open_box": 3, "open_drawer": 3, "open_fridge": 3, "open_jar": 7, "open_grill": 3, "open_microwave": 3, "open_oven": 5, "open_window": 4, "open_wine_bottle": 3, "reach_target": 1, "reach_and_drag": 6, "remove_cups": 6, "phone_on_base": 5, "pour_from_cup_to_cup": 6, "pick_and_lift": 4, "pick_and_lift_small": 4, "pick_up_cup": 3, "place_shape_in_shape_sorter": 7, "place_hanger_on_rack": 6, "place_cups": 23, "play_jenga": 3, "plug_charger_in_power_supply": 6, "press_switch": 2, "put_books_on_bookshelf": 5, "put_bottle_in_fridge": 9, "put_knife_on_chopping_board": 4, "put_groceries_in_cupboard": 6, "put_knife_in_knife_block": 5, "put_item_in_drawer": 12, "put_money_in_safe": 5, "put_plate_in_colored_dish_rack": 5, "put_rubbish_in_bin": 4, "put_tray_in_oven": 12, "put_toilet_roll_on_stand": 5, "put_shoes_in_box": 13, "put_umbrella_in_umbrella_stand": 4, "stack_wine": 5, "stack_blocks": 23, "stack_chairs": 11, "stack_cups": 10, "straighten_rope": 7, "scoop_with_spatula": 4, "screw_nail": 8, "setup_checkers": 6, "setup_chess": 5, "slide_block_to_target": 2, "slide_block_to_color_target": 5, "slide_cabinet_open_and_place_cups": 9, "solve_puzzle": 7, "sweep_to_dustpan": 5, "sweep_to_dustpan_of_size": 5, "push_button": 2, "push_buttons": 6, "push_repeated_buttons": 8, "take_money_out_safe": 4, "take_umbrella_out_of_umbrella_stand": 3, "take_cup_out_from_cabinet": 7, "take_frame_off_hanger": 4, "take_item_out_of_drawer": 9, "take_lid_off_saucepan": 3, "take_off_weighing_scales": 7, "take_plate_off_colored_dish_rack": 5, "take_shoes_out_of_box": 15, "take_toilet_roll_off_stand": 4, "take_tray_out_of_oven": 10, "take_usb_out_of_computer": 2, "toilet_seat_up": 3, "toilet_seat_down": 4, "tower": 29, "tower2": 17, "tower3": 6, "tower4": 11, "tower_sim2real": 12, "turn_oven_on": 3, "turn_tap": 2, "tv_on": 8, "unplug_charger": 2, "water_plants": 5, "wipe_desk": 8, "place_wine_at_rack_location": 5}, "variable_length": ["push_buttons", "push_repeated_buttons", "close_jar", "open_jar", "hockey", "hit_ball_with_queue", "lamp_on", "put_tray_in_oven", "solve_puzzle", "sweep_to_dustpan", "sweep_to_dustpan_of_size", "take_off_weighing_scales", "take_tray_out_of_oven", "tower", "tower2", "tower3", "tower4", "stack_blocks", "slide_block_to_color_target", "place_cups", "place_shape_in_shape_sorter", "put_groceries_in_cupboard", "slide_cabinet_open_and_place_cups", "wipe_desk", "setup_checkers", "water_plants", "screw_nail", "plug_charger_in_power_supply", "place_hanger_on_rack", "open_oven", "take_shoes_out_of_box"], "broken": ["empty_container", "put_all_groceries_in_cupboard", "set_the_table", "slide_cabinet_open", "weighing_scales"]}