# 3D Diffuser Actor 模型规模与显存分析

## 训练配置参数
- **批次大小 (B)**: 16
- **嵌入维度 (C)**: 120  
- **历史帧数**: 3
- **扩散时间步**: 100
- **图像尺寸**: 256×256 (默认)
- **相机数量**: 1 (front)
- **GPU数量**: 1

## 模型架构组件

### 1. 视觉编码器 (Encoder)
**CLIP ResNet-50 骨干网络**
- 参数量: ~38M (冻结，不参与训练)
- 输入: (B, ncam, 3, 256, 256)
- 输出特征层级: res1-res5

**特征金字塔网络 (FPN)**
- 输入通道: [64, 256, 512, 1024, 2048]
- 输出维度: 120
- 参数量: ~2.5M

**位置编码与注意力模块**
- 3D旋转位置编码: RotaryPositionEncoding3D(120)
- 当前夹爪嵌入: nn.Embedding(3, 120) 
- 夹爪上下文注意力: FFWRelativeCrossAttentionModule(120, 8, 3层)
- 视觉-语言注意力: ParallelAttention(2层, 120维, 8头)

### 2. 扩散预测头 (DiffusionHead)
**轨迹编码器**
- 轨迹编码: nn.Linear(9, 120)
- 时间嵌入: SinusoidalPosEmb + 2×Linear(120)
- 当前夹爪嵌入: Linear(360, 120) + ReLU + Linear(120, 120)

**注意力层**
- 轨迹-语言交叉注意力: ParallelAttention(1层)
- 主要交叉注意力: FFWRelativeCrossAttentionModule(120, 8, 2层)
- 共享自注意力: FFWRelativeSelfAttentionModule(120, 8, 4层)

**输出预测器**
- 旋转预测: 
  - 投影层: Linear(120, 120)
  - 自注意力: FFWRelativeSelfAttentionModule(120, 8, 2层)  
  - 预测器: Linear(120, 120) + ReLU + Linear(120, 6) [6D旋转]
- 位置预测:
  - 投影层: Linear(120, 120)
  - 自注意力: FFWRelativeSelfAttentionModule(120, 8, 2层)
  - 预测器: Linear(120, 120) + ReLU + Linear(120, 3)
- 夹爪开合: Linear(120, 120) + ReLU + Linear(120, 1)

### 3. 扩散调度器
- 位置噪声调度器: DDPMScheduler (scaled_linear)
- 旋转噪声调度器: DDPMScheduler (squaredcos_cap_v2)

## 参数量估算

### 可训练参数
1. **特征金字塔网络**: ~2.5M
2. **编码器模块**: 
   - 位置编码: ~0.1M
   - 夹爪嵌入: ~0.4K  
   - 注意力模块: ~3.5M
3. **扩散预测头**:
   - 编码器: ~0.5M
   - 注意力层: ~8M
   - 输出预测器: ~1.5M

**总可训练参数**: ~16M

### 总参数 (包含冻结)
- CLIP骨干: 38M (冻结)
- 可训练部分: 16M
- **总计**: ~54M

## 显存占用估算

### 模型权重
- FP32: 54M × 4 bytes = 216MB
- FP16混合精度: 54M × 2 bytes = 108MB

### 前向传播激活值 (批次大小=16)
**输入数据**:
- RGB图像: 16 × 1 × 3 × 256 × 256 × 4 bytes = 50MB
- 点云数据: 16 × 1 × 3 × 256 × 256 × 4 bytes = 50MB  
- 轨迹数据: 16 × seq_len × 9 × 4 bytes ≈ 5MB

**中间激活**:
- CLIP特征: 16 × 1 × 2048 × 8 × 8 × 4 bytes = 33MB
- FPN特征金字塔: ~80MB
- 注意力激活: ~150MB
- 扩散预测激活: ~100MB

**激活值总计**: ~470MB

### 反向传播梯度
- 梯度存储: 16M × 4 bytes = 64MB
- 优化器状态 (Adam): 16M × 8 bytes = 128MB

### 总显存需求
- **FP32训练**: 216MB + 470MB + 192MB = ~880MB
- **FP16混合精度**: 108MB + 235MB + 96MB = ~440MB

### 实际显存占用 (考虑开销)
- **FP32**: ~1.2GB (包含PyTorch开销)
- **FP16**: ~0.8GB (包含PyTorch开销)

## 结论

该训练脚本使用的3D Diffuser Actor模型规模适中：
- **模型参数**: 54M (16M可训练)
- **预估显存**: FP16混合精度下约0.8GB，FP32下约1.2GB
- **训练效率**: 单GPU即可训练，显存需求较低
- **模型复杂度**: 中等规模，适合机器人操作任务

这是一个相对轻量级的模型，在现代GPU上训练效率较高。
