#!/usr/bin/env python3
"""
修复测试图像保存问题的脚本
主要解决RGB通道数量问题（RGB+attention -> RGB）
"""

import os
import sys
import torch
import numpy as np
import random
from pathlib import Path
from PIL import Image

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def save_rgb_image(rgb_tensor, save_path, title=""):
    """保存RGB图像tensor为图片文件"""
    if rgb_tensor is None:
        print(f"警告: 无法保存图像 {save_path} - 数据为None")
        return
        
    try:
        print(f"保存图像 {save_path}, 原始形状: {rgb_tensor.shape}")
        
        if len(rgb_tensor.shape) == 4:  # (batch, channel, height, width)
            rgb_tensor = rgb_tensor[0]
        if len(rgb_tensor.shape) == 3:  # (channel, height, width)
            rgb_np = rgb_tensor.permute(1, 2, 0).cpu().numpy()
        else:
            rgb_np = rgb_tensor.cpu().numpy()
        
        print(f"  转换后形状: {rgb_np.shape}")
        print(f"  数据范围: [{rgb_np.min():.3f}, {rgb_np.max():.3f}]")
        
        # 确保值在[0,1]范围内
        rgb_np = np.clip(rgb_np, 0, 1)
        
        # 转换为[0,255]
        rgb_np = (rgb_np * 255).astype(np.uint8)
        
        # 保存图像
        img = Image.fromarray(rgb_np)
        img.save(save_path)
        print(f"已保存图像: {save_path}")
    except Exception as e:
        print(f"保存图像 {save_path} 时出错: {e}")

def test_rgb_channels():
    """测试RGB通道处理"""
    print("=== 测试RGB通道处理 ===")
    
    try:
        from utils.utils_with_rlbench import RLBenchEnv
        from utils.common_utils import load_instructions
        
        # 检查测试数据路径
        data_dir_candidates = [
            "data/peract/raw/test/",
            "/data/wangyiwen/3d_diffuser_actor/data/peract/raw/test/"
        ]
        
        data_dir = None
        for candidate in data_dir_candidates:
            if Path(candidate).exists():
                data_dir = candidate
                print(f"找到测试数据路径: {data_dir}")
                break
        
        if data_dir is None:
            print("警告: 找不到测试数据路径")
            return False
        
        # 创建测试环境
        env = RLBenchEnv(
            data_path=data_dir,
            image_size=[256, 256],
            apply_rgb=True,
            apply_pc=True,
            headless=True,
            apply_cameras=("front",),
            collision_checking=False
        )
        
        task_str = "put_toilet_roll_on_stand"
        variation = 0
        demo_id = 0
        
        print(f"\n获取demo: {task_str}, variation={variation}, demo_id={demo_id}")
        
        # 获取demo
        demo = env.get_demo(task_str, variation, demo_id)
        if demo is None or len(demo) == 0:
            print(f"无法获取demo")
            return False
        
        demo_obj = demo[0]
        print(f"demo长度: {len(demo_obj)}")
        
        # 获取第一帧
        first_obs = demo_obj[0]
        current_rgb, current_pcd, current_gripper = env.get_rgb_pcd_gripper_from_obs(first_obs)
        
        # 获取最后一帧
        last_obs = demo_obj[-1]
        goal_rgb, goal_pcd, goal_gripper = env.get_rgb_pcd_gripper_from_obs(last_obs)
        
        print(f"\n原始数据形状:")
        print(f"  当前帧RGB: {current_rgb.shape}")
        print(f"  目标帧RGB: {goal_rgb.shape}")
        
        # 创建输出目录
        output_dir = "debug_goal_frame_output/fixed_test_images"
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存图像 - 方法1：取前3个通道
        print(f"\n方法1：只取前3个通道")
        current_rgb_3ch = current_rgb[0, 0, :3]  # (3, H, W)
        goal_rgb_3ch = goal_rgb[0, 0, :3]  # (3, H, W)
        
        save_rgb_image(current_rgb_3ch, os.path.join(output_dir, "current_frame_3ch.png"))
        save_rgb_image(goal_rgb_3ch, os.path.join(output_dir, "goal_frame_3ch.png"))
        
        # 保存图像 - 方法2：显示所有通道
        print(f"\n方法2：显示所有通道信息")
        current_rgb_all = current_rgb[0, 0]  # (4, H, W)
        goal_rgb_all = goal_rgb[0, 0]  # (4, H, W)
        
        print(f"当前帧各通道统计:")
        for i in range(current_rgb_all.shape[0]):
            ch_data = current_rgb_all[i].cpu().numpy()
            print(f"  通道{i}: 范围[{ch_data.min():.3f}, {ch_data.max():.3f}], 均值{ch_data.mean():.3f}")
        
        print(f"目标帧各通道统计:")
        for i in range(goal_rgb_all.shape[0]):
            ch_data = goal_rgb_all[i].cpu().numpy()
            print(f"  通道{i}: 范围[{ch_data.min():.3f}, {ch_data.max():.3f}], 均值{ch_data.mean():.3f}")
        
        # 保存attention通道
        current_attn = current_rgb_all[3:4]  # (1, H, W) -> (1, H, W)
        goal_attn = goal_rgb_all[3:4]  # (1, H, W) -> (1, H, W)
        
        # 将单通道attention转换为3通道以便保存
        current_attn_3ch = current_attn.repeat(3, 1, 1)  # (3, H, W)
        goal_attn_3ch = goal_attn.repeat(3, 1, 1)  # (3, H, W)
        
        save_rgb_image(current_attn_3ch, os.path.join(output_dir, "current_frame_attention.png"))
        save_rgb_image(goal_attn_3ch, os.path.join(output_dir, "goal_frame_attention.png"))
        
        # 保存信息文件
        with open(os.path.join(output_dir, "channel_analysis.txt"), "w", encoding="utf-8") as f:
            f.write("RGB通道分析结果\n")
            f.write("=" * 30 + "\n\n")
            f.write(f"demo长度: {len(demo_obj)}\n")
            f.write(f"原始RGB形状: {current_rgb.shape}\n")
            f.write(f"包含通道数: {current_rgb.shape[2]}\n\n")
            
            f.write("当前帧各通道统计:\n")
            for i in range(current_rgb_all.shape[0]):
                ch_data = current_rgb_all[i].cpu().numpy()
                f.write(f"  通道{i}: 范围[{ch_data.min():.3f}, {ch_data.max():.3f}], 均值{ch_data.mean():.3f}\n")
            
            f.write("\n目标帧各通道统计:\n")
            for i in range(goal_rgb_all.shape[0]):
                ch_data = goal_rgb_all[i].cpu().numpy()
                f.write(f"  通道{i}: 范围[{ch_data.min():.3f}, {ch_data.max():.3f}], 均值{ch_data.mean():.3f}\n")
            
            f.write("\n结论:\n")
            f.write("- 前3个通道是RGB数据\n")
            f.write("- 第4个通道是attention数据\n")
            f.write("- 保存图像时应该只使用前3个通道\n")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始修复测试图像保存问题...")
    
    # 设置随机种子
    torch.manual_seed(0)
    np.random.seed(0)
    random.seed(0)
    
    # 测试RGB通道处理
    success = test_rgb_channels()
    
    print("\n" + "="*50)
    if success:
        print("修复测试完成！")
        print("现在您知道了问题原因：")
        print("1. 测试数据的RGB包含4个通道（RGB + attention）")
        print("2. 保存图像时需要只取前3个通道")
        print("3. actioner中的目标帧已经正确处理了这个问题")
        print("\n修复后的调试脚本应该可以正常工作了。")
    else:
        print("修复测试失败，请检查路径和环境设置。")
    print("="*50)

if __name__ == "__main__":
    main() 