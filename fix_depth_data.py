#!/usr/bin/env python3
"""
深度数据修复和检查工具
检测和修复异常的深度数据

作者: Assistant
日期: 2025-07-24
"""

import pickle
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import argparse
import os
from typing import Dict, List, Tuple, Optional


def analyze_depth_data(depth: np.ndarray, frame_name: str = "Unknown") -> Dict:
    """
    分析深度数据的统计特性
    
    Args:
        depth (np.ndarray): (H, W) 深度图
        frame_name (str): 帧名称
        
    Returns:
        Dict: 深度数据统计信息
    """
    stats = {
        'frame_name': frame_name,
        'shape': depth.shape,
        'dtype': str(depth.dtype),
        'min_value': float(depth.min()),
        'max_value': float(depth.max()),
        'mean_value': float(depth.mean()),
        'std_value': float(depth.std()),
        'median_value': float(np.median(depth)),
        'zero_pixels': int(np.sum(depth == 0)),
        'valid_pixels': int(np.sum(depth > 0)),
        'total_pixels': int(depth.size),
        'valid_ratio': float(np.sum(depth > 0) / depth.size),
        'has_nan': bool(np.any(np.isnan(depth))),
        'has_inf': bool(np.any(np.isinf(depth))),
        'unique_values': len(np.unique(depth)),
    }
    
    # 检查可能的数据格式问题
    stats['likely_mm_format'] = stats['max_value'] > 10.0  # 可能是毫米单位
    stats['likely_16bit_format'] = stats['max_value'] >= 65535  # 可能是16位整数
    stats['likely_raw_format'] = stats['dtype'] in ['uint16', 'int16']  # 原始整数格式
    
    return stats


def print_depth_stats(stats: Dict):
    """打印深度数据统计信息"""
    print(f"\n=== {stats['frame_name']} ===")
    print(f"形状: {stats['shape']}")
    print(f"数据类型: {stats['dtype']}")
    print(f"值范围: [{stats['min_value']:.4f}, {stats['max_value']:.4f}]")
    print(f"均值 ± 标准差: {stats['mean_value']:.4f} ± {stats['std_value']:.4f}")
    print(f"中位数: {stats['median_value']:.4f}")
    print(f"有效像素: {stats['valid_pixels']:,} / {stats['total_pixels']:,} ({stats['valid_ratio']:.1%})")
    print(f"零值像素: {stats['zero_pixels']:,}")
    print(f"唯一值数量: {stats['unique_values']:,}")
    
    if stats['has_nan'] or stats['has_inf']:
        print(f"⚠️ 异常值: NaN={stats['has_nan']}, Inf={stats['has_inf']}")
    
    # 数据格式推断
    print(f"\n数据格式推断:")
    if stats['likely_16bit_format']:
        print("  ⚠️ 可能是16位整数格式 (值达到65535)")
    if stats['likely_mm_format']:
        print("  ⚠️ 可能是毫米单位 (需要除以1000转换为米)")
    if stats['likely_raw_format']:
        print("  ⚠️ 原始整数格式，可能需要单位转换")


def suggest_depth_fix(stats: Dict) -> Dict:
    """
    根据统计信息建议深度数据修复方案
    
    Args:
        stats (Dict): 深度数据统计信息
        
    Returns:
        Dict: 修复建议
    """
    suggestions = {
        'needs_fix': False,
        'fixes': [],
        'expected_range': [0.3, 2.0],  # 正常深度范围（米）
    }
    
    # 检查是否需要修复
    if stats['max_value'] > 10.0:
        suggestions['needs_fix'] = True
        
        if stats['max_value'] >= 65535:
            suggestions['fixes'].append({
                'type': 'handle_invalid_values',
                'description': '处理无效值 (65535 → 0)',
                'action': 'depth[depth >= 65535] = 0'
            })
        
        if stats['max_value'] > 100:
            suggestions['fixes'].append({
                'type': 'unit_conversion',
                'description': '单位转换 (毫米 → 米)',
                'action': 'depth = depth / 1000.0'
            })
    
    if stats['dtype'] in ['uint16', 'int16']:
        suggestions['fixes'].append({
            'type': 'dtype_conversion',
            'description': '数据类型转换 (整数 → 浮点)',
            'action': 'depth = depth.astype(np.float32)'
        })
    
    return suggestions


def apply_depth_fixes(depth: np.ndarray, suggestions: Dict) -> np.ndarray:
    """
    应用深度数据修复
    
    Args:
        depth (np.ndarray): 原始深度数据
        suggestions (Dict): 修复建议
        
    Returns:
        np.ndarray: 修复后的深度数据
    """
    if not suggestions['needs_fix']:
        return depth.copy()
    
    fixed_depth = depth.copy()
    
    for fix in suggestions['fixes']:
        if fix['type'] == 'handle_invalid_values':
            # 处理无效值
            fixed_depth[fixed_depth >= 65535] = 0
            print(f"✅ 应用修复: {fix['description']}")
            
        elif fix['type'] == 'unit_conversion':
            # 单位转换
            fixed_depth = fixed_depth / 1000.0
            print(f"✅ 应用修复: {fix['description']}")
            
        elif fix['type'] == 'dtype_conversion':
            # 数据类型转换
            fixed_depth = fixed_depth.astype(np.float32)
            print(f"✅ 应用修复: {fix['description']}")
    
    return fixed_depth


def test_depth_fix_on_transitions(transitions: List[Dict], 
                                max_frames: int = 10,
                                apply_fix: bool = False) -> List[Dict]:
    """
    在transitions数据上测试深度修复
    
    Args:
        transitions (List[Dict]): transitions数据
        max_frames (int): 测试帧数
        apply_fix (bool): 是否应用修复
        
    Returns:
        List[Dict]: 修复后的transitions（如果apply_fix=True）
    """
    print(f"测试深度数据修复 (前{max_frames}帧)...")
    
    fixed_transitions = []
    
    for i in range(min(max_frames, len(transitions))):
        print(f"\n--- 第 {i+1}/{max_frames} 帧 ---")
        
        transition = transitions[i]
        obs = transition["observations"]["front"][0]
        depth = obs["depth"]
        
        # 分析原始深度数据
        stats = analyze_depth_data(depth, f"Frame_{i:04d}_原始")
        print_depth_stats(stats)
        
        # 获取修复建议
        suggestions = suggest_depth_fix(stats)
        
        if suggestions['needs_fix']:
            print(f"\n修复建议:")
            for fix in suggestions['fixes']:
                print(f"  - {fix['description']}")
            
            if apply_fix:
                # 应用修复
                fixed_depth = apply_depth_fixes(depth, suggestions)
                
                # 分析修复后的数据
                fixed_stats = analyze_depth_data(fixed_depth, f"Frame_{i:04d}_修复后")
                print_depth_stats(fixed_stats)
                
                # 创建修复后的transition
                fixed_transition = transition.copy()
                fixed_obs = obs.copy()
                fixed_obs["depth"] = fixed_depth
                fixed_transition["observations"] = {
                    "front": [fixed_obs]
                }
                fixed_transitions.append(fixed_transition)
            else:
                print("  (未应用修复，仅显示建议)")
                fixed_transitions.append(transition)
        else:
            print("✅ 深度数据正常，无需修复")
            fixed_transitions.append(transition)
    
    return fixed_transitions


def visualize_depth_comparison(original_depth: np.ndarray, 
                             fixed_depth: np.ndarray,
                             save_path: str = "depth_comparison.png"):
    """
    可视化深度修复前后的对比
    
    Args:
        original_depth (np.ndarray): 原始深度图
        fixed_depth (np.ndarray): 修复后深度图
        save_path (str): 保存路径
    """
    try:
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('深度数据修复对比', fontsize=16)
        
        # 原始深度图
        im1 = axes[0, 0].imshow(original_depth, cmap='viridis')
        axes[0, 0].set_title(f'原始深度图\n范围: [{original_depth.min():.1f}, {original_depth.max():.1f}]')
        axes[0, 0].set_xlabel('Width')
        axes[0, 0].set_ylabel('Height')
        plt.colorbar(im1, ax=axes[0, 0])
        
        # 修复后深度图
        im2 = axes[0, 1].imshow(fixed_depth, cmap='viridis')
        axes[0, 1].set_title(f'修复后深度图\n范围: [{fixed_depth.min():.3f}, {fixed_depth.max():.3f}]')
        axes[0, 1].set_xlabel('Width')
        axes[0, 1].set_ylabel('Height')
        plt.colorbar(im2, ax=axes[0, 1])
        
        # 差异图
        diff = np.abs(original_depth/1000.0 - fixed_depth)  # 假设原始是毫米单位
        im3 = axes[0, 2].imshow(diff, cmap='hot')
        axes[0, 2].set_title(f'差异图\n最大差异: {diff.max():.3f}')
        axes[0, 2].set_xlabel('Width')
        axes[0, 2].set_ylabel('Height')
        plt.colorbar(im3, ax=axes[0, 2])
        
        # 原始深度直方图
        valid_original = original_depth[original_depth > 0]
        if len(valid_original) > 0:
            axes[1, 0].hist(valid_original, bins=50, alpha=0.7, edgecolor='black')
            axes[1, 0].set_title('原始深度值分布')
            axes[1, 0].set_xlabel('深度值')
            axes[1, 0].set_ylabel('频次')
            axes[1, 0].set_yscale('log')
        
        # 修复后深度直方图
        valid_fixed = fixed_depth[fixed_depth > 0]
        if len(valid_fixed) > 0:
            axes[1, 1].hist(valid_fixed, bins=50, alpha=0.7, color='green', edgecolor='black')
            axes[1, 1].set_title('修复后深度值分布')
            axes[1, 1].set_xlabel('深度值 (米)')
            axes[1, 1].set_ylabel('频次')
        
        # 对比统计
        axes[1, 2].axis('off')
        stats_text = f"""
统计对比:

原始数据:
  最小值: {original_depth.min():.1f}
  最大值: {original_depth.max():.1f}
  均值: {original_depth.mean():.1f}
  有效像素: {np.sum(original_depth > 0):,}

修复后:
  最小值: {fixed_depth.min():.3f}
  最大值: {fixed_depth.max():.3f}
  均值: {fixed_depth.mean():.3f}
  有效像素: {np.sum(fixed_depth > 0):,}
        """
        axes[1, 2].text(0.1, 0.9, stats_text, transform=axes[1, 2].transAxes, 
                        fontsize=12, verticalalignment='top', fontfamily='monospace')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"✅ 深度对比图已保存: {save_path}")
        plt.close()
        
    except Exception as e:
        print(f"❌ 可视化失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="深度数据修复和检查工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 检查深度数据问题
  python fix_depth_data.py --input_file data.pkl --check_only

  # 应用修复并保存
  python fix_depth_data.py --input_file data.pkl --apply_fix --output_file fixed_data.pkl

  # 限制检查帧数
  python fix_depth_data.py --input_file data.pkl --max_frames 5
        """
    )
    
    parser.add_argument("--input_file", type=str, required=True, help="输入的pickle文件路径")
    parser.add_argument("--output_file", type=str, help="输出的修复后pickle文件路径")
    parser.add_argument("--max_frames", type=int, default=10, help="最大检查帧数")
    parser.add_argument("--apply_fix", action="store_true", help="应用修复（否则仅检查）")
    parser.add_argument("--check_only", action="store_true", help="仅检查，不修复")
    parser.add_argument("--visualize", action="store_true", help="生成可视化对比图")
    
    args = parser.parse_args()
    
    try:
        # 加载数据
        print(f"加载pickle文件: {args.input_file}")
        with open(args.input_file, "rb") as f:
            transitions = pickle.load(f)
        
        print(f"包含 {len(transitions)} 个transitions")
        
        # 检查和修复
        apply_fix = args.apply_fix and not args.check_only
        
        if args.check_only:
            print("\n=== 仅检查模式 ===")
        elif apply_fix:
            print("\n=== 检查并修复模式 ===")
        else:
            print("\n=== 检查模式（显示修复建议）===")
        
        fixed_transitions = test_depth_fix_on_transitions(
            transitions, 
            max_frames=args.max_frames,
            apply_fix=apply_fix
        )
        
        # 保存修复后的数据
        if apply_fix and args.output_file:
            # 应用修复到所有帧
            print(f"\n应用修复到所有 {len(transitions)} 帧...")
            all_fixed_transitions = []
            
            for i, transition in enumerate(transitions):
                obs = transition["observations"]["front"][0]
                depth = obs["depth"]
                
                # 获取修复建议
                stats = analyze_depth_data(depth, f"Frame_{i:04d}")
                suggestions = suggest_depth_fix(stats)
                
                if suggestions['needs_fix']:
                    fixed_depth = apply_depth_fixes(depth, suggestions)
                    
                    # 创建修复后的transition
                    fixed_transition = transition.copy()
                    fixed_obs = obs.copy()
                    fixed_obs["depth"] = fixed_depth
                    fixed_transition["observations"] = {
                        "front": [fixed_obs]
                    }
                    all_fixed_transitions.append(fixed_transition)
                else:
                    all_fixed_transitions.append(transition)
            
            # 保存修复后的数据
            with open(args.output_file, "wb") as f:
                pickle.dump(all_fixed_transitions, f)
            
            print(f"✅ 修复后的数据已保存到: {args.output_file}")
        
        # 生成可视化对比
        if args.visualize and len(fixed_transitions) > 0:
            print("\n生成可视化对比图...")
            original_depth = transitions[0]["observations"]["front"][0]["depth"]
            fixed_depth = fixed_transitions[0]["observations"]["front"][0]["depth"]
            
            visualize_depth_comparison(original_depth, fixed_depth)
        
        print("\n✅ 深度数据检查完成！")
        
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
