#!/usr/bin/env python3
"""
点云坐标分布分析工具
分析点云在世界坐标系中的分布范围和统计特性

作者: Assistant
日期: 2025-07-24
"""

import pickle
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import argparse
import os
from typing import Dict, List, Tuple, Optional
import seaborn as sns

# 导入现有的depth_to_xyz函数
from convert import depth_to_xyz


def preprocess_depth(depth: np.ndarray, debug: bool = False) -> np.ndarray:
    """
    预处理深度图，自动缩放到合理范围

    Args:
        depth (np.ndarray): 原始深度图
        debug (bool): 是否输出调试信息

    Returns:
        np.ndarray: 处理后的深度图（米单位）
    """
    depth_processed = depth.copy().astype(np.float32)

    # 检查深度值范围，判断是否需要缩放
    valid_depth_mask = depth_processed > 0
    if not np.any(valid_depth_mask):
        return depth_processed

    depth_min = depth_processed[valid_depth_mask].min()
    depth_max = depth_processed[valid_depth_mask].max()

    if debug:
        print(f"原始深度值范围: [{depth_min:.4f}, {depth_max:.4f}]")

    # 如果深度值看起来像是16位整数（通常 > 10），则缩放到米
    if depth_max > 10.0:
        if depth_max > 1000:
            # 可能是毫米单位，转换为米
            depth_processed = depth_processed / 1000.0
            if debug:
                print("检测到毫米单位深度，转换为米")
        else:
            # 可能是16位深度图，使用经验缩放
            # 假设最大深度对应2米，进行线性缩放
            depth_processed = depth_processed / depth_max * 2.0
            if debug:
                print(f"检测到16位深度图，缩放到0-2米范围")

        # 重新计算缩放后的范围
        valid_depth_mask = depth_processed > 0
        if np.any(valid_depth_mask):
            new_min = depth_processed[valid_depth_mask].min()
            new_max = depth_processed[valid_depth_mask].max()
            if debug:
                print(f"缩放后深度值范围: [{new_min:.4f}, {new_max:.4f}] 米")

    # 进一步限制深度范围到合理区间 (0.1m - 3.0m)
    depth_processed = np.clip(depth_processed, 0.0, 3.0)
    # 将过小的深度值设为0（无效）
    depth_processed[depth_processed < 0.05] = 0.0

    return depth_processed


def analyze_single_pointcloud(xyz: np.ndarray, depth: np.ndarray,
                            frame_name: str = "Unknown") -> Dict:
    """
    分析单个点云的坐标分布

    Args:
        xyz (np.ndarray): (H, W, 3) 世界坐标系下的点云
        depth (np.ndarray): (H, W) 深度图，用于确定有效点
        frame_name (str): 帧名称，用于标识

    Returns:
        Dict: 包含统计信息的字典
    """
    # 获取有效点云
    valid_mask = depth > 0
    valid_xyz = xyz[valid_mask]
    valid_depth = depth[valid_mask]

    if len(valid_xyz) == 0:
        return {
            'frame_name': frame_name,
            'valid_points': 0,
            'total_points': depth.size,
            'valid_ratio': 0.0
        }

    # 基本统计
    stats = {
        'frame_name': frame_name,
        'valid_points': len(valid_xyz),
        'total_points': depth.size,
        'valid_ratio': len(valid_xyz) / depth.size,

        # X坐标统计
        'x_min': valid_xyz[:, 0].min(),
        'x_max': valid_xyz[:, 0].max(),
        'x_mean': valid_xyz[:, 0].mean(),
        'x_std': valid_xyz[:, 0].std(),
        'x_range': valid_xyz[:, 0].max() - valid_xyz[:, 0].min(),

        # Y坐标统计
        'y_min': valid_xyz[:, 1].min(),
        'y_max': valid_xyz[:, 1].max(),
        'y_mean': valid_xyz[:, 1].mean(),
        'y_std': valid_xyz[:, 1].std(),
        'y_range': valid_xyz[:, 1].max() - valid_xyz[:, 1].min(),

        # Z坐标统计
        'z_min': valid_xyz[:, 2].min(),
        'z_max': valid_xyz[:, 2].max(),
        'z_mean': valid_xyz[:, 2].mean(),
        'z_std': valid_xyz[:, 2].std(),
        'z_range': valid_xyz[:, 2].max() - valid_xyz[:, 2].min(),

        # 深度统计
        'depth_min': valid_depth.min(),
        'depth_max': valid_depth.max(),
        'depth_mean': valid_depth.mean(),
        'depth_std': valid_depth.std(),
        'depth_range': valid_depth.max() - valid_depth.min(),

        # 距离统计（从原点）
        'distance_min': np.linalg.norm(valid_xyz, axis=1).min(),
        'distance_max': np.linalg.norm(valid_xyz, axis=1).max(),
        'distance_mean': np.linalg.norm(valid_xyz, axis=1).mean(),

        # 异常值检查
        'has_nan': np.any(np.isnan(valid_xyz)),
        'has_inf': np.any(np.isinf(valid_xyz)),

        # 保存深度数据用于后续可视化
        'depth_values': valid_depth.copy()
    }

    return stats


def print_pointcloud_stats(stats: Dict):
    """打印点云统计信息"""
    print(f"\n=== {stats['frame_name']} ===")
    print(f"有效点数: {stats['valid_points']:,} / {stats['total_points']:,} ({stats['valid_ratio']:.1%})")

    if stats['valid_points'] > 0:
        print(f"\n坐标范围 (米):")
        print(f"  X: [{stats['x_min']:.4f}, {stats['x_max']:.4f}] (范围: {stats['x_range']:.4f})")
        print(f"  Y: [{stats['y_min']:.4f}, {stats['y_max']:.4f}] (范围: {stats['y_range']:.4f})")
        print(f"  Z: [{stats['z_min']:.4f}, {stats['z_max']:.4f}] (范围: {stats['z_range']:.4f})")

        print(f"\n坐标均值 ± 标准差:")
        print(f"  X: {stats['x_mean']:.4f} ± {stats['x_std']:.4f}")
        print(f"  Y: {stats['y_mean']:.4f} ± {stats['y_std']:.4f}")
        print(f"  Z: {stats['z_mean']:.4f} ± {stats['z_std']:.4f}")

        print(f"\n深度统计 (米):")
        print(f"  范围: [{stats['depth_min']:.4f}, {stats['depth_max']:.4f}] (跨度: {stats['depth_range']:.4f})")
        print(f"  均值: {stats['depth_mean']:.4f} ± {stats['depth_std']:.4f}")

        print(f"\n距离统计 (从原点):")
        print(f"  最近: {stats['distance_min']:.4f} 米")
        print(f"  最远: {stats['distance_max']:.4f} 米")
        print(f"  平均: {stats['distance_mean']:.4f} 米")

        if stats['has_nan'] or stats['has_inf']:
            print(f"\n⚠️ 异常值:")
            if stats['has_nan']:
                print("  包含 NaN 值")
            if stats['has_inf']:
                print("  包含 Inf 值")
    else:
        print("  无有效点云数据")


def analyze_transitions_batch(transitions: List[Dict], 
                            max_frames: Optional[int] = None,
                            verbose: bool = True) -> List[Dict]:
    """
    批量分析transitions中的点云分布
    
    Args:
        transitions (List[Dict]): transitions数据列表
        max_frames (Optional[int]): 最大分析帧数
        verbose (bool): 是否输出详细信息
        
    Returns:
        List[Dict]: 每帧的统计信息列表
    """
    # 确定分析的帧数
    total_frames = len(transitions)
    if max_frames is not None:
        total_frames = min(total_frames, max_frames)
    
    print(f"开始分析 {total_frames} 帧的点云分布...")
    
    all_stats = []
    
    for i in range(total_frames):
        try:
            # 提取观测数据
            obs = transitions[i]["observations"]["front"][0]
            depth_raw = obs["depth"]  # (H, W)

            if verbose:
                print(f"\n--- 分析第 {i+1}/{total_frames} 帧 ---")
                print(f"深度图形状: {depth_raw.shape}")
                print(f"原始深度值范围: [{depth_raw.min():.4f}, {depth_raw.max():.4f}]")

            # 预处理深度图
            depth = preprocess_depth(depth_raw, debug=verbose)

            if verbose:
                print(f"处理后深度值范围: [{depth.min():.4f}, {depth.max():.4f}] 米")

            # 转换为点云
            xyz = depth_to_xyz(depth, debug=False, validate_params=False, auto_scale_intrinsics=True)
            
            # 分析点云分布
            frame_name = f"Frame_{i:04d}"
            stats = analyze_single_pointcloud(xyz, depth, frame_name)
            all_stats.append(stats)
            
            if verbose:
                print_pointcloud_stats(stats)
                
        except Exception as e:
            print(f"❌ 分析帧 {i} 时出错: {e}")
            continue
    
    return all_stats


def create_distribution_summary(all_stats: List[Dict]) -> Dict:
    """
    创建整体分布摘要

    Args:
        all_stats (List[Dict]): 所有帧的统计信息

    Returns:
        Dict: 整体统计摘要
    """
    valid_stats = [s for s in all_stats if s['valid_points'] > 0]

    if not valid_stats:
        return {'error': '没有有效的点云数据'}

    # 收集所有坐标值
    x_values = [s['x_min'] for s in valid_stats] + [s['x_max'] for s in valid_stats]
    y_values = [s['y_min'] for s in valid_stats] + [s['y_max'] for s in valid_stats]
    z_values = [s['z_min'] for s in valid_stats] + [s['z_max'] for s in valid_stats]
    depth_values = [s['depth_min'] for s in valid_stats] + [s['depth_max'] for s in valid_stats]

    x_means = [s['x_mean'] for s in valid_stats]
    y_means = [s['y_mean'] for s in valid_stats]
    z_means = [s['z_mean'] for s in valid_stats]
    depth_means = [s['depth_mean'] for s in valid_stats]

    valid_points = [s['valid_points'] for s in valid_stats]
    valid_ratios = [s['valid_ratio'] for s in valid_stats]

    summary = {
        'total_frames': len(all_stats),
        'valid_frames': len(valid_stats),
        'invalid_frames': len(all_stats) - len(valid_stats),

        # 整体坐标范围
        'overall_x_range': [min(x_values), max(x_values)],
        'overall_y_range': [min(y_values), max(y_values)],
        'overall_z_range': [min(z_values), max(z_values)],
        'overall_depth_range': [min(depth_values), max(depth_values)],

        # 坐标中心分布
        'x_center_mean': np.mean(x_means),
        'x_center_std': np.std(x_means),
        'y_center_mean': np.mean(y_means),
        'y_center_std': np.std(y_means),
        'z_center_mean': np.mean(z_means),
        'z_center_std': np.std(z_means),
        'depth_center_mean': np.mean(depth_means),
        'depth_center_std': np.std(depth_means),

        # 点云密度统计
        'points_min': min(valid_points),
        'points_max': max(valid_points),
        'points_mean': np.mean(valid_points),
        'points_std': np.std(valid_points),

        # 有效比例统计
        'valid_ratio_min': min(valid_ratios),
        'valid_ratio_max': max(valid_ratios),
        'valid_ratio_mean': np.mean(valid_ratios),
        'valid_ratio_std': np.std(valid_ratios),
    }

    return summary


def print_distribution_summary(summary: Dict):
    """打印分布摘要"""
    if 'error' in summary:
        print(f"\n❌ {summary['error']}")
        return

    print(f"\n{'='*60}")
    print(f"整体点云分布摘要")
    print(f"{'='*60}")

    print(f"帧数统计:")
    print(f"  总帧数: {summary['total_frames']}")
    print(f"  有效帧数: {summary['valid_frames']}")
    print(f"  无效帧数: {summary['invalid_frames']}")

    print(f"\n整体坐标范围 (米):")
    x_range = summary['overall_x_range']
    y_range = summary['overall_y_range']
    z_range = summary['overall_z_range']
    depth_range = summary['overall_depth_range']
    print(f"  X: [{x_range[0]:.4f}, {x_range[1]:.4f}] (跨度: {x_range[1]-x_range[0]:.4f})")
    print(f"  Y: [{y_range[0]:.4f}, {y_range[1]:.4f}] (跨度: {y_range[1]-y_range[0]:.4f})")
    print(f"  Z: [{z_range[0]:.4f}, {z_range[1]:.4f}] (跨度: {z_range[1]-z_range[0]:.4f})")
    print(f"  深度: [{depth_range[0]:.4f}, {depth_range[1]:.4f}] (跨度: {depth_range[1]-depth_range[0]:.4f})")

    print(f"\n点云中心分布:")
    print(f"  X中心: {summary['x_center_mean']:.4f} ± {summary['x_center_std']:.4f}")
    print(f"  Y中心: {summary['y_center_mean']:.4f} ± {summary['y_center_std']:.4f}")
    print(f"  Z中心: {summary['z_center_mean']:.4f} ± {summary['z_center_std']:.4f}")
    print(f"  深度中心: {summary['depth_center_mean']:.4f} ± {summary['depth_center_std']:.4f}")

    print(f"\n点云密度统计:")
    print(f"  最少点数: {summary['points_min']:,}")
    print(f"  最多点数: {summary['points_max']:,}")
    print(f"  平均点数: {summary['points_mean']:.0f} ± {summary['points_std']:.0f}")

    print(f"\n有效像素比例:")
    print(f"  最低: {summary['valid_ratio_min']:.1%}")
    print(f"  最高: {summary['valid_ratio_max']:.1%}")
    print(f"  平均: {summary['valid_ratio_mean']:.1%} ± {summary['valid_ratio_std']:.1%}")


def visualize_depth_distribution(all_stats: List[Dict], save_dir: str = "pointcloud_analysis"):
    """
    可视化深度分布

    Args:
        all_stats (List[Dict]): 所有帧的统计信息
        save_dir (str): 保存目录
    """
    try:
        import matplotlib.pyplot as plt
        import seaborn as sns
    except ImportError:
        print("⚠️ matplotlib 或 seaborn 未安装，跳过深度分布可视化")
        return

    valid_stats = [s for s in all_stats if s['valid_points'] > 0 and 'depth_values' in s]
    if not valid_stats:
        print("⚠️ 没有有效的深度数据用于可视化")
        return

    os.makedirs(save_dir, exist_ok=True)

    # 设置绘图风格
    plt.style.use('default')
    sns.set_palette("husl")

    # 1. 深度统计分布图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Depth Distribution Analysis', fontsize=16)

    # 深度范围分布
    depth_mins = [s['depth_min'] for s in valid_stats]
    depth_maxs = [s['depth_max'] for s in valid_stats]
    depth_means = [s['depth_mean'] for s in valid_stats]

    axes[0, 0].plot(depth_mins, 'b-', label='Minimum Depth', alpha=0.7, linewidth=2)
    axes[0, 0].plot(depth_maxs, 'r-', label='Maximum Depth', alpha=0.7, linewidth=2)
    axes[0, 0].plot(depth_means, 'g-', label='Mean Depth', alpha=0.7, linewidth=2)
    axes[0, 0].fill_between(range(len(depth_mins)), depth_mins, depth_maxs, alpha=0.2)
    axes[0, 0].set_title('Depth Range Distribution (m)')
    axes[0, 0].set_xlabel('Frame Index')
    axes[0, 0].set_ylabel('Depth (m)')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # 深度标准差分布
    depth_stds = [s['depth_std'] for s in valid_stats]
    depth_ranges = [s['depth_range'] for s in valid_stats]

    axes[0, 1].plot(depth_stds, 'purple', label='Depth Standard Deviation', alpha=0.7, linewidth=2)
    axes[0, 1].plot(depth_ranges, 'orange', label='Depth Range', alpha=0.7, linewidth=2)
    axes[0, 1].set_title('Depth Variability Analysis')
    axes[0, 1].set_xlabel('Frame Index')
    axes[0, 1].set_ylabel('Depth (m)')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # 深度直方图（合并所有帧）
    all_depth_values = []
    for stats in valid_stats[:min(10, len(valid_stats))]:  # 限制帧数以避免内存问题
        if 'depth_values' in stats:
            all_depth_values.extend(stats['depth_values'])

    if all_depth_values:
        axes[1, 0].hist(all_depth_values, bins=50, alpha=0.7, color='skyblue',
                       edgecolor='black', density=True)
        axes[1, 0].set_title(f'Depth Value Histogram (First {min(10, len(valid_stats))} Frames)')
        axes[1, 0].set_xlabel('Depth (m)')
        axes[1, 0].set_ylabel('Density')
        axes[1, 0].grid(True, alpha=0.3)

        # 添加统计信息
        mean_depth = np.mean(all_depth_values)
        std_depth = np.std(all_depth_values)
        axes[1, 0].axvline(mean_depth, color='red', linestyle='--',
                          label=f'Mean: {mean_depth:.3f}m')
        axes[1, 0].axvline(mean_depth + std_depth, color='orange', linestyle='--',
                          label=f'+1σ: {mean_depth + std_depth:.3f}m')
        axes[1, 0].axvline(mean_depth - std_depth, color='orange', linestyle='--',
                          label=f'-1σ: {mean_depth - std_depth:.3f}m')
        axes[1, 0].legend()

    # 深度分布箱线图（每帧一个箱线图）
    depth_data_for_boxplot = []
    frame_labels = []
    for i, stats in enumerate(valid_stats[:min(20, len(valid_stats))]):  # 限制显示帧数
        if 'depth_values' in stats and len(stats['depth_values']) > 0:
            # 随机采样以减少数据量
            sample_size = min(1000, len(stats['depth_values']))
            sampled_depths = np.random.choice(stats['depth_values'], sample_size, replace=False)
            depth_data_for_boxplot.append(sampled_depths)
            frame_labels.append(f'F{i}')

    if depth_data_for_boxplot:
        bp = axes[1, 1].boxplot(depth_data_for_boxplot, labels=frame_labels, patch_artist=True)
        for patch in bp['boxes']:
            patch.set_facecolor('lightblue')
            patch.set_alpha(0.7)
        axes[1, 1].set_title(f'Depth Boxplot (First {len(depth_data_for_boxplot)} Frames)')
        axes[1, 1].set_xlabel('Frame')
        axes[1, 1].set_ylabel('Depth (m)')
        axes[1, 1].grid(True, alpha=0.3)
        # 旋转x轴标签以避免重叠
        axes[1, 1].tick_params(axis='x', rotation=45)

    plt.tight_layout()

    # 保存深度分布图
    depth_dist_path = os.path.join(save_dir, "depth_distribution.png")
    plt.savefig(depth_dist_path, dpi=300, bbox_inches='tight')
    print(f"✅ 深度分布图已保存: {depth_dist_path}")
    plt.close()


def visualize_depth_coordinate_relationship(all_stats: List[Dict], save_dir: str = "pointcloud_analysis"):
    """
    可视化深度与坐标的关系

    Args:
        all_stats (List[Dict]): 所有帧的统计信息
        save_dir (str): 保存目录
    """
    try:
        import matplotlib.pyplot as plt
        import seaborn as sns
    except ImportError:
        print("⚠️ matplotlib 或 seaborn 未安装，跳过深度-坐标关系可视化")
        return

    valid_stats = [s for s in all_stats if s['valid_points'] > 0]
    if not valid_stats:
        print("⚠️ 没有有效数据用于深度-坐标关系可视化")
        return

    os.makedirs(save_dir, exist_ok=True)

    # 设置绘图风格
    plt.style.use('default')
    sns.set_palette("husl")

    # 创建深度与坐标关系图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Depth-Coordinate Relationship Analysis', fontsize=16)

    # 收集数据
    depth_means = [s['depth_mean'] for s in valid_stats]
    x_means = [s['x_mean'] for s in valid_stats]
    y_means = [s['y_mean'] for s in valid_stats]
    z_means = [s['z_mean'] for s in valid_stats]

    # 深度 vs X坐标
    axes[0, 0].scatter(depth_means, x_means, alpha=0.6, s=50, c='blue')
    axes[0, 0].set_title('Depth vs X Coordinate')
    axes[0, 0].set_xlabel('Mean Depth (m)')
    axes[0, 0].set_ylabel('Mean X Coordinate (m)')
    axes[0, 0].grid(True, alpha=0.3)

    # 添加趋势线
    if len(depth_means) > 1:
        z = np.polyfit(depth_means, x_means, 1)
        p = np.poly1d(z)
        axes[0, 0].plot(depth_means, p(depth_means), "r--", alpha=0.8, linewidth=2)
        correlation = np.corrcoef(depth_means, x_means)[0, 1]
        axes[0, 0].text(0.05, 0.95, f'Correlation: {correlation:.3f}',
                       transform=axes[0, 0].transAxes, bbox=dict(boxstyle="round", facecolor='wheat'))

    # 深度 vs Y坐标
    axes[0, 1].scatter(depth_means, y_means, alpha=0.6, s=50, c='green')
    axes[0, 1].set_title('Depth vs Y Coordinate')
    axes[0, 1].set_xlabel('Mean Depth (m)')
    axes[0, 1].set_ylabel('Mean Y Coordinate (m)')
    axes[0, 1].grid(True, alpha=0.3)

    if len(depth_means) > 1:
        z = np.polyfit(depth_means, y_means, 1)
        p = np.poly1d(z)
        axes[0, 1].plot(depth_means, p(depth_means), "r--", alpha=0.8, linewidth=2)
        correlation = np.corrcoef(depth_means, y_means)[0, 1]
        axes[0, 1].text(0.05, 0.95, f'Correlation: {correlation:.3f}',
                       transform=axes[0, 1].transAxes, bbox=dict(boxstyle="round", facecolor='wheat'))

    # 深度 vs Z坐标
    axes[1, 0].scatter(depth_means, z_means, alpha=0.6, s=50, c='red')
    axes[1, 0].set_title('Depth vs Z Coordinate')
    axes[1, 0].set_xlabel('Mean Depth (m)')
    axes[1, 0].set_ylabel('Mean Z Coordinate (m)')
    axes[1, 0].grid(True, alpha=0.3)

    if len(depth_means) > 1:
        z_poly = np.polyfit(depth_means, z_means, 1)
        p = np.poly1d(z_poly)
        axes[1, 0].plot(depth_means, p(depth_means), "r--", alpha=0.8, linewidth=2)
        correlation = np.corrcoef(depth_means, z_means)[0, 1]
        axes[1, 0].text(0.05, 0.95, f'Correlation: {correlation:.3f}',
                       transform=axes[1, 0].transAxes, bbox=dict(boxstyle="round", facecolor='wheat'))

    # 深度范围 vs 有效点数
    depth_ranges = [s['depth_range'] for s in valid_stats]
    valid_points = [s['valid_points'] for s in valid_stats]

    axes[1, 1].scatter(depth_ranges, valid_points, alpha=0.6, s=50, c='purple')
    axes[1, 1].set_title('Depth Range vs Valid Points')
    axes[1, 1].set_xlabel('Depth Range (m)')
    axes[1, 1].set_ylabel('Valid Points')
    axes[1, 1].grid(True, alpha=0.3)

    if len(depth_ranges) > 1:
        z = np.polyfit(depth_ranges, valid_points, 1)
        p = np.poly1d(z)
        axes[1, 1].plot(depth_ranges, p(depth_ranges), "r--", alpha=0.8, linewidth=2)
        correlation = np.corrcoef(depth_ranges, valid_points)[0, 1]
        axes[1, 1].text(0.05, 0.95, f'Correlation: {correlation:.3f}',
                       transform=axes[1, 1].transAxes, bbox=dict(boxstyle="round", facecolor='wheat'))

    plt.tight_layout()

    # 保存深度-坐标关系图
    relationship_path = os.path.join(save_dir, "depth_coordinate_relationship.png")
    plt.savefig(relationship_path, dpi=300, bbox_inches='tight')
    print(f"✅ 深度-坐标关系图已保存: {relationship_path}")
    plt.close()


def visualize_distribution(all_stats: List[Dict], save_dir: str = "pointcloud_analysis"):
    """
    可视化点云分布
    
    Args:
        all_stats (List[Dict]): 所有帧的统计信息
        save_dir (str): 保存目录
    """
    try:
        import matplotlib.pyplot as plt
        import seaborn as sns
    except ImportError:
        print("⚠️ matplotlib 或 seaborn 未安装，跳过可视化")
        return
    
    valid_stats = [s for s in all_stats if s['valid_points'] > 0]
    if not valid_stats:
        print("⚠️ 没有有效数据用于可视化")
        return
    
    os.makedirs(save_dir, exist_ok=True)
    
    # 设置绘图风格
    plt.style.use('default')
    sns.set_palette("husl")
    
    # 1. 坐标范围分布
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Point Cloud Coordinate Distribution Analysis', fontsize=16)
    
    # X坐标分布
    x_mins = [s['x_min'] for s in valid_stats]
    x_maxs = [s['x_max'] for s in valid_stats]
    x_means = [s['x_mean'] for s in valid_stats]
    
    axes[0, 0].plot(x_mins, 'b-', label='Minimum', alpha=0.7)
    axes[0, 0].plot(x_maxs, 'r-', label='Maximum', alpha=0.7)
    axes[0, 0].plot(x_means, 'g-', label='Mean', alpha=0.7)
    axes[0, 0].fill_between(range(len(x_mins)), x_mins, x_maxs, alpha=0.2)
    axes[0, 0].set_title('X Coordinate Distribution (m)')
    axes[0, 0].set_xlabel('Frame Index')
    axes[0, 0].set_ylabel('X Coordinate (m)')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # Y坐标分布
    y_mins = [s['y_min'] for s in valid_stats]
    y_maxs = [s['y_max'] for s in valid_stats]
    y_means = [s['y_mean'] for s in valid_stats]
    
    axes[0, 1].plot(y_mins, 'b-', label='Minimum', alpha=0.7)
    axes[0, 1].plot(y_maxs, 'r-', label='Maximum', alpha=0.7)
    axes[0, 1].plot(y_means, 'g-', label='Mean', alpha=0.7)
    axes[0, 1].fill_between(range(len(y_mins)), y_mins, y_maxs, alpha=0.2)
    axes[0, 1].set_title('Y Coordinate Distribution (m)')
    axes[0, 1].set_xlabel('Frame Index')
    axes[0, 1].set_ylabel('Y Coordinate (m)')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # Z坐标分布
    z_mins = [s['z_min'] for s in valid_stats]
    z_maxs = [s['z_max'] for s in valid_stats]
    z_means = [s['z_mean'] for s in valid_stats]
    
    axes[1, 0].plot(z_mins, 'b-', label='Minimum', alpha=0.7)
    axes[1, 0].plot(z_maxs, 'r-', label='Maximum', alpha=0.7)
    axes[1, 0].plot(z_means, 'g-', label='Mean', alpha=0.7)
    axes[1, 0].fill_between(range(len(z_mins)), z_mins, z_maxs, alpha=0.2)
    axes[1, 0].set_title('Z Coordinate Distribution (m)')
    axes[1, 0].set_xlabel('Frame Index')
    axes[1, 0].set_ylabel('Z Coordinate (m)')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 有效点数分布
    valid_points = [s['valid_points'] for s in valid_stats]
    valid_ratios = [s['valid_ratio'] * 100 for s in valid_stats]
    
    ax1 = axes[1, 1]
    ax2 = ax1.twinx()
    
    line1 = ax1.plot(valid_points, 'b-', label='Valid Points', alpha=0.7)
    line2 = ax2.plot(valid_ratios, 'r-', label='Valid Ratio (%)', alpha=0.7)
    
    ax1.set_title('Point Cloud Density Distribution')
    ax1.set_xlabel('Frame Index')
    ax1.set_ylabel('Valid Points', color='b')
    ax2.set_ylabel('Valid Ratio (%)', color='r')
    ax1.grid(True, alpha=0.3)
    
    # 合并图例
    lines = line1 + line2
    labels = [l.get_label() for l in lines]
    ax1.legend(lines, labels, loc='upper left')
    
    plt.tight_layout()
    
    # 保存图像
    coord_dist_path = os.path.join(save_dir, "coordinate_distribution.png")
    plt.savefig(coord_dist_path, dpi=300, bbox_inches='tight')
    print(f"✅ 坐标分布图已保存: {coord_dist_path}")
    plt.close()
    
    # 2. 坐标直方图
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle('Coordinate Value Histogram Distribution', fontsize=16)
    
    # 收集所有坐标值用于直方图
    all_x_values = x_mins + x_maxs + x_means
    all_y_values = y_mins + y_maxs + y_means
    all_z_values = z_mins + z_maxs + z_means
    
    axes[0].hist(all_x_values, bins=30, alpha=0.7, color='blue', edgecolor='black')
    axes[0].set_title('X Coordinate Distribution')
    axes[0].set_xlabel('X Coordinate (m)')
    axes[0].set_ylabel('Frequency')
    axes[0].grid(True, alpha=0.3)
    
    axes[1].hist(all_y_values, bins=30, alpha=0.7, color='green', edgecolor='black')
    axes[1].set_title('Y Coordinate Distribution')
    axes[1].set_xlabel('Y Coordinate (m)')
    axes[1].set_ylabel('Frequency')
    axes[1].grid(True, alpha=0.3)
    
    axes[2].hist(all_z_values, bins=30, alpha=0.7, color='red', edgecolor='black')
    axes[2].set_title('Z Coordinate Distribution')
    axes[2].set_xlabel('Z Coordinate (m)')
    axes[2].set_ylabel('Frequency')
    axes[2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存直方图
    hist_path = os.path.join(save_dir, "coordinate_histogram.png")
    plt.savefig(hist_path, dpi=300, bbox_inches='tight')
    print(f"✅ 坐标直方图已保存: {hist_path}")
    plt.close()

    # 3. 生成深度分布图
    visualize_depth_distribution(all_stats, save_dir)

    # 4. 生成深度与坐标关系图
    visualize_depth_coordinate_relationship(all_stats, save_dir)


def analyze_pickle_file(pickle_path: str, 
                       max_frames: Optional[int] = None,
                       save_analysis: bool = True,
                       verbose: bool = True) -> Tuple[List[Dict], Dict]:
    """
    分析pickle文件中的点云分布
    
    Args:
        pickle_path (str): pickle文件路径
        max_frames (Optional[int]): 最大分析帧数
        save_analysis (bool): 是否保存分析结果
        verbose (bool): 是否输出详细信息
        
    Returns:
        Tuple[List[Dict], Dict]: (所有帧统计, 整体摘要)
    """
    try:
        # 加载pickle文件
        with open(pickle_path, "rb") as f:
            transitions = pickle.load(f)
        
        print(f"加载pickle文件: {pickle_path}")
        print(f"包含 {len(transitions)} 个transitions")
        
        # 分析点云分布
        all_stats = analyze_transitions_batch(transitions, max_frames, verbose)
        
        # 创建整体摘要
        summary = create_distribution_summary(all_stats)
        print_distribution_summary(summary)
        
        # 可视化
        if save_analysis:
            pickle_name = Path(pickle_path).stem
            analysis_dir = f"pointcloud_analysis_{pickle_name}"
            visualize_distribution(all_stats, analysis_dir)
        
        return all_stats, summary
        
    except Exception as e:
        print(f"❌ 分析pickle文件时出错: {e}")
        return [], {}


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="分析点云坐标分布范围",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 分析单个pickle文件
  python analyze_pointcloud_distribution.py --input_file /data/wangyiwen/3d_diffuser_actor/data/raw_data_try/train/pick_bread/ram_insertion_5_demos_2025-06-22_15-53-17.pkl

  # 限制分析帧数
  python analyze_pointcloud_distribution.py --input_file data.pkl --max_frames 20

  # 简化输出
  python analyze_pointcloud_distribution.py --input_file data.pkl --quiet

  # 不保存可视化结果
  python analyze_pointcloud_distribution.py --input_file data.pkl --no_save
        """
    )
    
    parser.add_argument("--input_file", type=str, required=True, help="输入的pickle文件路径")
    parser.add_argument("--max_frames", type=int, default=None, help="最大分析帧数")
    parser.add_argument("--no_save", action="store_true", help="不保存分析结果")
    parser.add_argument("--quiet", action="store_true", help="简化输出（不显示每帧详情）")
    
    args = parser.parse_args()
    
    try:
        print("开始点云坐标分布分析...")
        
        all_stats, summary = analyze_pickle_file(
            args.input_file,
            max_frames=args.max_frames,
            save_analysis=not args.no_save,
            verbose=not args.quiet
        )
        
        if all_stats:
            print(f"\n✅ 分析完成！")
            print(f"分析了 {len(all_stats)} 帧数据")
            if not args.no_save:
                pickle_name = Path(args.input_file).stem
                print(f"分析结果已保存到: pointcloud_analysis_{pickle_name}/")
        else:
            print("\n❌ 分析失败，未获得有效数据")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
