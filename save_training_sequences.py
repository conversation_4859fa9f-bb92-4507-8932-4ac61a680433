#!/usr/bin/env python3
"""
简化脚本：保存训练数据序列图像
加载5个训练序列并保存所有图像
"""

import os
import sys
import torch
import numpy as np
import random
from pathlib import Path
from PIL import Image

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from datasets.dataset_engine import RLBenchDataset
from utils.common_utils import load_instructions

def save_rgb_image(rgb_tensor, save_path):
    """保存RGB图像tensor为图片文件"""
    if len(rgb_tensor.shape) == 4:  # (batch, channel, height, width)
        rgb_tensor = rgb_tensor[0]
    if len(rgb_tensor.shape) == 3:  # (channel, height, width)
        rgb_np = rgb_tensor.permute(1, 2, 0).cpu().numpy()
    else:
        rgb_np = rgb_tensor.cpu().numpy()
    
    # 确保值在[0,1]范围内
    rgb_np = np.clip(rgb_np, 0, 1)
    
    # 转换为[0,255]
    rgb_np = (rgb_np * 255).astype(np.uint8)
    
    # 保存图像
    img = Image.fromarray(rgb_np)
    img.save(save_path)
    print(f"已保存: {save_path}")

def get_full_episode_data(dataset, episode_id):
    """获取完整演示数据"""
    episode_id %= len(dataset._episodes)
    task, variation, file = dataset._episodes[episode_id]
    
    # 直接加载完整演示
    episode = dataset.read_from_cache(file)
    if episode is None:
        return None
    
    # 获取所有帧的ID
    frame_ids = episode[0]
    
    # 获取所有帧的状态
    states = torch.stack([
        episode[1][i] if isinstance(episode[1][i], torch.Tensor)
        else torch.from_numpy(episode[1][i])
        for i in frame_ids
    ])
    
    # 处理相机索引
    if episode[3]:
        cameras = list(episode[3][0].keys())
        index = torch.tensor([cameras.index(c) for c in dataset._cameras])
        states = states[:, index]
    
    # 分离RGB和点云
    rgbs = states[:, :, 0]
    rgbs = dataset._unnormalize_rgb(rgbs)
    
    # 添加目标帧
    if dataset._goal_frame_mode == 1:
        # 从state_dict[6]获取原始序列的最后一帧作为目标帧
        if len(episode) > 6 and episode[6] is not None:
            # 使用保存的原始序列最后一帧
            last_state = episode[6]
            if not isinstance(last_state, torch.Tensor):
                last_state = torch.from_numpy(last_state)
                
            if episode[3]: 
                last_state = last_state[index]
                
            last_rgb = last_state[:, 0]
            last_rgb = dataset._unnormalize_rgb(last_rgb.unsqueeze(0))
        else:
            # 后备方案：使用关键帧序列的最后一帧
            last_frame_id = episode[0][-1]
            last_state = episode[1][last_frame_id]
            if not isinstance(last_state, torch.Tensor):
                last_state = torch.from_numpy(last_state)
                
            if episode[3]: 
                last_state = last_state[index]
                
            last_rgb = last_state[:, 0]
            last_rgb = dataset._unnormalize_rgb(last_rgb.unsqueeze(0))
        
        # 将目标帧复制到每个时间步
        goal_rgb = last_rgb.repeat(len(rgbs), 1, 1, 1, 1) 
        
        # 将目标帧作为新的视角添加
        rgbs = torch.cat([rgbs, goal_rgb], dim=1)
    
    return {
        "task": task,
        "rgbs": rgbs,
        "full_episode_length": len(frame_ids)
    }

def main():
    """主函数"""
    print("开始保存训练序列图像...")
    
    # 设置参数
    dataset_path = "/data/wangyiwen/3d_diffuser_actor/data/peract/packaged_highres/train"
    instructions_path = "/data/wangyiwen/3d_diffuser_actor/instructions/peract/instructions_custom6.pkl"
    output_dir = "training_sequences_output"
    
    # 检查路径
    if not Path(dataset_path).exists():
        print(f"错误: 训练数据路径不存在: {dataset_path}")
        return
    
    # 加载instructions
    instructions = None
    if Path(instructions_path).exists():
        instructions = load_instructions(instructions_path)
    
    # 创建数据集
    tasks = ['put_toilet_roll_on_stand']
    taskvar = [(task, 0) for task in tasks]
    
    dataset = RLBenchDataset(
        root=dataset_path,
        instructions=instructions,
        taskvar=taskvar,
        max_episode_length=10,
        cameras=("front",),
        goal_frame_mode=1,
        training=False,
        cache_size=0
    )
    
    print(f"数据集大小: {len(dataset)}")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 处理5个序列
    for i in range(min(5, len(dataset))):
        print(f"\n处理序列 {i}:")
        
        # 获取数据
        data = get_full_episode_data(dataset, i)
        if data is None:
            print(f"  无法获取数据 {i}")
            continue
        
        print(f"  任务: {data['task']}")
        print(f"  序列长度: {data['full_episode_length']}")
        print(f"  RGB形状: {data['rgbs'].shape}")
        
        # 创建序列目录
        sequence_dir = os.path.join(output_dir, f"sequence_{i}")
        os.makedirs(sequence_dir, exist_ok=True)
        
        rgbs = data['rgbs']  # (T, ncam+1, C, H, W)
        
        if len(rgbs.shape) == 5:
            T, num_cams, C, H, W = rgbs.shape
            print(f"  时间步数: {T}, 相机数: {num_cams}")
            
            # 保存每个时间步的帧
            for t in range(T):
                # 保存当前帧（第一个相机）
                if num_cams > 0:
                    current_frame = rgbs[t, 0]  # (C, H, W)
                    save_rgb_image(current_frame, os.path.join(sequence_dir, f"current_t{t:03d}.png"))
                
                # 保存目标帧（如果有）
                if num_cams > 1:
                    goal_frame = rgbs[t, -1]  # (C, H, W)
                    save_rgb_image(goal_frame, os.path.join(sequence_dir, f"goal_t{t:03d}.png"))
        
        # 保存序列信息
        with open(os.path.join(sequence_dir, "info.txt"), "w") as f:
            f.write(f"任务: {data['task']}\n")
            f.write(f"序列长度: {data['full_episode_length']}\n")
            f.write(f"RGB形状: {data['rgbs'].shape}\n")
            if len(rgbs.shape) == 5:
                T, num_cams, C, H, W = rgbs.shape
                f.write(f"时间步数: {T}\n")
                f.write(f"相机数: {num_cams}\n")
                f.write(f"图像分辨率: {H}x{W}\n")
                f.write(f"通道数: {C}\n")
    
    print(f"\n完成！所有图像已保存到 {output_dir}/")

if __name__ == "__main__":
    main() 