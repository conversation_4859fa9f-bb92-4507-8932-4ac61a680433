#!/usr/bin/env python3
"""
测试gripper状态修改的脚本
验证使用下一帧实际gripper状态的修改是否正确工作
"""

import numpy as np
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from convert import convert_action_to_8d

def test_gripper_modification():
    """测试gripper状态修改"""
    print("🧪 测试gripper状态修改...")
    
    # 模拟状态数据
    # state格式: [gripper_pose(0), tcp_force(1,2,3), tcp_pose(4,5,6,7,8,9), tcp_torque(10,11,12), tcp_vel(13,14,15,16,17,18)]
    current_state = np.zeros(19)
    current_state[0] = 0.0  # gripper闭合
    current_state[4:7] = [0.5, 0.0, 0.3]  # tcp位置
    current_state[7:10] = [0.0, 0.0, 0.0]  # tcp欧拉角
    
    next_state = np.zeros(19)
    next_state[0] = 1.0  # gripper打开
    next_state[4:7] = [0.5, 0.0, 0.3]  # tcp位置
    next_state[7:10] = [0.0, 0.0, 0.0]  # tcp欧拉角
    
    # 模拟动作数据
    action = np.array([0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0])  # gripper动作指令为打开(1.0)
    
    print(f"📊 测试数据:")
    print(f"   当前gripper状态: {current_state[0]:.1f} (0=闭合, 1=打开)")
    print(f"   下一帧gripper状态: {next_state[0]:.1f}")
    print(f"   动作中gripper指令: {action[6]:.1f} (-1=闭合, 1=打开)")
    
    # 测试新的逻辑
    result_with_next = convert_action_to_8d(action, current_state, next_state)
    print(f"✅ 使用下一帧状态: gripper={result_with_next[7]:.1f}")
    
    # 测试旧的逻辑（不传入next_state）
    result_without_next = convert_action_to_8d(action, current_state, None)
    print(f"🔄 使用动作指令: gripper={result_without_next[7]:.1f}")
    
    # 验证结果
    expected_with_next = 1.0  # 应该使用下一帧的实际状态
    expected_without_next = 1.0  # (1.0 + 1) / 2 = 1.0
    
    assert abs(result_with_next[7] - expected_with_next) < 0.01, f"使用下一帧状态失败: {result_with_next[7]} != {expected_with_next}"
    assert abs(result_without_next[7] - expected_without_next) < 0.01, f"使用动作指令失败: {result_without_next[7]} != {expected_without_next}"
    
    print("✅ 所有测试通过!")

def test_edge_cases():
    """测试边界情况"""
    print("\n🧪 测试边界情况...")
    
    # 测试gripper状态超出范围的情况
    current_state = np.zeros(19)
    current_state[0] = 0.5
    current_state[4:7] = [0.5, 0.0, 0.3]
    current_state[7:10] = [0.0, 0.0, 0.0]
    
    next_state = np.zeros(19)
    next_state[0] = 1.5  # 超出[0,1]范围
    next_state[4:7] = [0.5, 0.0, 0.3]
    next_state[7:10] = [0.0, 0.0, 0.0]
    
    action = np.array([0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0])
    
    result = convert_action_to_8d(action, current_state, next_state)
    print(f"📊 超出范围测试: 输入={next_state[0]:.1f}, 输出={result[7]:.1f}")
    
    # 应该被clip到1.0
    assert abs(result[7] - 1.0) < 0.01, f"Clipping失败: {result[7]} != 1.0"
    
    print("✅ 边界情况测试通过!")

def test_gripper_change_detection():
    """测试gripper变化检测 - 基于action指令"""
    print("\n🧪 测试gripper变化检测...")

    # 测试action中gripper指令的变化检测
    # action中gripper范围是[-1, 1]，-1=闭合，0=保持，1=打开

    action1 = -1.0  # 闭合指令
    action2 = 1.0   # 打开指令
    action3 = -0.9  # 几乎闭合，考虑0.1浮动，应该不算变化
    action4 = 0.0   # 保持不变

    # 测试变化检测（阈值0.15，大于标定浮动0.1）
    change_1_to_2 = abs(action2 - action1) > 0.15  # -1 到 1，差值2.0
    change_1_to_3 = abs(action3 - action1) > 0.15  # -1 到 -0.9，差值0.1
    change_1_to_4 = abs(action4 - action1) > 0.15  # -1 到 0，差值1.0
    change_3_to_1 = abs(action1 - action3) > 0.15  # -0.9 到 -1，差值0.1

    print(f"📊 Action gripper变化检测:")
    print(f"   -1.0 -> 1.0: {change_1_to_2} (差值2.0，应该为True)")
    print(f"   -1.0 -> -0.9: {change_1_to_3} (差值0.1，应该为False)")
    print(f"   -1.0 -> 0.0: {change_1_to_4} (差值1.0，应该为True)")
    print(f"   -0.9 -> -1.0: {change_3_to_1} (差值0.1，应该为False)")

    assert change_1_to_2 == True, "大变化检测失败"
    assert change_1_to_3 == False, "小变化检测失败（标定浮动）"
    assert change_1_to_4 == True, "中等变化检测失败"
    assert change_3_to_1 == False, "反向小变化检测失败"

    print("✅ Action gripper变化检测测试通过!")

def test_keyframe_detection_logic():
    """测试关键帧检测逻辑"""
    print("\n🧪 测试关键帧检测逻辑...")

    # 模拟transitions数据
    transitions = []

    # 创建一系列帧，模拟gripper动作变化
    for i in range(5):
        # 基础状态
        state = np.zeros(19)
        state[4:7] = [0.5, 0.0, 0.3]  # tcp位置
        state[7:10] = [0.0, 0.0, 0.0]  # tcp欧拉角

        # 不同的gripper动作
        if i == 0:
            action = np.array([0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0])  # 闭合
            state[0] = 0.0  # 对应的实际状态
        elif i == 1:
            action = np.array([0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -0.95])  # 几乎闭合（浮动）
            state[0] = 0.05
        elif i == 2:
            action = np.array([0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0])   # 打开
            state[0] = 1.0
        elif i == 3:
            action = np.array([0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.9])   # 几乎打开（浮动）
            state[0] = 0.95
        else:
            action = np.array([0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0])  # 再次闭合
            state[0] = 0.0

        transition = {
            "observations": {"state": state.reshape(1, -1)},
            "actions": action
        }
        transitions.append(transition)

    # 测试关键帧检测
    prev_gripper_action = transitions[0]['actions'][6]
    keyframes = [0]  # 第一帧总是关键帧

    for i in range(1, len(transitions)):
        current_gripper_action = transitions[i]['actions'][6]
        gripper_changed = abs(current_gripper_action - prev_gripper_action) > 0.15

        if gripper_changed:
            keyframes.append(i)
            prev_gripper_action = current_gripper_action
            print(f"   帧{i}: gripper {prev_gripper_action:.2f} -> {current_gripper_action:.2f} (变化)")
        else:
            print(f"   帧{i}: gripper {prev_gripper_action:.2f} -> {current_gripper_action:.2f} (无变化)")

    print(f"📊 检测到的关键帧: {keyframes}")

    # 预期结果：帧0（初始），帧2（-1到1的大变化），帧4（1到-1的大变化）
    expected_keyframes = [0, 2, 4]
    assert keyframes == expected_keyframes, f"关键帧检测错误: {keyframes} != {expected_keyframes}"

    print("✅ 关键帧检测逻辑测试通过!")

if __name__ == "__main__":
    test_gripper_modification()
    test_edge_cases()
    test_gripper_change_detection()
    test_keyframe_detection_logic()
    print("\n🎉 所有测试完成!")
