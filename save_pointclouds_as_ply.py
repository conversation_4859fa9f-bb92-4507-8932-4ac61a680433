#!/usr/bin/env python3
"""
Save point cloud data as PLY files based on the training script configuration.
References train_keypose_real.sh for data paths and parameters.
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path
import argparse

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# Import required modules
from datasets.dataset_engine import RLBenchDataset
from utils.common_utils import load_instructions

# Import PLY saving function from existing code
import open3d as o3d


def save_pcd_ply(pcd_tensor, save_path, max_points=100000):
    """
    Save point cloud tensor as PLY file with XYZ coordinate mapping to RGB colors.
    Based on the function from debug_goal_frame_consistency.py
    """
    if pcd_tensor is None:
        print(f"Warning: No point cloud data to save PLY to {save_path}")
        return

    # Normalize shape to (3, H, W)
    if len(pcd_tensor.shape) == 5:
        pcd_tensor = pcd_tensor[0, 0]
    elif len(pcd_tensor.shape) == 4:
        pcd_tensor = pcd_tensor[0]

    xyz = pcd_tensor.cpu().numpy().reshape(3, -1).T  # (N,3)

    # Filter invalid points
    valid = ~np.all(np.isclose(xyz, 0), axis=1)
    xyz = xyz[valid]

    if xyz.shape[0] == 0:
        print(f"Warning: No valid points found for {save_path}")
        return

    # Downsample if too many points
    if xyz.shape[0] > max_points:
        idx = np.random.choice(xyz.shape[0], max_points, replace=False)
        xyz = xyz[idx]

    # Map XYZ coordinates to RGB colors
    # Normalize XYZ to [0,1] range
    xyz_min = xyz.min(axis=0, keepdims=True)
    xyz_max = xyz.max(axis=0, keepdims=True)
    xyz_range = xyz_max - xyz_min
    xyz_range[xyz_range == 0] = 1  # Avoid division by zero
    xyz_normalized = (xyz - xyz_min) / xyz_range
    
    # Map XYZ to RGB
    colors = xyz_normalized  # X->R, Y->G, Z->B

    # Create Open3D point cloud
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(xyz.astype(np.float32))
    pcd.colors = o3d.utility.Vector3dVector(colors.astype(np.float32))

    # Ensure directory exists
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    o3d.io.write_point_cloud(save_path, pcd)
    print(f"Saved PLY point cloud (XYZ→RGB coloring): {save_path}")


def save_pointclouds_from_dataset(dataset_path, output_dir, task_name, max_episodes=10, max_frames_per_episode=20, use_training_augmentation=True):
    """
    Load dataset and save point clouds as PLY files.
    
    Args:
        dataset_path: Path to the dataset (e.g., data/converted_data_try/train)
        output_dir: Directory to save PLY files
        task_name: Task name (e.g., pick_bread)
        max_episodes: Maximum number of episodes to process
        max_frames_per_episode: Maximum number of frames per episode to save
        use_training_augmentation: Whether to use training-time data augmentation (default: True)
    """
    print(f"Loading dataset from: {dataset_path}")
    
    if not Path(dataset_path).exists():
        print(f"Dataset path does not exist: {dataset_path}")
        return

    # Create dataset following the training script configuration
    taskvar = [(task_name, 0)]  # Same as in train_keypose_real.sh
    
    # Configure dataset to match training settings
    if use_training_augmentation:
        # Use same settings as training script
        dataset = RLBenchDataset(
            root=dataset_path,
            instructions=None,  # No instructions needed for PLY saving
            taskvar=taskvar,
            max_episode_length=10,  # Same as typical usage in codebase
            cameras=("front",),  # Same as in train_keypose_real.sh
            training=True,  # Enable training-time augmentation
            image_rescale=(0.75, 1.25),  # Same as training script
            cache_size=0  # No caching needed
        )
    else:
        # Use raw data without augmentation
        dataset = RLBenchDataset(
            root=dataset_path,
            instructions=None,  # No instructions needed for PLY saving
            taskvar=taskvar,
            max_episode_length=10,  # Same as typical usage in codebase
            cameras=("front",),  # Same as in train_keypose_real.sh
            training=False,  # Disable augmentation for raw data
            cache_size=0  # No caching needed
        )
    
    print(f"Dataset size: {len(dataset)}")
    
    if len(dataset) == 0:
        print("No data found in dataset")
        return

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Process episodes
    num_episodes = min(max_episodes, len(dataset))
    print(f"Processing {num_episodes} episodes...")
    
    for episode_idx in range(num_episodes):
        try:
            print(f"\nProcessing episode {episode_idx}:")
            
            # Get data from dataset
            data = dataset[episode_idx]
            pcds = data['pcds']  # Point cloud data
            
            print(f"  Point cloud shape: {pcds.shape}")
            
            if len(pcds.shape) == 5:
                T, num_cams, C, H, W = pcds.shape
                print(f"  Frames: {T}, Cameras: {num_cams}, Channels: {C}, Height: {H}, Width: {W}")
                
                # Process each frame
                num_frames = min(max_frames_per_episode, T)
                for frame_idx in range(num_frames):
                    # Get point cloud for current frame and first camera
                    current_pcd = pcds[frame_idx, 0]  # Shape: (C, H, W)
                    
                    # Create output filename
                    ply_filename = f"episode_{episode_idx:03d}_frame_{frame_idx:03d}.ply"
                    ply_path = os.path.join(output_dir, ply_filename)
                    
                    # Save as PLY
                    save_pcd_ply(current_pcd, ply_path)
                    
                    # If multiple cameras, save goal frame as well
                    if num_cams > 1:
                        goal_pcd = pcds[frame_idx, -1]  # Last camera (goal frame)
                        goal_ply_filename = f"episode_{episode_idx:03d}_frame_{frame_idx:03d}_goal.ply"
                        goal_ply_path = os.path.join(output_dir, goal_ply_filename)
                        save_pcd_ply(goal_pcd, goal_ply_path)
            else:
                print(f"  Unexpected point cloud shape: {pcds.shape}")
                
        except Exception as e:
            print(f"Error processing episode {episode_idx}: {e}")
            continue
    
    print(f"\nFinished processing. PLY files saved to: {output_dir}")


def main():
    parser = argparse.ArgumentParser(description="Save point clouds as PLY files from training data")
    parser.add_argument("--task", default="pick_bread", help="Task name (default: pick_bread)")
    parser.add_argument("--train_data", default="data/converted_data_try/train", 
                       help="Training data path (default: data/converted_data_try/train)")
    parser.add_argument("--val_data", default="data/converted_data_try/val",
                       help="Validation data path (default: data/converted_data_try/val)")
    parser.add_argument("--output_dir", default="output/pointclouds_ply",
                       help="Output directory for PLY files (default: output/pointclouds_ply)")
    parser.add_argument("--max_episodes", type=int, default=10,
                       help="Maximum episodes to process (default: 10)")
    parser.add_argument("--max_frames", type=int, default=20,
                       help="Maximum frames per episode (default: 20)")
    parser.add_argument("--process_val", action="store_true",
                       help="Also process validation data")
    parser.add_argument("--use_training_augmentation", action="store_true", default=True,
                       help="Use training-time data augmentation (default: True)")
    parser.add_argument("--no_augmentation", action="store_true",
                       help="Disable training-time augmentation (use raw data)")
    
    args = parser.parse_args()

    # Handle augmentation flag
    use_augmentation = args.use_training_augmentation and not args.no_augmentation

    print("=== Saving Point Clouds as PLY Files ===")
    print(f"Task: {args.task}")
    print(f"Max episodes: {args.max_episodes}")
    print(f"Max frames per episode: {args.max_frames}")
    print(f"Use training augmentation: {use_augmentation}")
    
    # Process training data
    print("\n--- Processing Training Data ---")
    train_output_dir = os.path.join(args.output_dir, "train")
    save_pointclouds_from_dataset(
        dataset_path=args.train_data,
        output_dir=train_output_dir,
        task_name=args.task,
        max_episodes=args.max_episodes,
        max_frames_per_episode=args.max_frames,
        use_training_augmentation=use_augmentation
    )
    
    # Process validation data if requested
    if args.process_val:
        print("\n--- Processing Validation Data ---")
        val_output_dir = os.path.join(args.output_dir, "val")
        save_pointclouds_from_dataset(
            dataset_path=args.val_data,
            output_dir=val_output_dir,
            task_name=args.task,
            max_episodes=args.max_episodes,
            max_frames_per_episode=args.max_frames,
            use_training_augmentation=use_augmentation
        )
    
    print("\n=== Complete ===")


if __name__ == "__main__":
    main()
