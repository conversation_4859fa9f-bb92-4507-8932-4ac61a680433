#!/usr/bin/env python3
"""
调试脚本：比较训练和测试时获取的目标帧
验证训练时和测试时的目标帧是否一致
"""

import os
import sys
import torch
import numpy as np
import pickle
import random
from pathlib import Path
import matplotlib.pyplot as plt
from PIL import Image
import cv2
from mpl_toolkits.mplot3d import Axes3D  # noqa: F401
import open3d as o3d

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from datasets.dataset_engine import RLBenchDataset
from utils.utils_with_rlbench import RLBenchEnv, Actioner
from utils.common_utils import load_instructions

def save_rgb_image(rgb_tensor, save_path, title=""):
    """保存RGB图像tensor为图片文件"""
    if len(rgb_tensor.shape) == 4:  # (batch, channel, height, width)
        rgb_tensor = rgb_tensor[0]
    if len(rgb_tensor.shape) == 3:  # (channel, height, width)
        rgb_np = rgb_tensor.permute(1, 2, 0).cpu().numpy()
    else:
        rgb_np = rgb_tensor.cpu().numpy()
    
    # 确保值在[0,1]范围内
    rgb_np = np.clip(rgb_np, 0, 1)
    
    # 转换为[0,255]
    rgb_np = (rgb_np * 255).astype(np.uint8)
    
    # 保存图像
    img = Image.fromarray(rgb_np)
    img.save(save_path)
    print(f"已保存图像: {save_path}")

def save_pcd_image(pcd_tensor, save_path, rgb_tensor=None, title="", max_points=30000, img_wh=(512, 512)):
    """使用Open3D将点云渲染为PNG，可选按RGB着色"""

    # 规整形状至 (3, H, W)
    if pcd_tensor is None:
        print(f"警告: 无点云数据可保存到 {save_path}")
        return

    if len(pcd_tensor.shape) == 5:  # (B, N, C, H, W)
        pcd_tensor = pcd_tensor[0, 0]
    elif len(pcd_tensor.shape) == 4:  # (N, C, H, W)
        pcd_tensor = pcd_tensor[0]
    elif len(pcd_tensor.shape) == 3:
        pass  # already (C,H,W)
    else:
        print(f"未识别的点云数据形状: {pcd_tensor.shape}")
        return

    # numpy, reshape为(N,3)
    pcd_np = pcd_tensor.cpu().numpy()
    xyz = pcd_np.reshape(3, -1).T  # (N,3)

    # 颜色处理
    if rgb_tensor is not None:
        if len(rgb_tensor.shape) == 5:
            rgb_tensor = rgb_tensor[0, 0]
        elif len(rgb_tensor.shape) == 4:
            rgb_tensor = rgb_tensor[0]
        # 只取前三通道，去掉attention等
        rgb_np = rgb_tensor[:3].cpu().numpy()
        if rgb_np.min() < 0:
            rgb_np = (rgb_np + 1) / 2  # [-1,1] -> [0,1]
        rgb_np = np.clip(rgb_np, 0, 1)
        rgb_pts = rgb_np.reshape(3, -1).T  # (N,3)
    else:
        rgb_pts = None

    # 过滤全零
    valid = ~np.all(np.isclose(xyz, 0), axis=1)
    xyz = xyz[valid]
    if rgb_pts is not None:
        rgb_pts = rgb_pts[valid]

    # 随机采样
    if len(xyz) == 0:
        print(f"警告: 点云为空，跳过保存 {save_path}")
        return
    if xyz.shape[0] > max_points:
        idx = np.random.choice(xyz.shape[0], max_points, replace=False)
        xyz = xyz[idx]
        if rgb_pts is not None:
            rgb_pts = rgb_pts[idx]

    # 构造Open3D点云
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(xyz)

    # 使用XYZ坐标映射到RGB颜色（忽略传入的rgb_tensor）
    # 将XYZ归一化到[0,1]范围
    xyz_min = xyz.min(axis=0, keepdims=True)
    xyz_max = xyz.max(axis=0, keepdims=True)
    xyz_range = xyz_max - xyz_min
    xyz_range[xyz_range == 0] = 1  # 避免除零
    xyz_normalized = (xyz - xyz_min) / xyz_range
    
    # XYZ分别映射到RGB
    colors = xyz_normalized  # X->R, Y->G, Z->B
    pcd.colors = o3d.utility.Vector3dVector(colors)

    # 使用Open3D OffscreenRenderer渲染
    import open3d.visualization.rendering as rendering

    renderer = rendering.OffscreenRenderer(img_wh[0], img_wh[1])
    mat = rendering.MaterialRecord()
    mat.shader = "defaultUnlit"
    if hasattr(mat, "point_size"):
        mat.point_size = 3.0  # 增大点大小，便于可视
    renderer.scene.add_geometry("pcd", pcd, mat)

    # 背景颜色兼容不同版本
    if hasattr(renderer.scene, "set_background"):
        renderer.scene.set_background([1, 1, 1, 1])

    # 摄像机视角
    bounds = pcd.get_axis_aligned_bounding_box()
    center = bounds.get_center()
    try:
        renderer.setup_camera(60.0, bounds, center)
    except Exception:
        if hasattr(renderer.scene, "camera") and hasattr(renderer.scene.camera, "look_at"):
            eye = center + np.array([0, 0, bounds.get_extent().max() * 1.2])
            renderer.scene.camera.look_at(center, eye, [0, 1, 0])

    img_o3d = renderer.render_to_image()

    # 将float32 RGBA转换为uint8 RGB，避免在某些查看器中出现色块
    img_np = np.asarray(img_o3d)  # H,W,4 float32
    img_np = (img_np[:, :, :3] * 255.0).clip(0, 255).astype(np.uint8)
    Image.fromarray(img_np).save(save_path)

    # 释放资源（旧版Open3D可能无release方法）
    if hasattr(renderer, "release"):
        renderer.release()

    print(f"已保存点云图像: {save_path}")

def save_pcd_ply(pcd_tensor, save_path, rgb_tensor=None, max_points=100000):
    """把点云tensor直接保存为PLY文件，使用XYZ坐标映射到RGB颜色"""

    if pcd_tensor is None:
        print(f"警告: 无点云数据可保存PLY到 {save_path}")
        return

    # 规整形状到 (3, H, W)
    if len(pcd_tensor.shape) == 5:
        pcd_tensor = pcd_tensor[0, 0]
    elif len(pcd_tensor.shape) == 4:
        pcd_tensor = pcd_tensor[0]

    xyz = pcd_tensor.cpu().numpy().reshape(3, -1).T  # (N,3)

    # 过滤无效点
    valid = ~np.all(np.isclose(xyz, 0), axis=1)
    xyz = xyz[valid]

    # 下采样
    if xyz.shape[0] > max_points:
        idx = np.random.choice(xyz.shape[0], max_points, replace=False)
        xyz = xyz[idx]

    # 使用XYZ坐标映射到RGB颜色
    # 将XYZ归一化到[0,1]范围
    xyz_min = xyz.min(axis=0, keepdims=True)
    xyz_max = xyz.max(axis=0, keepdims=True)
    xyz_range = xyz_max - xyz_min
    xyz_range[xyz_range == 0] = 1  # 避免除零
    xyz_normalized = (xyz - xyz_min) / xyz_range
    
    # XYZ分别映射到RGB
    colors = xyz_normalized  # X->R, Y->G, Z->B

    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(xyz.astype(np.float32))
    pcd.colors = o3d.utility.Vector3dVector(colors.astype(np.float32))

    # 确保目录存在
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    o3d.io.write_point_cloud(save_path, pcd)
    print(f"已保存PLY点云（XYZ→RGB着色）: {save_path}")

def save_pcd_as_2d_image(pcd_tensor, save_path):
    """使用您提供的代码直接生成点云2D图像"""
    
    if pcd_tensor is None:
        print(f"警告: 无点云数据可保存2D图像到 {save_path}")
        return

    # 按照您的要求修改：先reshape成(H*W, 3)，再min axis=0
    xyz = pcd_tensor.cpu().numpy().copy()  # (3, H, W)
    xyz = xyz.transpose(1, 2, 0)  # (H, W, 3)
    xyz = xyz.reshape(-1, 3)  # (H*W, 3)
    
    xyz_min = xyz.min(axis=0)  # (3,) 按axis=0计算最小值
    xyz_max = xyz.max(axis=0)  # (3,) 按axis=0计算最大值
    norm_xyz = (xyz - xyz_min) / (xyz_max - xyz_min + 1e-8)
    
    # 重新reshape回(H, W, 3)并转为uint8
    H, W = pcd_tensor.shape[1], pcd_tensor.shape[2]
    vis_img = norm_xyz.reshape(H, W, 3)
    vis_img = (vis_img * 255).astype(np.uint8)
    
    # 确保目录存在
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    plt.imsave(save_path, vis_img)
    print(f"已保存点云2D图像: {save_path}")

def print_camera_extrinsics(obs, camera_name="front"):
    """打印指定相机的外参和内参"""
    print(f"\n=== {camera_name}相机参数 ===")
    
    # 获取外参矩阵 (4x4)
    extrinsics_key = f"{camera_name}_camera_extrinsics"
    intrinsics_key = f"{camera_name}_camera_intrinsics"
    
    if hasattr(obs, 'misc') and extrinsics_key in obs.misc:
        extrinsics = obs.misc[extrinsics_key]
        print(f"外参矩阵 (4x4):")
        print(extrinsics)
        
        # 提取旋转矩阵和平移向量
        rotation = extrinsics[:3, :3]
        translation = extrinsics[:3, 3]
        print(f"旋转矩阵 (3x3):")
        print(rotation)
        print(f"平移向量 (3,):")
        print(translation)
    else:
        print(f"未找到{camera_name}相机外参")
    
    # 获取内参矩阵 (3x3)
    if hasattr(obs, 'misc') and intrinsics_key in obs.misc:
        intrinsics = obs.misc[intrinsics_key]
        print(f"内参矩阵 (3x3):")
        print(intrinsics)
        
        # 提取焦距和主点
        fx, fy = intrinsics[0, 0], intrinsics[1, 1]
        cx, cy = intrinsics[0, 2], intrinsics[1, 2]
        print(f"焦距: fx={fx:.2f}, fy={fy:.2f}")
        print(f"主点: cx={cx:.2f}, cy={cy:.2f}")
    else:
        print(f"未找到{camera_name}相机内参")
    
    print("=" * 40)

def get_data_from_training_pipeline():
    """通过训练数据流水线获取数据"""
    print("=== 通过训练数据流水线获取数据 ===")
    
    # 设置参数
    dataset_path = "/data/xjd/3d_diffuser_actor/data/peract/packaged_highres/train"
    instructions_path = "/data/wangyiwen/3d_diffuser_actor/instructions/peract/instructions_custom6.pkl"
    
    # 检查路径是否存在
    if not Path(dataset_path).exists():
        print(f"警告: 训练数据路径不存在: {dataset_path}")
        return None
    
    if not Path(instructions_path).exists():
        print(f"警告: instruction路径不存在: {instructions_path}")
        instructions = None
    else:
        instructions = load_instructions(instructions_path)
    
    # 创建数据集 - 注意这里我们只是用来访问数据文件，不依赖max_episode_length
    tasks = ['stack_wine']  # 与测试脚本一致
    taskvar = [(task, 0) for task in tasks]  # variation 0
    
    dataset = RLBenchDataset(
        root=dataset_path,
        instructions=instructions,
        taskvar=taskvar,
        max_episode_length=10,  # 这个参数在我们的自定义函数中不会被使用
        cameras=("front",),  # 与测试脚本一致
        goal_frame_mode=1,  # 与测试脚本一致
        training=False,  # 不使用数据增强，便于比较
        cache_size=0
    )
    
    print(f"训练数据集大小: {len(dataset)}")
    
    training_data = []
    for i in range(min(5, len(dataset))):
        try:
            print(f"\n训练数据 {i}:")
            
            # 使用自定义函数获取完整演示数据
            data = get_full_episode_data(dataset, i)
            if data is None:
                print(f"  无法获取数据 {i}")
                continue
            
            print(f"  任务: {data['task'][0] if data['task'] else 'unknown'}")
            print(f"  完整演示长度: {data['full_episode_length']}")
            print(f"  RGB形状: {data['rgbs'].shape}")
            print(f"  PCD形状: {data['pcds'].shape}")
            print(f"  instruction形状: {data['instr'].shape}")
            print(f"  动作形状: {data['action'].shape}")
            
            # 验证目标帧逻辑
            if len(data['rgbs'].shape) == 5:
                T, num_cams, C, H, W = data['rgbs'].shape
                print(f"  关键帧数量: {T}, 相机数: {num_cams}")
                if num_cams > 1:
                    print(f"  验证目标帧一致性...")
                    # 比较第一帧和最后一帧的当前观测
                    first_current = data['rgbs'][0, 0]  # 第一个关键帧的当前观测
                    last_current = data['rgbs'][-1, 0]  # 最后一个关键帧的当前观测
                    first_goal = data['rgbs'][0, -1]    # 目标帧（应该是固定的）
                    last_goal = data['rgbs'][-1, -1]    # 目标帧（应该与first_goal相同）
                    
                    # 检查目标帧是否在整个序列中保持一致
                    goal_consistency = torch.allclose(first_goal, last_goal, atol=1e-6)
                    print(f"  目标帧在关键帧序列中是否一致: {goal_consistency}")
                    
                    # 检查最后一个关键帧的当前观测是否与目标帧一致（应该一致，因为目标帧就是最后一个关键帧）
                    goal_correctness = torch.allclose(last_current, last_goal, atol=1e-3)
                    print(f"  最后一个关键帧当前观测与目标帧是否一致: {goal_correctness}")
                    
                    # 检查关键帧序列是否显示任务进展
                    current_change = torch.norm(last_current - first_current).item()
                    print(f"  关键帧序列进展程度 (当前帧变化): {current_change:.4f}")
                    
                    if current_change < 0.1:
                        print(f"  注意: 关键帧变化较小，可能任务较简单或视角问题")
                    
                    # 检查每个关键帧之间的变化
                    if T > 2:
                        print(f"  关键帧间变化分析:")
                        for t in range(1, T):
                            prev_current = data['rgbs'][t-1, 0]
                            curr_current = data['rgbs'][t, 0]
                            step_change = torch.norm(curr_current - prev_current).item()
                            print(f"    关键帧{t-1}→{t}: 变化 = {step_change:.4f}")
                else:
                    print(f"  警告: 只有一个相机视角，无法验证目标帧")
            
            training_data.append({
                'index': i,
                'task': data['task'][0] if data['task'] else 'unknown',
                'rgbs': data['rgbs'],
                'pcds': data['pcds'],
                'instruction': data['instr'],
                'action': data['action'],
                'gripper': data['curr_gripper'],
                'full_episode_length': data['full_episode_length'],
                'goal_frame_from_saved': data.get('goal_frame_from_saved')  # 添加新字段
            })
        except Exception as e:
            print(f"获取训练数据 {i} 时出错: {e}")
            import traceback
            traceback.print_exc()
            continue
    
    return training_data

def get_data_from_testing_pipeline():
    """通过测试数据流水线获取数据"""
    print("\n=== 通过测试数据流水线获取数据 ===")
    
    # 设置参数
    data_dir = "/data/wangyiwen/3d_diffuser_actor/data/peract/raw/train/"
    instructions_path = "/data/wangyiwen/3d_diffuser_actor/instructions/peract/instructions_selected.pkl"
    
    # 检查路径是否存在
    if not Path(data_dir).exists():
        print(f"警告: 测试数据路径不存在: {data_dir}")
        return None
    
    if not Path(instructions_path).exists():
        print(f"警告: instruction路径不存在: {instructions_path}")
        return None
    
    # 创建测试环境
    try:
        env = RLBenchEnv(
            data_path=data_dir,
            image_size=[256, 256],
            apply_rgb=True,
            apply_pc=True,
            headless=True,
            apply_cameras=("front",),
            collision_checking=False
        )
        
        instructions = load_instructions(instructions_path)
        
        # 创建假的模型和Actioner（只用于获取目标帧）
        class FakeModel:
            def eval(self):
                pass
        
        actioner = Actioner(
            policy=FakeModel(),
            instructions=instructions,
            apply_cameras=("front",),
            goal_frame_mode=1
        )
        
        task_str = "stack_wine"
        variation = 0
        
        testing_data = []
        
        for demo_id in range(5):
            try:
                print(f"\n测试数据 {demo_id}:")
                
                # 加载演示数据
                actioner.load_episode(task_str, variation, env, demo_id)
                
                # 获取demo
                demo = env.get_demo(task_str, variation, demo_id)
                if demo is None or len(demo) == 0:
                    print(f"  无法获取demo {demo_id}")
                    continue
                
                demo_obj = demo[0]
                print(f"  原始demo长度: {len(demo_obj)}")
                
                # 检查demo的详细结构
                if len(demo_obj) > 0:
                    print(f"  demo类型: {type(demo_obj)}")
                    print(f"  第一个观测类型: {type(demo_obj[0])}")
                    if hasattr(demo_obj[0], '__dict__'):
                        print(f"  第一个观测属性: {list(demo_obj[0].__dict__.keys())}")
                
                # 获取第一帧作为当前帧
                first_obs = demo_obj[0]
                current_rgb, current_pcd, current_gripper = env.get_rgb_pcd_gripper_from_obs(first_obs)
                
                # 打印第一帧的相机外参
                print_camera_extrinsics(first_obs, "front")
                
                # 获取最后一帧作为目标帧
                last_obs = demo_obj[-1]
                goal_rgb, goal_pcd, goal_gripper = env.get_rgb_pcd_gripper_from_obs(last_obs)
                
                print(f"  当前帧RGB形状: {current_rgb.shape}")
                print(f"  目标帧RGB形状: {goal_rgb.shape}")
                print(f"  目标帧已加载到actioner: {actioner._goal_rgb is not None}")
                
                # 检查demo中每几帧的状态，看看是否有明显变化
                if len(demo_obj) > 10:
                    step_size = max(1, len(demo_obj) // 5)  # 检查5个关键点
                    print(f"  检查关键帧变化（每{step_size}帧）:")
                    prev_rgb = None
                    for i in range(0, len(demo_obj), step_size):
                        obs = demo_obj[i]
                        rgb, _, _ = env.get_rgb_pcd_gripper_from_obs(obs)
                        if prev_rgb is not None:
                            change = torch.norm(rgb - prev_rgb).item()
                            print(f"    帧{i}: 与前一关键帧变化 = {change:.4f}")
                        prev_rgb = rgb
                
                # 获取instruction
                if instructions and task_str in instructions and variation in instructions[task_str]:
                    instr = random.choice(list(instructions[task_str][variation]))
                    instr = instr.unsqueeze(0)
                else:
                    instr = torch.zeros(1, 53, 512)
                
                testing_data.append({
                    'index': demo_id,
                    'task': task_str,
                    'current_rgb': current_rgb,
                    'goal_rgb': goal_rgb,
                    'current_pcd': current_pcd,
                    'goal_pcd': goal_pcd,
                    'current_gripper': current_gripper,
                    'goal_gripper': goal_gripper,
                    'instruction': instr,
                    'actioner_goal_rgb': actioner._goal_rgb,
                    'actioner_goal_pcd': actioner._goal_pcd
                })
                
            except Exception as e:
                print(f"获取测试数据 {demo_id} 时出错: {e}")
                continue
        
        return testing_data
        
    except Exception as e:
        print(f"创建测试环境失败: {e}")
        return None

def save_comparison_data(training_data, testing_data, output_dir="debug_goal_frame_output"):
    """保存比较数据"""
    print(f"\n=== 保存比较数据到 {output_dir} ===")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存训练数据
    if training_data:
        train_dir = os.path.join(output_dir, "training")
        os.makedirs(train_dir, exist_ok=True)
        
        for i, data in enumerate(training_data):
            item_dir = os.path.join(train_dir, f"item_{i}")
            os.makedirs(item_dir, exist_ok=True)
            
            rgbs = data['rgbs']  # 形状应该是 (T, ncam+1, C, H, W) 当goal_frame_mode=1时
            pcds = data['pcds']  # 点云，形状与RGB相同但通道为XYZ
            
            print(f"\n训练数据 {i}:")
            print(f"  RGB形状: {rgbs.shape}")
            
            if len(rgbs.shape) == 5:
                T, num_cams, C, H, W = rgbs.shape
                print(f"  时间步数: {T}, 相机数: {num_cams}, 分辨率: {H}x{W}")
                
                # 保存每个时间步的帧
                for t in range(T):
                    # 保存当前帧（第一个相机）
                    if num_cams > 0:
                        current_frame = rgbs[t, 0]  # (C, H, W)
                        save_rgb_image(current_frame, os.path.join(item_dir, f"train_current_t{t:03d}.png"))
                        # 保存当前帧点云
                        current_pcd = pcds[t, 0]
                        save_pcd_image(current_pcd, os.path.join(item_dir, f"train_current_pcd_t{t:03d}.png"), rgbs[t, 0])
                        save_pcd_ply(current_pcd, os.path.join(item_dir, f"train_current_pcd_t{t:03d}.ply"), rgbs[t, 0])
                        save_pcd_as_2d_image(current_pcd, os.path.join(item_dir, f"train_current_pcd_2d_t{t:03d}.png"))
                    
                    # 如果有目标帧（最后一个相机位置通常是目标帧）
                    if num_cams > 1:
                        goal_frame = rgbs[t, -1]  # (C, H, W)
                        save_rgb_image(goal_frame, os.path.join(item_dir, f"train_goal_t{t:03d}.png"))
                        # 保存目标帧点云
                        goal_pcd = pcds[t, -1]
                        save_pcd_image(goal_pcd, os.path.join(item_dir, f"train_goal_pcd_t{t:03d}.png"), rgbs[t, -1])
                        save_pcd_ply(goal_pcd, os.path.join(item_dir, f"train_goal_pcd_t{t:03d}.ply"), rgbs[t, -1])
                        save_pcd_as_2d_image(goal_pcd, os.path.join(item_dir, f"train_goal_pcd_2d_t{t:03d}.png"))
                
                # 保存从state_dict[6]获取的目标帧
                if 'goal_frame_from_saved' in data and data['goal_frame_from_saved'] is not None:
                    saved_goal_rgb = data['goal_frame_from_saved']['rgb'][0]  # 取第一个相机的RGB
                    save_rgb_image(saved_goal_rgb, os.path.join(item_dir, "train_saved_goal_frame.png"))
                    saved_goal_pcd = data['goal_frame_from_saved']['pcd'][0]
                    save_pcd_image(saved_goal_pcd, os.path.join(item_dir, "train_saved_goal_pcd.png"), saved_goal_rgb)
                    save_pcd_ply(saved_goal_pcd, os.path.join(item_dir, "train_saved_goal_pcd.ply"), saved_goal_rgb)
                    save_pcd_as_2d_image(saved_goal_pcd, os.path.join(item_dir, "train_saved_goal_pcd_2d.png"))
            
            else:
                # 处理其他形状的数据
                if len(rgbs.shape) == 4:  # (T, C, H, W)
                    T = rgbs.shape[0]
                    for t in range(T):
                        save_rgb_image(rgbs[t], os.path.join(item_dir, f"train_frame_t{t:03d}.png"))
                else:
                    save_rgb_image(rgbs, os.path.join(item_dir, "train_frame.png"))
            
            # 保存详细的演示信息
            with open(os.path.join(item_dir, "train_info.txt"), "w") as f:
                f.write(f"任务: {data['task']}\n")
                f.write(f"RGB形状: {data['rgbs'].shape}\n")
                f.write(f"PCD形状: {data['pcds'].shape}\n")
                f.write(f"instruction形状: {data['instruction'].shape}\n")
                f.write(f"动作形状: {data['action'].shape}\n")
                
                if 'full_episode_length' in data:
                    f.write(f"完整演示长度: {data['full_episode_length']}\n")
                
                # 检查state_dict[6]信息
                if 'goal_frame_from_saved' in data:
                    if data['goal_frame_from_saved'] is not None:
                        f.write(f"state_dict[6]目标帧可用: 是\n")
                        f.write(f"state_dict[6]目标帧RGB形状: {data['goal_frame_from_saved']['rgb'].shape}\n")
                        f.write(f"state_dict[6]目标帧PCD形状: {data['goal_frame_from_saved']['pcd'].shape}\n")
                    else:
                        f.write(f"state_dict[6]目标帧可用: 否\n")
                
                if len(rgbs.shape) == 5:
                    T, num_cams, C, H, W = rgbs.shape
                    f.write(f"\n演示序列信息:\n")
                    f.write(f"保存的序列长度: {T}\n")
                    f.write(f"相机数量: {num_cams}\n")
                    f.write(f"图像分辨率: {H}x{W}\n")
                    f.write(f"通道数: {C}\n")
                    f.write(f"\n保存文件说明:\n")
                    f.write(f"- train_current_tXXX.png: 第XXX时间步的当前帧\n")
                    if num_cams > 1:
                        f.write(f"- train_goal_tXXX.png: 第XXX时间步的目标帧（固定为演示最后一帧）\n")
                    if 'goal_frame_from_saved' in data and data['goal_frame_from_saved'] is not None:
                        f.write(f"- train_saved_goal_frame.png: 从state_dict[6]获取的目标帧\n")
                    f.write(f"\n注意:\n")
                    f.write(f"- 目标帧在整个序列中应该保持一致\n")
                    f.write(f"- 目标帧应该与序列最后一帧的当前观测一致\n")
                    f.write(f"- 当前帧应该从第一帧到最后一帧显示任务执行过程\n")
                    f.write(f"- state_dict[6]保存的是序列的最后一帧，应该与目标帧一致\n")
    
    # 保存测试数据
    if testing_data:
        test_dir = os.path.join(output_dir, "testing")
        os.makedirs(test_dir, exist_ok=True)
        
        for i, data in enumerate(testing_data):
            item_dir = os.path.join(test_dir, f"item_{i}")
            os.makedirs(item_dir, exist_ok=True)
            
            # 保存当前帧 - 只取前3个通道（去掉attention通道）
            current_rgb = data['current_rgb'][0, 0, :3]  # 只取RGB通道，去掉attention通道
            save_rgb_image(current_rgb, os.path.join(item_dir, "test_current_frame.png"))
            # 保存当前帧点云
            current_pcd = data['current_pcd'][0, 0]
            save_pcd_image(current_pcd, os.path.join(item_dir, "test_current_pcd.png"), current_rgb)
            save_pcd_ply(current_pcd, os.path.join(item_dir, "test_current_pcd.ply"), current_rgb)
            save_pcd_as_2d_image(current_pcd, os.path.join(item_dir, "test_current_pcd_2d.png"))
            
            # 保存目标帧 - 只取前3个通道（去掉attention通道）
            goal_rgb = data['goal_rgb'][0, 0, :3]  # 只取RGB通道，去掉attention通道
            save_rgb_image(goal_rgb, os.path.join(item_dir, "test_goal_frame.png"))
            # 保存目标帧点云
            goal_pcd = data['goal_pcd'][0, 0]
            save_pcd_image(goal_pcd, os.path.join(item_dir, "test_goal_pcd.png"), goal_rgb)
            save_pcd_ply(goal_pcd, os.path.join(item_dir, "test_goal_pcd.ply"), goal_rgb)
            save_pcd_as_2d_image(goal_pcd, os.path.join(item_dir, "test_goal_pcd_2d.png"))
            
            # 如果actioner中有目标帧，也保存
            if data['actioner_goal_rgb'] is not None:
                actioner_goal = data['actioner_goal_rgb'][0]  # 移除camera维度，只取第一个相机
                save_rgb_image(actioner_goal, os.path.join(item_dir, "test_actioner_goal_frame.png"))
                if data['actioner_goal_pcd'] is not None:
                    actioner_goal_pcd = data['actioner_goal_pcd'][0]
                    save_pcd_image(actioner_goal_pcd, os.path.join(item_dir, "test_actioner_goal_pcd.png"), actioner_goal)
                    save_pcd_ply(actioner_goal_pcd, os.path.join(item_dir, "test_actioner_goal_pcd.ply"), actioner_goal)
                    save_pcd_as_2d_image(actioner_goal_pcd, os.path.join(item_dir, "test_actioner_goal_pcd_2d.png"))
            
            print(f"\n测试数据 {i}:")
            print(f"  当前帧RGB形状: {current_rgb.shape}")
            print(f"  目标帧RGB形状: {goal_rgb.shape}")
            if data['actioner_goal_rgb'] is not None:
                actioner_goal = data['actioner_goal_rgb'][0]
                print(f"  Actioner目标帧RGB形状: {actioner_goal.shape}")
            print(f"  当前帧PCD形状: {current_pcd.shape}")
            print(f"  目标帧PCD形状: {goal_pcd.shape}")

            # 保存测试信息
            with open(os.path.join(item_dir, "test_info.txt"), "w") as f:
                f.write(f"任务: {data['task']}\n")
                f.write(f"当前帧RGB形状: {data['current_rgb'].shape}\n")
                f.write(f"目标帧RGB形状: {data['goal_rgb'].shape}\n")
                f.write(f"instruction形状: {data['instruction'].shape}\n")
                f.write(f"actioner目标帧可用: {data['actioner_goal_rgb'] is not None}\n")
                if data['actioner_goal_rgb'] is not None:
                    f.write(f"actioner目标帧形状: {data['actioner_goal_rgb'].shape}\n")
                f.write(f"注意: 测试数据的RGB包含4个通道（RGB+attention），这里只保存前3个通道\n")

def create_comparison_summary(output_dir="debug_goal_frame_output"):
    """创建比较总结"""
    summary_path = os.path.join(output_dir, "comparison_summary.txt")
    
    with open(summary_path, "w", encoding="utf-8") as f:
        f.write("训练与测试目标帧比较总结\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("重要概念澄清:\n")
        f.write("- RLBench数据集使用关键帧提取（keypoint discovery）\n")
        f.write("- 4-5帧代表任务的关键动作点，不是完整的连续序列\n")
        f.write("- 关键帧通常对应：开始、抓取、移动、放置等关键动作\n")
        f.write("- 目标帧应该是最后一个关键帧（任务完成状态）\n")
        f.write("- 在goal_frame_mode=1下，目标帧在整个关键帧序列中保持固定\n\n")
        
        f.write("目录结构:\n")
        f.write("- training/: 训练数据流水线获取的关键帧序列\n")
        f.write("  - item_X/: 第X个训练数据项\n")
        f.write("    - train_current_tXXX.png: 第XXX关键帧的当前观测\n")
        f.write("    - train_goal_tXXX.png: 第XXX关键帧的目标帧（固定为最后关键帧）\n")
        f.write("    - train_saved_goal_frame.png: 从state_dict[6]保存的最后一帧\n")
        f.write("    - train_info.txt: 详细信息，包括关键帧分析和state_dict[6]信息\n\n")
        
        f.write("- testing/: 测试数据流水线获取的数据\n")
        f.write("  - item_X/: 第X个测试数据项\n")
        f.write("    - test_current_frame.png: 当前帧（演示第一关键帧）\n")
        f.write("    - test_goal_frame.png: 目标帧（演示最后关键帧）\n")
        f.write("    - test_actioner_goal_frame.png: Actioner中的目标帧\n")
        f.write("    - test_info.txt: 详细信息\n\n")
        
        f.write("关键检查要点:\n")
        f.write("1. 关键帧序列验证:\n")
        f.write("   - 4-5个关键帧是正常的任务复杂度\n")
        f.write("   - 关键帧应该显示从开始到完成的关键动作转换\n")
        f.write("   - 目标帧在所有关键帧中应该保持一致\n")
        f.write("   - 最后一个关键帧的当前观测应该与目标帧一致\n\n")
        f.write("2. state_dict[6]验证:\n")
        f.write("   - state_dict[6]保存的是序列的最后一帧（目标帧）\n")
        f.write("   - train_saved_goal_frame.png应该与train_goal_tXXX.png一致\n")
        f.write("   - 这个帧应该代表任务完成后的最终状态\n\n")
        f.write("3. 训练测试对比:\n")
        f.write("   - 比较train_current_t000.png和test_current_frame.png（都是开始状态）\n")
        f.write("   - 比较train_saved_goal_frame.png和test_goal_frame.png（都是完成状态）\n")
        f.write("   - 验证test_goal_frame.png和test_actioner_goal_frame.png是否一致\n\n")
        f.write("4. 问题诊断:\n")
        f.write("   - 如果关键帧数量异常（<3或>8），可能是任务复杂度问题\n")
        f.write("   - 如果关键帧间变化很小，可能是视角问题或任务本身变化不明显\n")
        f.write("   - 如果目标帧不一致，说明数据处理逻辑有问题\n")
        f.write("   - 关键帧提取算法基于夹爪状态变化和机器人停止检测\n")
    
    print(f"已创建比较总结: {summary_path}")

def get_full_episode_data(dataset, episode_id):
    """获取完整演示数据，不受max_episode_length限制"""
    import math
    
    episode_id %= len(dataset._episodes)
    task, variation, file = dataset._episodes[episode_id]
    
    # 直接加载完整演示
    episode = dataset.read_from_cache(file)
    if episode is None:
        return None
    
    # 获取所有帧的ID（不进行chunk分割）
    frame_ids = episode[0]  # 所有帧
    
    print(f"  完整演示帧数: {len(frame_ids)}")
    
    # 获取所有帧的状态
    states = torch.stack([
        episode[1][i] if isinstance(episode[1][i], torch.Tensor)
        else torch.from_numpy(episode[1][i])
        for i in frame_ids
    ])
    
    # 处理相机索引
    if episode[3]:
        cameras = list(episode[3][0].keys())
        index = torch.tensor([cameras.index(c) for c in dataset._cameras])
        states = states[:, index]
    
    # 分离RGB和点云
    rgbs = states[:, :, 0]
    pcds = states[:, :, 1]
    rgbs = dataset._unnormalize_rgb(rgbs)
    
    # 获取保存的最后一帧 (state_dict[6])
    goal_frame_from_saved = None
    if len(episode) > 6 and episode[6] is not None:
        goal_frame_raw = episode[6]
        if not isinstance(goal_frame_raw, torch.Tensor):
            goal_frame_raw = torch.from_numpy(goal_frame_raw)
        
        if episode[3]:
            goal_frame_raw = goal_frame_raw[index]
            
        goal_rgb_saved = goal_frame_raw[:, 0]  # (ncam, 3, H, W)
        goal_pcd_saved = goal_frame_raw[:, 1]  # (ncam, 3, H, W)
        goal_rgb_saved = dataset._unnormalize_rgb(goal_rgb_saved.unsqueeze(0))  # (1, ncam, 3, H, W)
        goal_pcd_saved = goal_pcd_saved.unsqueeze(0)  # (1, ncam, 3, H, W)
        
        goal_frame_from_saved = {
            'rgb': goal_rgb_saved[0],  # (ncam, 3, H, W)
            'pcd': goal_pcd_saved[0]   # (ncam, 3, H, W)
        }
        print(f"  从state_dict[6]获取到目标帧: {goal_rgb_saved.shape}")
    
    # 添加目标帧（演示的最后一帧）
    if dataset._goal_frame_mode == 1:
        # 获取演示的最后一帧作为目标帧
        last_frame_id = episode[0][-1]
        last_state = episode[1][last_frame_id]
        if not isinstance(last_state, torch.Tensor):
            last_state = torch.from_numpy(last_state)
            
        if episode[3]: 
            last_state = last_state[index]
            
        last_rgb = last_state[:, 0]  # (ncam, 3, H, W)
        last_pcd = last_state[:, 1]  # (ncam, 3, H, W)
        last_rgb = dataset._unnormalize_rgb(last_rgb.unsqueeze(0))  # (1, ncam, 3, H, W)
        last_pcd = last_pcd.unsqueeze(0)  # (1, ncam, 3, H, W)
        
        # 将目标帧复制到每个时间步
        goal_rgb = last_rgb.repeat(len(rgbs), 1, 1, 1, 1) 
        goal_pcd = last_pcd.repeat(len(rgbs), 1, 1, 1, 1)
        
        # 将目标帧作为新的视角添加
        rgbs = torch.cat([rgbs, goal_rgb], dim=1)  # (T, ncam+1, 3, H, W)
        pcds = torch.cat([pcds, goal_pcd], dim=1)
    
    # 获取动作
    action = torch.cat([episode[2][i] for i in frame_ids])
    
    # 获取instruction
    if dataset._instructions:
        instr = random.choice(list(dataset._instructions[task][variation]))
        instr = instr[None].repeat(len(rgbs), 1, 1)
    else:
        instr = torch.zeros((rgbs.shape[0], 53, 512))
    
    # 获取gripper
    gripper = torch.cat([episode[4][i] for i in frame_ids])
    
    return {
        "task": [task for _ in frame_ids],
        "rgbs": rgbs,
        "pcds": pcds,
        "action": action,
        "instr": instr,
        "curr_gripper": gripper,
        "full_episode_length": len(frame_ids),
        "goal_frame_from_saved": goal_frame_from_saved  # 新增：从state_dict[6]获取的目标帧
    }

def inspect_raw_data_files(dataset_path, task='put_toilet_roll_on_stand', variation=0):
    """直接检查原始数据文件的结构"""
    print(f"\n=== 检查原始数据文件结构 ===")
    
    from datasets.utils import loader
    import glob
    
    # 查找数据文件
    data_dir = Path(dataset_path) / f"{task}+{variation}"
    if not data_dir.exists():
        print(f"数据目录不存在: {data_dir}")
        return
    
    # 找到所有数据文件
    data_files = list(data_dir.glob("*.dat")) + list(data_dir.glob("*.pkl")) + list(data_dir.glob("*.npy"))
    print(f"找到 {len(data_files)} 个数据文件")
    
    # 检查前几个文件
    for i, file_path in enumerate(data_files[:3]):
        print(f"\n检查文件 {i}: {file_path.name}")
        try:
            # 直接加载数据文件
            episode_data = loader(file_path)
            
            if episode_data is None:
                print(f"  无法加载文件")
                continue
                
            print(f"  数据类型: {type(episode_data)}")
            if isinstance(episode_data, (list, tuple)):
                print(f"  数据长度: {len(episode_data)}")
                for j, item in enumerate(episode_data):
                    if isinstance(item, (list, tuple)):
                        print(f"    item[{j}] 长度: {len(item)}")
                        if j == 0:  # frame_ids
                            print(f"    frame_ids: {item[:10]}..." if len(item) > 10 else f"    frame_ids: {item}")
                    else:
                        print(f"    item[{j}] 类型: {type(item)}")
                        
                # 重点检查frame_ids (episode[0]) 和 obs_tensors (episode[1])
                if len(episode_data) > 0:
                    frame_ids = episode_data[0]
                    print(f"  原始frame_ids数量: {len(frame_ids)}")
                    print(f"  frame_ids范围: {min(frame_ids)} - {max(frame_ids)}")
                    
                if len(episode_data) > 1:
                    obs_tensors = episode_data[1]
                    print(f"  obs_tensors类型: {type(obs_tensors)}")
                    if hasattr(obs_tensors, 'shape'):
                        print(f"  obs_tensors形状: {obs_tensors.shape}")
                    elif isinstance(obs_tensors, (list, tuple, np.ndarray)):
                        print(f"  obs_tensors长度: {len(obs_tensors)}")
                        if len(obs_tensors) > 0:
                            print(f"  第一个obs形状: {obs_tensors[0].shape if hasattr(obs_tensors[0], 'shape') else type(obs_tensors[0])}")
                            
        except Exception as e:
            print(f"  加载文件时出错: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    print("开始调试目标帧一致性...")
    print("注意：RLBench数据集使用关键帧提取，4-5帧是正常的演示长度")
    
    # 设置随机种子以便复现
    torch.manual_seed(0)
    np.random.seed(0)
    random.seed(0)
    
    # 首先检查原始数据文件结构
    dataset_path = "/data/wangyiwen/3d_diffuser_actor/data/peract/packaged_highres/train"
    inspect_raw_data_files(dataset_path)
    
    # 获取训练数据
    training_data = get_data_from_training_pipeline()
    
    # 获取测试数据
    testing_data = get_data_from_testing_pipeline()
    
    # 保存比较数据
    save_comparison_data(training_data, testing_data)
    
    # 创建比较总结
    create_comparison_summary()
    
    print("\n" + "="*50)
    print("调试完成！")
    print("关键发现：")
    print("- RLBench数据集使用关键帧提取（keypoint discovery）")
    print("- 4-5帧代表任务的关键动作点，这是正常的")
    print("- 目标帧应该是最后一个关键帧（任务完成状态）")
    print("请检查 debug_goal_frame_output/ 目录中的图像和信息文件")
    print("="*50)

if __name__ == "__main__":
    main() 