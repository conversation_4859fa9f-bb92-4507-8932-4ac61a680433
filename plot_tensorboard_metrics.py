#!/usr/bin/env python3
"""
TensorBoard日志可视化脚本
用法: python plot_tensorboard_metrics.py <log_path> [--save-dir output] [--format png]
"""

from tensorboard.backend.event_processing import event_accumulator
import pandas as pd
import matplotlib.pyplot as plt
import sys
import os
import argparse
from pathlib import Path

def setup_matplotlib():
    """设置matplotlib的中文字体和样式"""
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
    plt.style.use('seaborn-v0_8' if 'seaborn-v0_8' in plt.style.available else 'default')

def load_tensorboard_data(log_path):
    """加载TensorBoard数据"""
    print(f"正在加载TensorBoard日志: {log_path}")
    ea = event_accumulator.EventAccumulator(log_path)
    ea.Reload()
    
    print(f"可用的标签类型: {list(ea.Tags().keys())}")
    
    if 'scalars' not in ea.Tags():
        print("警告: 没有找到标量数据")
        return {}
    
    scalar_tags = ea.Tags()['scalars']
    print(f"找到 {len(scalar_tags)} 个标量指标: {scalar_tags}")
    
    # 提取所有标量数据
    data = {}
    for tag in scalar_tags:
        events = ea.Scalars(tag)
        df = pd.DataFrame([(e.step, e.wall_time, e.value) for e in events], 
                         columns=['step', 'timestamp', 'value'])
        data[tag] = df
        print(f"指标 '{tag}': {len(df)} 个数据点")
    
    return data

def plot_metrics(data, save_dir=None, format='png'):
    """绘制指标图表"""
    if not data:
        print("没有数据可以绘制")
        return
    
    # 创建保存目录
    if save_dir:
        Path(save_dir).mkdir(parents=True, exist_ok=True)
    
    # 设置图表样式
    setup_matplotlib()
    
    # 为每个指标创建单独的图表
    for tag, df in data.items():
        plt.figure(figsize=(12, 6))
        
        # 绘制主图
        plt.subplot(1, 2, 1)
        plt.plot(df['step'], df['value'], linewidth=2, alpha=0.8)
        plt.title(f'{tag} vs Steps', fontsize=14, fontweight='bold')
        plt.xlabel('Training Steps')
        plt.ylabel('Value')
        plt.grid(True, alpha=0.3)
        
        # 添加统计信息
        mean_val = df['value'].mean()
        std_val = df['value'].std()
        min_val = df['value'].min()
        max_val = df['value'].max()
        
        plt.axhline(y=mean_val, color='red', linestyle='--', alpha=0.7, label=f'Mean: {mean_val:.4f}')
        plt.legend()
        
        # 绘制时间序列图
        plt.subplot(1, 2, 2)
        # 将时间戳转换为相对时间（小时）
        start_time = df['timestamp'].min()
        relative_time = (df['timestamp'] - start_time) / 3600  # 转换为小时
        
        plt.plot(relative_time, df['value'], linewidth=2, alpha=0.8, color='orange')
        plt.title(f'{tag} vs Time', fontsize=14, fontweight='bold')
        plt.xlabel('Training Time (hours)')
        plt.ylabel('Value')
        plt.grid(True, alpha=0.3)
        
        # 添加统计信息文本框
        stats_text = f'Statistics:\nMean: {mean_val:.4f}\nStd: {std_val:.4f}\nMin: {min_val:.4f}\nMax: {max_val:.4f}\nPoints: {len(df)}'
        plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        
        # 保存图表
        if save_dir:
            safe_tag = tag.replace('/', '_').replace('\\', '_')
            filename = f"{safe_tag}.{format}"
            filepath = os.path.join(save_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            print(f"图表已保存: {filepath}")
        
        plt.show()

def plot_all_metrics_combined(data, save_dir=None, format='png'):
    """在一个图表中绘制所有指标（归一化后）"""
    if not data or len(data) == 0:
        return
    
    plt.figure(figsize=(15, 10))
    
    # 创建子图
    n_metrics = len(data)
    cols = min(3, n_metrics)
    rows = (n_metrics + cols - 1) // cols
    
    for i, (tag, df) in enumerate(data.items(), 1):
        plt.subplot(rows, cols, i)
        plt.plot(df['step'], df['value'], linewidth=2)
        plt.title(tag, fontsize=12, fontweight='bold')
        plt.xlabel('Steps')
        plt.ylabel('Value')
        plt.grid(True, alpha=0.3)
        
        # 添加最新值标注
        if len(df) > 0:
            latest_val = df['value'].iloc[-1]
            plt.text(0.95, 0.95, f'Latest: {latest_val:.4f}', 
                    transform=plt.gca().transAxes, ha='right', va='top',
                    bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存组合图表
    if save_dir:
        filename = f"all_metrics_combined.{format}"
        filepath = os.path.join(save_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        print(f"组合图表已保存: {filepath}")
    
    plt.show()

def main():
    parser = argparse.ArgumentParser(description='绘制TensorBoard指标变化图')
    parser.add_argument('log_path', nargs='?', default='.', 
                       help='TensorBoard日志路径 (默认: 当前目录)')
    parser.add_argument('--save-dir', default=None,
                       help='图表保存目录 (默认: 不保存)')
    parser.add_argument('--format', default='png', choices=['png', 'jpg', 'pdf', 'svg'],
                       help='图表保存格式 (默认: png)')
    parser.add_argument('--combined', action='store_true',
                       help='同时生成组合图表')
    
    args = parser.parse_args()
    
    try:
        # 加载数据
        data = load_tensorboard_data(args.log_path)
        
        if not data:
            print("没有找到可绘制的数据")
            return
        
        # 绘制单独的指标图表
        plot_metrics(data, args.save_dir, args.format)
        
        # 绘制组合图表
        if args.combined:
            plot_all_metrics_combined(data, args.save_dir, args.format)
        
        print(f"\n绘制完成! 共处理了 {len(data)} 个指标")
        
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
