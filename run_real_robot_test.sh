#!/bin/bash

# 真机测试运行脚本
# 使用方法: ./run_real_robot_test.sh

# 设置默认参数
CHECKPOINT="./train_logs/Actor_18Peract_96Demo_plug_front/diffusion_multitask-C120-B5-lr1e-4-DI1-2-H3-DT100/last.pth"
GRIPPER_LOC_BOUNDS="tasks/18_peract_tasks_location_bounds.json"
CAMERA_SERIAL="311322301369"
ROBOT_URL="http://localhost:8080/"
MAX_STEPS=50
ACTION_DELAY=1.0

# 检查必要文件是否存在
if [ ! -f "$CHECKPOINT" ]; then
    echo "错误: 模型检查点文件不存在: $CHECKPOINT"
    echo "请确保模型已经训练完成或指定正确的检查点路径"
    exit 1
fi

if [ ! -f "$GRIPPER_LOC_BOUNDS" ]; then
    echo "错误: 夹爪位置边界文件不存在: $GRIPPER_LOC_BOUNDS"
    exit 1
fi

# 检查RealSense相机
echo "检查RealSense相机..."
python3 -c "
import pyrealsense2 as rs
devices = rs.context().devices
serials = [d.get_info(rs.camera_info.serial_number) for d in devices]
print(f'找到的相机序列号: {serials}')
if '$CAMERA_SERIAL' not in serials:
    print(f'警告: 指定的相机序列号 $CAMERA_SERIAL 未找到')
    exit(1)
else:
    print(f'找到指定的相机: $CAMERA_SERIAL')
"

if [ $? -ne 0 ]; then
    echo "错误: RealSense相机检查失败"
    exit 1
fi

# 检查机器人API连接
echo "检查机器人API连接..."
curl -s -X POST "$ROBOT_URL"getstate -H "Content-Type: application/json" > /dev/null
if [ $? -ne 0 ]; then
    echo "警告: 无法连接到机器人API: $ROBOT_URL"
    echo "请确保机器人服务器正在运行"
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 设置CUDA设备
export CUDA_VISIBLE_DEVICES=0

# 运行真机测试
echo "开始真机测试..."
echo "模型检查点: $CHECKPOINT"
echo "相机序列号: $CAMERA_SERIAL"
echo "机器人URL: $ROBOT_URL"
echo "最大步数: $MAX_STEPS"
echo "动作延迟: $ACTION_DELAY"
echo ""

python3 real_robot_test.py \
    --checkpoint "$CHECKPOINT" \
    --gripper_loc_bounds "$GRIPPER_LOC_BOUNDS" \
    --backbone clip \
    --image_size "128,128" \
    --embedding_dim 120 \
    --num_vis_ins_attn_layers 2 \
    --use_instruction 0 \
    --fps_subsampling_factor 5 \
    --rotation_parametrization 6D \
    --quaternion_format xyzw \
    --diffusion_timesteps 100 \
    --num_history 3 \
    --relative_action 0 \
    --lang_enhanced 0 \
    --interpolation_length 2 \
    --device cuda \
    --camera_serial "$CAMERA_SERIAL" \
    --robot_url "$ROBOT_URL" \
    --max_steps "$MAX_STEPS" \
    --action_delay "$ACTION_DELAY"

echo "真机测试完成" 