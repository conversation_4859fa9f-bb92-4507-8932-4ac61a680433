#!/usr/bin/env python3
"""
数据范围分析脚本
参考convert.py，读取转换后的数据文件，输出每个数据项的取值范围
输入：数据文件路径
"""

import pickle
import numpy as np
import torch
import argparse
from pathlib import Path
import sys
from scipy.spatial.transform import Rotation

def analyze_tensor_range(tensor, name):
    """分析张量的取值范围"""
    if isinstance(tensor, torch.Tensor):
        tensor = tensor.numpy()
    
    if isinstance(tensor, np.ndarray):
        # 检查是否包含NaN或Inf
        has_nan = np.isnan(tensor).any()
        has_inf = np.isinf(tensor).any()
        
        # 获取有效值（非NaN非Inf）
        valid_mask = ~(np.isnan(tensor) | np.isinf(tensor))
        valid_data = tensor[valid_mask]
        
        if len(valid_data) > 0:
            min_val = valid_data.min()
            max_val = valid_data.max()
            mean_val = valid_data.mean()
            std_val = valid_data.std()
        else:
            min_val = max_val = mean_val = std_val = float('nan')
        
        print(f"  {name}:")
        print(f"    形状: {tensor.shape}")
        print(f"    数据类型: {tensor.dtype}")
        print(f"    取值范围: [{min_val:.6f}, {max_val:.6f}]")
        print(f"    均值: {mean_val:.6f}, 标准差: {std_val:.6f}")
        print(f"    异常值: NaN={np.sum(np.isnan(tensor))}, Inf={np.sum(np.isinf(tensor))}")
        print(f"    有效值数量: {len(valid_data)}/{tensor.size}")
        
        return {
            'shape': tensor.shape,
            'dtype': str(tensor.dtype),
            'min': float(min_val),
            'max': float(max_val),
            'mean': float(mean_val),
            'std': float(std_val),
            'nan_count': int(np.sum(np.isnan(tensor))),
            'inf_count': int(np.sum(np.isinf(tensor))),
            'valid_count': len(valid_data),
            'total_count': tensor.size
        }
    else:
        print(f"  {name}: 非数值类型 {type(tensor)}")
        return {'type': str(type(tensor))}

def analyze_trajectory_components(combined_traj, results):
    """
    将轨迹数据按照xyz和欧拉角划分开计算均值和统计信息

    Args:
        combined_traj: torch.Tensor, 形状为 (N, 8)，格式为 [x, y, z, qx, qy, qz, qw, gripper]
        results: dict, 存储分析结果的字典
    """
    print("\n  轨迹数据分组分析:")

    if isinstance(combined_traj, torch.Tensor):
        traj_array = combined_traj.numpy()
    else:
        traj_array = combined_traj

    # 分离各个组件
    positions = traj_array[:, :3]  # x, y, z
    quaternions = traj_array[:, 3:7]  # qx, qy, qz, qw
    grippers = traj_array[:, 7:8]  # gripper

    # 分析位置组件 (xyz)
    results['traj_positions'] = analyze_tensor_range(positions, "轨迹-位置 (x,y,z)")

    # 分析夹爪组件
    results['traj_grippers'] = analyze_tensor_range(grippers, "轨迹-夹爪状态")

    # 将四元数转换为欧拉角进行分析
    try:
        # 检查四元数的有效性
        valid_quat_mask = ~(np.isnan(quaternions).any(axis=1) | np.isinf(quaternions).any(axis=1))
        valid_quaternions = quaternions[valid_quat_mask]

        if len(valid_quaternions) > 0:
            # 归一化四元数（确保单位长度）
            quat_norms = np.linalg.norm(valid_quaternions, axis=1)
            valid_norm_mask = quat_norms > 1e-6  # 避免零向量

            if np.sum(valid_norm_mask) > 0:
                normalized_quats = valid_quaternions[valid_norm_mask]
                normalized_quats = normalized_quats / np.linalg.norm(normalized_quats, axis=1, keepdims=True)

                # 转换为欧拉角 (roll, pitch, yaw)
                # 注意：scipy使用 [x, y, z, w] 格式，我们的数据是 [qx, qy, qz, qw]
                rotations = Rotation.from_quat(normalized_quats)  # 输入格式：[x, y, z, w]
                euler_angles = rotations.as_euler('xyz', degrees=False)  # 输出：[roll, pitch, yaw] 弧度

                # 分析欧拉角
                results['traj_quaternions'] = analyze_tensor_range(normalized_quats, "轨迹-四元数 (qx,qy,qz,qw)")
                results['traj_euler_angles'] = analyze_tensor_range(euler_angles, "轨迹-欧拉角 (roll,pitch,yaw)")

                # 分别分析每个欧拉角分量
                roll_angles = euler_angles[:, 0:1]
                pitch_angles = euler_angles[:, 1:2]
                yaw_angles = euler_angles[:, 2:3]

                results['traj_roll'] = analyze_tensor_range(roll_angles, "轨迹-Roll角 (绕X轴)")
                results['traj_pitch'] = analyze_tensor_range(pitch_angles, "轨迹-Pitch角 (绕Y轴)")
                results['traj_yaw'] = analyze_tensor_range(yaw_angles, "轨迹-Yaw角 (绕Z轴)")

                print(f"    成功转换 {len(euler_angles)} 个四元数到欧拉角")
                print(f"    欧拉角范围 (弧度):")
                print(f"      Roll:  [{euler_angles[:, 0].min():.6f}, {euler_angles[:, 0].max():.6f}]")
                print(f"      Pitch: [{euler_angles[:, 1].min():.6f}, {euler_angles[:, 1].max():.6f}]")
                print(f"      Yaw:   [{euler_angles[:, 2].min():.6f}, {euler_angles[:, 2].max():.6f}]")
                print(f"    欧拉角范围 (度):")
                print(f"      Roll:  [{np.degrees(euler_angles[:, 0].min()):.2f}, {np.degrees(euler_angles[:, 0].max()):.2f}]")
                print(f"      Pitch: [{np.degrees(euler_angles[:, 1].min()):.2f}, {np.degrees(euler_angles[:, 1].max()):.2f}]")
                print(f"      Yaw:   [{np.degrees(euler_angles[:, 2].min()):.2f}, {np.degrees(euler_angles[:, 2].max()):.2f}]")
            else:
                print("    警告: 没有有效的归一化四元数")
                results['traj_quaternions'] = analyze_tensor_range(quaternions, "轨迹-四元数 (原始数据)")
        else:
            print("    警告: 没有有效的四元数数据")
            results['traj_quaternions'] = analyze_tensor_range(quaternions, "轨迹-四元数 (原始数据)")

    except Exception as e:
        print(f"    错误: 四元数转欧拉角失败: {e}")
        results['traj_quaternions'] = analyze_tensor_range(quaternions, "轨迹-四元数 (原始数据)")

    # 分别分析每个位置分量
    x_positions = positions[:, 0:1]
    y_positions = positions[:, 1:2]
    z_positions = positions[:, 2:3]

    results['traj_x'] = analyze_tensor_range(x_positions, "轨迹-X坐标")
    results['traj_y'] = analyze_tensor_range(y_positions, "轨迹-Y坐标")
    results['traj_z'] = analyze_tensor_range(z_positions, "轨迹-Z坐标")

def analyze_episode_data(episode_data):
    """分析单个episode的数据范围"""
    print("=" * 60)
    print("Episode 数据结构分析")
    print("=" * 60)
    
    if not isinstance(episode_data, list) or len(episode_data) != 6:
        print("错误: 数据结构不符合预期，应该是包含6个元素的列表")
        return None
    
    results = {}
    
    # 1. frame_ids
    print("\n1. Frame IDs:")
    frame_ids = episode_data[0]
    print(f"  类型: {type(frame_ids)}")
    print(f"  长度: {len(frame_ids)}")
    print(f"  内容: {frame_ids}")
    results['frame_ids'] = {'type': str(type(frame_ids)), 'length': len(frame_ids), 'content': frame_ids}
    
    # 2. obs_tensors (RGB-D观测)
    print("\n2. 观测张量 (RGB-D):")
    obs_tensors = episode_data[1]
    if isinstance(obs_tensors, np.ndarray):
        print(f"  整体形状: {obs_tensors.shape}")
        print(f"  结构说明: (时间步={obs_tensors.shape[0]}, 相机数={obs_tensors.shape[1]}, 模态数={obs_tensors.shape[2]}, 通道={obs_tensors.shape[3]}, 高={obs_tensors.shape[4]}, 宽={obs_tensors.shape[5]})")
        
        # 分析RGB数据 (模态索引0)
        rgb_data = obs_tensors[:, :, 0, :, :, :]  # (T, n_cam, 3, H, W)
        results['rgb'] = analyze_tensor_range(rgb_data, "RGB数据")
        
        # 分析XYZ数据 (模态索引1)
        xyz_data = obs_tensors[:, :, 1, :, :, :]  # (T, n_cam, 3, H, W)
        results['xyz'] = analyze_tensor_range(xyz_data, "XYZ点云数据")
    
    # 3. action_list (动作列表)
    print("\n3. 动作列表:")
    action_list = episode_data[2]
    print(f"  类型: {type(action_list)}")
    print(f"  长度: {len(action_list)}")
    if len(action_list) > 0:
        # 合并所有动作进行分析
        all_actions = torch.cat(action_list, dim=0)  # (N, 8)
        results['actions'] = analyze_tensor_range(all_actions, "动作数据 (8维)")
        
        # 分别分析位置、旋转、夹爪
        positions = all_actions[:, :3]  # x, y, z
        rotations = all_actions[:, 3:7]  # qx, qy, qz, qw
        grippers = all_actions[:, 7:8]  # gripper
        
        results['action_positions'] = analyze_tensor_range(positions, "动作-位置 (x,y,z)")
        results['action_rotations'] = analyze_tensor_range(rotations, "动作-旋转 (四元数)")
        results['action_grippers'] = analyze_tensor_range(grippers, "动作-夹爪状态")
    
    # 4. attn_indices (注意力索引)
    print("\n4. 注意力索引:")
    attn_indices = episode_data[3]
    print(f"  类型: {type(attn_indices)}")
    print(f"  长度: {len(attn_indices)}")
    if len(attn_indices) > 0:
        print(f"  第0帧相机: {list(attn_indices[0].keys())}")
        # 收集所有注意力坐标
        all_u_coords = []
        all_v_coords = []
        for attn_dict in attn_indices:
            for cam_name, (u, v) in attn_dict.items():
                all_u_coords.append(u)
                all_v_coords.append(v)
        
        if all_u_coords:
            u_array = np.array(all_u_coords)
            v_array = np.array(all_v_coords)
            results['attention_u'] = analyze_tensor_range(u_array, "注意力坐标-U")
            results['attention_v'] = analyze_tensor_range(v_array, "注意力坐标-V")
    
    # 5. gripper_list (夹爪位置列表)
    print("\n5. 夹爪位置列表:")
    gripper_list = episode_data[4]
    print(f"  类型: {type(gripper_list)}")
    print(f"  长度: {len(gripper_list)}")
    if len(gripper_list) > 0:
        # 合并所有夹爪数据进行分析
        all_grippers = torch.cat(gripper_list, dim=0)  # (N, 8)
        results['gripper_states'] = analyze_tensor_range(all_grippers, "夹爪状态数据 (8维)")
    
    # 6. trajectory_list (轨迹列表)
    print("\n6. 轨迹列表:")
    trajectory_list = episode_data[5]
    print(f"  类型: {type(trajectory_list)}")
    print(f"  长度: {len(trajectory_list)}")
    if len(trajectory_list) > 0:
        # 分析每条轨迹
        for i, traj in enumerate(trajectory_list[:3]):  # 只显示前3条轨迹的详情
            if isinstance(traj, torch.Tensor):
                print(f"  轨迹{i}: 形状={traj.shape}")
        
        # 合并所有轨迹数据进行整体分析
        all_traj_data = []
        for traj in trajectory_list:
            if isinstance(traj, torch.Tensor) and traj.numel() > 0:
                all_traj_data.append(traj)
        
        if all_traj_data:
            combined_traj = torch.cat(all_traj_data, dim=0)  # (总数, 8)
            results['trajectories'] = analyze_tensor_range(combined_traj, "轨迹数据 (8维)")

            # 将轨迹数据按照xyz和欧拉角划分开计算
            analyze_trajectory_components(combined_traj, results)
    
    return results

def analyze_data_file(file_path):
    """分析单个数据文件"""
    file_path = Path(file_path)
    
    if not file_path.exists():
        print(f"错误: 文件 {file_path} 不存在")
        return None
    
    print(f"正在分析文件: {file_path}")
    
    try:
        with open(file_path, 'rb') as f:
            episode_data = pickle.load(f)
        
        results = analyze_episode_data(episode_data)
        return results
        
    except Exception as e:
        print(f"加载文件时出错: {str(e)}")
        return None

def main():
    parser = argparse.ArgumentParser(description='分析转换后数据文件的取值范围')
    parser.add_argument('data_path', type=str, help='数据文件路径 (.pkl)')
    parser.add_argument('--output', type=str, help='输出结果到文件 (可选)')
    
    args = parser.parse_args()
    
    # 分析数据文件
    results = analyze_data_file(args.data_path)
    
    if results is None:
        sys.exit(1)
    
    # 输出总结
    print("\n" + "=" * 60)
    print("数据范围总结")
    print("=" * 60)
    
    for key, value in results.items():
        if isinstance(value, dict) and 'min' in value and 'max' in value:
            print(f"{key}: [{value['min']:.6f}, {value['max']:.6f}]")
    
    # 保存结果到文件
    if args.output:
        output_path = Path(args.output)
        with open(output_path, 'w') as f:
            f.write("数据范围分析结果\n")
            f.write("=" * 60 + "\n")
            f.write(f"文件: {args.data_path}\n\n")
            
            for key, value in results.items():
                f.write(f"{key}:\n")
                if isinstance(value, dict):
                    for k, v in value.items():
                        f.write(f"  {k}: {v}\n")
                else:
                    f.write(f"  {value}\n")
                f.write("\n")
        
        print(f"\n结果已保存到: {output_path}")

if __name__ == "__main__":
    main()
