#!/usr/bin/env python3

import os
import sys
import pickle
from pathlib import Path
from subprocess import call

def rearrange_rlbench_data(root_dir, task):
    """
    重排RLBench数据从all_variations结构到variation{N}结构
    参考: data_preprocessing/rearrange_rlbench_demos.py
    """
    print(f"重排任务 {task} 的数据...")
    
    all_variations_dir = Path(root_dir) / task / "all_variations" / "episodes"
    if not all_variations_dir.exists():
        print(f"错误: all_variations目录不存在: {all_variations_dir}")
        return False
    
    episodes = list(all_variations_dir.glob("episode*"))
    print(f"找到 {len(episodes)} 个episodes")
    
    seen_variations = {}
    
    for episode_dir in episodes:
        episode_num = int(episode_dir.name.replace('episode', ''))
        
        # 读取variation_number
        var_file = episode_dir / "variation_number.pkl"
        if not var_file.exists():
            print(f"警告: 找不到variation_number.pkl在 {episode_dir}")
            continue
            
        try:
            with open(var_file, 'rb') as f:
                variation = pickle.load(f)
        except Exception as e:
            print(f"错误: 无法读取variation_number.pkl: {e}")
            continue
        
        print(f"Episode {episode_num} -> Variation {variation}")
        
        # 创建variation目录
        var_dir = Path(root_dir) / task / f"variation{variation}"
        episodes_dir = var_dir / "episodes"
        episodes_dir.mkdir(parents=True, exist_ok=True)
        
        # 跟踪每个variation的episode数量
        if variation not in seen_variations:
            seen_variations[variation] = []
        seen_variations[variation].append(episode_num)
        
        # 处理variation_descriptions.pkl
        var_desc_src = episode_dir / "variation_descriptions.pkl"
        var_desc_dst = var_dir / "variation_descriptions.pkl"
        
        if var_desc_src.exists():
            if var_desc_dst.exists():
                # 验证内容一致
                try:
                    with open(var_desc_src, 'rb') as f:
                        data1 = pickle.load(f)
                    with open(var_desc_dst, 'rb') as f:
                        data2 = pickle.load(f)
                    if data1 != data2:
                        print(f"警告: variation_descriptions.pkl内容不一致")
                except Exception as e:
                    print(f"警告: 无法验证variation_descriptions.pkl: {e}")
            else:
                # 创建符号链接
                try:
                    var_desc_dst.symlink_to(var_desc_src.resolve())
                    print(f"创建符号链接: {var_desc_dst} -> {var_desc_src}")
                except Exception as e:
                    print(f"警告: 无法创建符号链接: {e}")
        
        # 创建episode符号链接
        ep_id = len(seen_variations[variation]) - 1
        episode_link = episodes_dir / f"episode{ep_id}"
        
        if not episode_link.exists():
            try:
                episode_link.symlink_to(episode_dir.resolve())
                print(f"创建episode链接: {episode_link} -> {episode_dir}")
            except Exception as e:
                print(f"错误: 无法创建episode符号链接: {e}")
    
    print(f"重排完成! 创建了 {len(seen_variations)} 个variations:")
    for var, episodes in seen_variations.items():
        print(f"  Variation {var}: {len(episodes)} episodes")
    
    return True

def main():
    root_dir = "/data/wangyiwen/3d_diffuser_actor/data/peract/raw/train"
    task = "phone_on_base"
    
    print(f"开始重排RLBench数据...")
    print(f"根目录: {root_dir}")
    print(f"任务: {task}")
    
    if not Path(root_dir).exists():
        print(f"错误: 根目录不存在: {root_dir}")
        return
    
    success = rearrange_rlbench_data(root_dir, task)
    
    if success:
        print("\n✓ 数据重排成功!")
        print("现在可以运行 save_pcd_2d.py 了")
    else:
        print("\n✗ 数据重排失败")

if __name__ == "__main__":
    main()
