Arguments:
{'action_dim': 8,
 'backbone': 'clip',
 'cameras': ('left_shoulder', 'right_shoulder', 'wrist', 'front'),
 'checkpoint': PosixPath('train_logs/Actor_18Peract_100Demo_multitask/diffusion_multitask-C120-B8-lr1e-4-DI1-2-H3-DT100/best.pth'),
 'collision_checking': 0,
 'data_dir': PosixPath('data/peract/raw/test'),
 'dense_interpolation': 1,
 'device': 'cuda',
 'diffusion_timesteps': 100,
 'embedding_dim': 120,
 'fine_sampling_ball_diameter': 0.16,
 'fps_subsampling_factor': 5,
 'gp_emb_tying': 1,
 'gripper_loc_bounds_buffer': 0.04,
 'gripper_loc_bounds_file': 'tasks/18_peract_tasks_location_bounds.json',
 'headless': 1,
 'image_size': '256,256',
 'instructions': PosixPath('instructions/peract/instructions.pkl'),
 'interpolation_length': 2,
 'lang_enhanced': 0,
 'max_steps': 25,
 'max_tries': 2,
 'num_episodes': 5,
 'num_ghost_point_cross_attn_layers': 2,
 'num_ghost_points': 10000,
 'num_ghost_points_val': 10000,
 'num_history': 3,
 'num_query_cross_attn_layers': 2,
 'num_sampling_level': 3,
 'num_vis_ins_attn_layers': 2,
 'output_file': PosixPath('eval_logs/3d_diffuser_actor/seed0/place_wine_at_rack_location.json'),
 'predict_trajectory': 1,
 'quaternion_format': 'xyzw',
 'regress_position_offset': 0,
 'relative_action': 0,
 'rotation_parametrization': '6D',
 'seed': 0,
 'single_task_gripper_loc_bounds': 0,
 'tasks': ('place_wine_at_rack_location',),
 'test_model': '3d_diffuser_actor',
 'use_instruction': 1,
 'variations': (0, 1, 2, 3, 4, 5),
 'verbose': 1,
 'weight_tying': 1}
----------------------------------------------------------------------------------------------------
Loading model from train_logs/Actor_18Peract_100Demo_multitask/diffusion_multitask-C120-B8-lr1e-4-DI1-2-H3-DT100/best.pth
Gripper workspace
Gripper workspace size: [0.75823578 1.07414986 0.79873248]

Starting demo 0
Predict Trajectory
Step 0

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:03<00:00,  3.44s/it]
100%|██████████| 1/1 [00:03<00:00,  3.44s/it]
Predict Trajectory
Step 1

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:04<00:00,  4.07s/it]
100%|██████████| 1/1 [00:04<00:00,  4.07s/it]
Predict Trajectory
Step 2

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:03<00:00,  3.83s/it]
100%|██████████| 1/1 [00:03<00:00,  3.83s/it]
Predict Trajectory
Step 3

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:01<00:00,  1.16s/it]
100%|██████████| 1/1 [00:01<00:00,  1.16s/it]
Predict Trajectory
Step 4

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:00<00:00,  1.01it/s]
100%|██████████| 1/1 [00:00<00:00,  1.01it/s]
Predict Trajectory
Step 5

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:04<00:00,  4.78s/it]
100%|██████████| 1/1 [00:04<00:00,  4.78s/it]
Predict Trajectory
Step 6

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:04<00:00,  4.73s/it]
100%|██████████| 1/1 [00:04<00:00,  4.73s/it]
Predict Trajectory
Step 7

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:02<00:00,  2.67s/it]
100%|██████████| 1/1 [00:02<00:00,  2.67s/it]
Predict Trajectory
Step 8

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:03<00:00,  3.24s/it]
100%|██████████| 1/1 [00:03<00:00,  3.24s/it]
Predict Trajectory
Step 9

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:01<00:00,  1.51s/it]
100%|██████████| 1/1 [00:01<00:00,  1.51s/it]
Predict Trajectory
Step 10

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:01<00:00,  1.17s/it]
100%|██████████| 1/1 [00:01<00:00,  1.17s/it]
Predict Trajectory
Step 11

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:02<00:00,  2.49s/it]
100%|██████████| 1/1 [00:02<00:00,  2.49s/it]
Predict Trajectory
Step 12

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:02<00:00,  2.85s/it]
100%|██████████| 1/1 [00:02<00:00,  2.85s/it]
Predict Trajectory
Step 13

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:01<00:00,  1.98s/it]
100%|██████████| 1/1 [00:01<00:00,  1.98s/it]
Predict Trajectory
Step 14

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:01<00:00,  1.70s/it]
100%|██████████| 1/1 [00:01<00:00,  1.70s/it]
Predict Trajectory
Step 15

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:01<00:00,  1.79s/it]
100%|██████████| 1/1 [00:01<00:00,  1.79s/it]
Predict Trajectory
Step 16

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:00<00:00,  1.06it/s]
100%|██████████| 1/1 [00:00<00:00,  1.06it/s]
Predict Trajectory
Step 17

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:01<00:00,  1.16s/it]
100%|██████████| 1/1 [00:01<00:00,  1.16s/it]
Predict Trajectory
Step 18

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:02<00:00,  2.16s/it]
100%|██████████| 1/1 [00:02<00:00,  2.16s/it]
Predict Trajectory
Step 19

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:00<00:00,  1.28it/s]
100%|██████████| 1/1 [00:00<00:00,  1.28it/s]
Predict Trajectory
Step 20

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:00<00:00,  1.44it/s]
100%|██████████| 1/1 [00:00<00:00,  1.44it/s]
Predict Trajectory
Step 21

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:02<00:00,  2.07s/it]
100%|██████████| 1/1 [00:02<00:00,  2.07s/it]
Predict Trajectory
Step 22

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:02<00:00,  2.10s/it]
100%|██████████| 1/1 [00:02<00:00,  2.10s/it]
Predict Trajectory
Step 23

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:01<00:00,  1.82s/it]
100%|██████████| 1/1 [00:01<00:00,  1.82s/it]
Predict Trajectory
Step 24

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:01<00:00,  1.74s/it]
100%|██████████| 1/1 [00:01<00:00,  1.74s/it]
place_wine_at_rack_location Variation 0 Demo 0 Reward 0.00 max_reward 0.00 SR: 0/1 SR: 0.00/1 # valid demos 1

Starting demo 1
Predict Trajectory
Step 0

  0%|          | 0/1 [00:00<?, ?it/s]
100%|██████████| 1/1 [00:01<00:00,  1.40s/it]
100%|██████████| 1/1 [00:01<00:00,  1.40s/it]
Predict Trajectory
Step 1

  0%|          | 0/1 [00:00<?, ?it/s]
  0%|          | 0/1 [00:00<?, ?it/s]
Traceback (most recent call last):
  File "online_evaluation_rlbench/evaluate_policy.py", line 190, in <module>
    var_success_rates = env.evaluate_task_on_multiple_variations(
  File "/data/wangyiwen/3d_diffuser_actor/utils/utils_with_rlbench.py", line 443, in evaluate_task_on_multiple_variations
    self._evaluate_task_on_one_variation(
  File "/ssd/lyx/conda/envs/3d_diffuser_actor/lib/python3.8/site-packages/torch/utils/_contextlib.py", line 115, in decorate_context
    return func(*args, **kwargs)
  File "/data/wangyiwen/3d_diffuser_actor/utils/utils_with_rlbench.py", line 567, in _evaluate_task_on_one_variation
    obs, reward, terminate, _ = move(action, collision_checking=collision_checking)
  File "/data/wangyiwen/3d_diffuser_actor/utils/utils_with_rlbench.py", line 95, in __call__
    obs, reward, terminate = self._task.step(action_collision)
  File "/data/wangyiwen/3d_diffuser_actor/RLBench/rlbench/task_environment.py", line 99, in step
    self._action_mode.action(self._scene, action)
  File "/data/wangyiwen/3d_diffuser_actor/RLBench/rlbench/action_modes/action_mode.py", line 35, in action
    self.arm_action_mode.action(scene, arm_action, ignore_collisions)
  File "/data/wangyiwen/3d_diffuser_actor/RLBench/rlbench/action_modes/arm_action_modes.py", line 261, in action
    success, terminate = scene.task.success()
  File "/data/wangyiwen/3d_diffuser_actor/RLBench/rlbench/backend/task.py", line 298, in success
    [cond.condition_met()[0] for cond in self._success_conditions])
  File "/data/wangyiwen/3d_diffuser_actor/RLBench/rlbench/backend/task.py", line 298, in <listcomp>
    [cond.condition_met()[0] for cond in self._success_conditions])
  File "/data/wangyiwen/3d_diffuser_actor/RLBench/rlbench/backend/conditions.py", line 51, in condition_met
    met = self._detector.is_detected(self._obj)
  File "/data/wangyiwen/3d_diffuser_actor/PyRep/pyrep/objects/proximity_sensor.py", line 36, in is_detected
    state, point = sim.simCheckProximitySensor(
  File "/data/wangyiwen/3d_diffuser_actor/PyRep/pyrep/backend/sim.py", line 339, in simCheckProximitySensor
    _check_return(state)
  File "/data/wangyiwen/3d_diffuser_actor/PyRep/pyrep/backend/sim.py", line 27, in _check_return
    raise RuntimeError(
RuntimeError: The call failed on the V-REP side. Return value: -1
QMutex: destroying locked mutex
