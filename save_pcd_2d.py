#!/usr/bin/env python3

import os
import torch
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import itertools


def save_pcd_as_2d_image(pcd_tensor, save_path):
    if pcd_tensor is None:
        print(f"警告: 无点云数据可保存2D图像到 {save_path}")
        return

    try:
        xyz = pcd_tensor.cpu().numpy().copy()
        print(f"点云tensor形状: {pcd_tensor.shape}")

        # 根据实际形状处理数据
        if len(xyz.shape) == 3:  # (ch, h, w)
            xyz = xyz.transpose(1, 2, 0)  # (h, w, ch)
            xyz = xyz.reshape(-1, 3)  # (h*w, 3)
        elif len(xyz.shape) == 2:  # 已经是(N, 3)格式
            pass  # 不需要重塑
        else:
            print(f"错误: 不支持的点云形状: {xyz.shape}")
            return

        print(f"重塑后点云形状: {xyz.shape}")
    except Exception as e:
        print(f"错误: 处理点云数据时出错: {e}")
        print(f"点云tensor类型: {type(pcd_tensor)}")
        if hasattr(pcd_tensor, 'shape'):
            print(f"点云tensor形状: {pcd_tensor.shape}")
        return

    # xyz_min = xyz.min(axis=0)
    # xyz_max = xyz.max(axis=0)
    # norm_xyz = (xyz - xyz_min) / (xyz_max - xyz_min + 1e-8)
    #
    # H, W = pcd_tensor.shape[1], pcd_tensor.shape[2]
    # vis_img = norm_xyz.reshape(H, W, 3)
    # vis_img = (vis_img * 255).astype(np.uint8)
    #
    # os.makedirs(os.path.dirname(save_path), exist_ok=True)
    # plt.imsave(save_path, vis_img)
    # print(f"已保存点云2D图像: {save_path}")

    # 处理点云数据，将xyz坐标映射为RGB颜色
    if len(xyz.shape) == 2:  # (N, 3) 格式
        # 判断哪些是填充的[0,0,0]
        mask = ~(np.all(xyz == 0, axis=1))  # 只保留非0的有效点
        if np.any(mask):
            xyz_min = xyz[mask].min(axis=0)
            xyz_max = xyz[mask].max(axis=0)
        else:
            xyz_min = np.array([0.0, 0.0, 0.0])
            xyz_max = np.array([1.0, 1.0, 1.0])

        # 标准化
        norm_xyz = (xyz - xyz_min) / (xyz_max - xyz_min + 1e-8)
        norm_xyz = np.clip(norm_xyz, 0.0, 1.0)  # 防止负数或溢出

        # 需要重新reshape为图像格式
        # 假设原始是(ch, h, w)格式，那么reshape后应该是(h, w, 3)
        original_shape = pcd_tensor.shape
        if len(original_shape) == 3:
            h, w = original_shape[1], original_shape[2]
            vis_img = norm_xyz.reshape(h, w, 3)
        else:
            # 如果无法确定原始形状，使用平方根作为近似
            total_pixels = len(norm_xyz)
            side_length = int(np.sqrt(total_pixels))
            if side_length * side_length == total_pixels:
                vis_img = norm_xyz.reshape(side_length, side_length, 3)
            else:
                print(f"警告: 无法重塑为正方形图像，使用原始形状")
                return
    else:  # (H, W, 3) 格式
        # 判断哪些是填充的[0,0,0]
        mask = ~(np.all(xyz == 0, axis=2))  # 只保留非0的有效点
        if np.any(mask):
            xyz_min = xyz[mask].min(axis=0)
            xyz_max = xyz[mask].max(axis=0)
        else:
            xyz_min = np.array([0.0, 0.0, 0.0])
            xyz_max = np.array([1.0, 1.0, 1.0])

        # 全图标准化
        norm_xyz = (xyz - xyz_min) / (xyz_max - xyz_min + 1e-8)
        norm_xyz = np.clip(norm_xyz, 0.0, 1.0)  # 防止负数或溢出
        vis_img = norm_xyz

    # 转换为uint8
    vis_img = (vis_img * 255).astype(np.uint8)
    print("x range:", xyz_min[0], "to", xyz_max[0])
    print("Y range:", xyz_min[1], "to", xyz_max[1])
    print("z range:", xyz_min[2], "to", xyz_max[2])

    # =保存图像===
    try:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.imsave(save_path, vis_img)
        print(f"	可视化图像已保存到:{save_path}")
    except Exception as e:
        print(f"错误: 保存图像时出错: {e}")
        print(f"图像形状: {vis_img.shape}, 数据类型: {vis_img.dtype}")


def main():
    import sys
    project_root = Path(__file__).parent
    sys.path.append(str(project_root))

    from utils.utils_with_rlbench import RLBenchEnv, keypoint_discovery, transform
    import einops
    import pickle
    from rlbench.demo import Demo

    # 使用raw数据路径
    raw_data_path = "/data/wangyiwen/3d_diffuser_actor/data/peract/raw/train/"

    if not Path(raw_data_path).exists():
        print(f"Raw数据路径不存在: {raw_data_path}")
        return

    # 首先需要重排数据结构，或者直接使用RLBenchEnv.get_demo()方法
    # 让我们先尝试使用标准的RLBenchEnv方法

    # 初始化RLBenchEnv
    env = RLBenchEnv(
        data_path=raw_data_path,
        image_size=[256, 256],
        apply_rgb=True,
        apply_pc=True,
        apply_cameras=("front",),
    )

    # 指定要处理的任务
    tasks = ['phone_on_base']
    max_episodes = 3  # 最多处理3个episode

    # 创建输出目录
    os.makedirs("output", exist_ok=True)

    episode_count = 0
    for task_str in tasks:
        # 首先检查是否有variation结构的数据
        variation_dirs = list(Path(raw_data_path).glob(f"{task_str}/variation*"))

        if len(variation_dirs) == 0:
            print(f"没有找到variation结构的数据，需要先运行数据重排脚本")
            print(f"请运行: python data_preprocessing/rearrange_rlbench_demos.py --root_dir {raw_data_path}")

            # 作为临时解决方案，我们尝试直接处理all_variations结构
            episodes_dir = Path(raw_data_path) / task_str / "all_variations" / "episodes"
            if not episodes_dir.exists():
                print(f"Episode目录也不存在: {episodes_dir}")
                continue

            # 获取所有episode目录
            episode_dirs = list(episodes_dir.glob("episode*"))
            print(f"找到 {len(episode_dirs)} 个episodes in all_variations for {task_str}")

            for ep_dir in episode_dirs[:max_episodes]:
                episode_num = int(ep_dir.name[7:])
                print(f"处理 {task_str} episode {episode_num}")

                try:
                    # 读取variation_number
                    var_file = ep_dir / "variation_number.pkl"
                    if var_file.exists():
                        with open(var_file, 'rb') as f:
                            variation = pickle.load(f)
                        print(f"Episode {episode_num} 属于 variation {variation}")

                        # 尝试使用RLBenchEnv.get_demo方法
                        # 但这需要数据在variation{N}结构中
                        print("由于数据结构问题，无法使用标准方法加载")
                        continue
                    else:
                        print(f"找不到variation_number.pkl文件: {var_file}")
                        continue

                except Exception as e:
                    print(f"处理episode {episode_num}时出错: {e}")
                    continue

            continue

        # 如果有variation结构的数据，使用标准方法
        print(f"找到 {len(variation_dirs)} 个variations for {task_str}")

        for var_dir in variation_dirs:
            variation = int(var_dir.name.replace('variation', ''))
            episodes_dir = var_dir / "episodes"

            if not episodes_dir.exists():
                continue

            episode_files = list(episodes_dir.glob("episode*"))
            print(f"Variation {variation} 有 {len(episode_files)} 个episodes")

            for ep_file in episode_files[:max_episodes]:
                episode_num = int(ep_file.name[7:])
                print(f"处理 {task_str} variation {variation} episode {episode_num}")

                try:
                    # 使用标准的RLBenchEnv.get_demo方法
                    demos = env.get_demo(task_str, variation, episode_num)
                    if not demos:
                        print(f"无法获取demo: {task_str} variation {variation} episode {episode_num}")
                        continue

                    demo = demos[0]
                    print(f"成功加载demo，包含 {len(demo._observations)} 个观察")

                    # 获取关键帧
                    key_frames = keypoint_discovery(demo)
                    key_frames.insert(0, 0)  # 添加第一帧

                    print(f"关键帧索引: {key_frames}")

                    # 按照参考脚本的方式处理关键帧
                    keyframe_state_ls = []
                    keyframe_action_ls = []

                    for i in range(len(key_frames)):
                        state, action = env.get_obs_action(demo._observations[key_frames[i]])
                        state = transform(state)
                        keyframe_state_ls.append(state.unsqueeze(0))
                        keyframe_action_ls.append(action.unsqueeze(0))

                    # 按照参考脚本重新排列数据
                    state_ls = einops.rearrange(
                        keyframe_state_ls,
                        "t 1 (m n ch) h w -> t n m ch h w",
                        ch=3,
                        n=len(env.apply_cameras),
                        m=2,
                    )

                    print(f"处理后的state_ls形状: {state_ls.shape}")

                    # 处理每个关键帧
                    for frame_idx in range(len(key_frames)):
                        # 提取当前帧的数据
                        current_frame_data = state_ls[frame_idx]  # (n, m, ch, h, w)

                        # 提取点云数据 (第二个m维度是点云)
                        pcd = current_frame_data[0, 1]  # 第一个相机的点云数据 (ch, h, w)

                        print(f"帧 {frame_idx} 点云形状: {pcd.shape}")

                        # 保存点云2D图像
                        save_path = f"output/raw_pcd_2d_{task_str}_var{variation}_ep{episode_num}_frame{frame_idx:02d}.png"
                        save_pcd_as_2d_image(pcd, save_path)

                except Exception as e:
                    print(f"处理episode {episode_num}时出错: {e}")
                    import traceback
                    traceback.print_exc()

                episode_count += 1
                if episode_count >= max_episodes:
                    break

            if episode_count >= max_episodes:
                break


if __name__ == "__main__":
    main()