#!/usr/bin/env python3
"""
更新版真机测试脚本 - 整合3D Diffuser Actor模型与真实机器人
包含正确的数据格式转换，与训练时保持一致
"""

import os
import sys
import json
import time
import queue
import threading
import subprocess
import argparse
from pathlib import Path
from typing import Dict, Any, Optional, Tuple

import cv2
import numpy as np
import torch
import torch.nn.functional as F
from scipy.spatial.transform import Rotation

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from diffuser_actor import DiffuserActor


class KeyboardListener:
    """键盘监听器，用于强制打开夹爪"""
    def __init__(self, robot_controller):
        self.robot_controller = robot_controller
        self.running = True
        self.thread = threading.Thread(target=self._listen, daemon=True)

    def start(self):
        """启动键盘监听"""
        self.thread.start()
        print("键盘监听已启动 - 按 'o' 键强制打开夹爪，按 'q' 键退出")

    def stop(self):
        """停止键盘监听"""
        self.running = False

    def _listen(self):
        """监听键盘输入"""
        try:
            import termios
            import tty
            import select

            old_settings = termios.tcgetattr(sys.stdin)
            tty.setraw(sys.stdin.fileno())

            while self.running:
                if select.select([sys.stdin], [], [], 0.1)[0]:
                    key = sys.stdin.read(1)
                    if key.lower() == 'o':
                        print("\n检测到 'o' 键 - 强制打开夹爪!")
                        self.robot_controller.force_open_gripper()
                    elif key.lower() == 'q':
                        print("\n检测到 'q' 键 - 退出程序")
                        self.running = False
                        break

        except ImportError:
            print("警告: 无法导入termios，键盘监听功能不可用")
        except Exception as e:
            print(f"键盘监听错误: {e}")
        finally:
            try:
                termios.tcsetattr(sys.stdin, termios.TCSADRAIN, old_settings)
            except:
                pass


class VideoCapture:
    """线程安全的视频捕获类"""
    
    def __init__(self, cap, name=None):
        if name is None:
            name = cap.name
        self.name = name
        self.q = queue.Queue()
        self.cap = cap
        self.t = threading.Thread(target=self._reader)
        self.t.daemon = False
        self.enable = True
        self.t.start()

    def _reader(self):
        while self.enable:
            ret, frame = self.cap.read()
            if not ret:
                break
            if not self.q.empty():
                try:
                    self.q.get_nowait()  # 丢弃未处理的帧
                except queue.Empty:
                    pass
            self.q.put(frame)

    def read(self):
        return self.q.get(timeout=5)

    def close(self):
        self.enable = False
        self.t.join()
        self.cap.close()


class RSCapture:
    """RealSense相机捕获类"""
    
    def get_device_serial_numbers(self):
        import pyrealsense2 as rs
        devices = rs.context().devices
        return [d.get_info(rs.camera_info.serial_number) for d in devices]

    def __init__(self, name, serial_number, dim=(640, 480), fps=15, depth=True, exposure=40000):
        import pyrealsense2 as rs
        self.name = name
        assert serial_number in self.get_device_serial_numbers()
        self.serial_number = serial_number
        self.depth = depth
        self.pipe = rs.pipeline()
        self.cfg = rs.config()
        self.cfg.enable_device(self.serial_number)
        self.cfg.enable_stream(rs.stream.color, dim[0], dim[1], rs.format.bgr8, fps)
        if self.depth:
            self.cfg.enable_stream(rs.stream.depth, dim[0], dim[1], rs.format.z16, fps)
        self.profile = self.pipe.start(self.cfg)
        self.s = self.profile.get_device().query_sensors()[0]
        self.s.set_option(rs.option.exposure, exposure)

        # 创建对齐对象
        align_to = rs.stream.color
        self.align = rs.align(align_to)

    def read(self):
        import pyrealsense2 as rs
        frames = self.pipe.wait_for_frames()
        aligned_frames = self.align.process(frames)
        color_frame = aligned_frames.get_color_frame()
        if self.depth:
            depth_frame = aligned_frames.get_depth_frame()

        if color_frame.is_video_frame():
            image = np.asarray(color_frame.get_data())
            if self.depth and depth_frame.is_depth_frame():
                depth = np.expand_dims(np.asarray(depth_frame.get_data()), axis=2)
                return True, np.concatenate((image, depth), axis=-1)
            else:
                return True, image
        else:
            return False, None

    def close(self):
        self.pipe.stop()
        self.cfg.disable_all_streams()


class RobotController:
    """机器人控制器"""
    
    def __init__(self, url: str, action_scale: list, gripper_mode: str):
        self.url = url
        self.ACTION_SCALE = action_scale
        self.gripper_mode = gripper_mode
        
    def get_currpos(self) -> np.ndarray:
        """获取机器人当前状态"""
        curl_command = [
            "curl",
            "-X", "POST", 
            self.url + "getstate",
            "-H", "Content-Type: application/json",
            "-s"
        ]
        
        result = subprocess.run(curl_command, capture_output=True, text=True)
        
        if result.returncode != 0:
            raise Exception("Failed to get robot state. Curl command failed.")
        
        ps = json.loads(result.stdout)
        currpos = np.array(ps["pose"])
        return currpos

    def send_gripper_command(self, pos: float, force_open=False):
        """发送夹爪命令

        Args:
            pos: 夹爪位置指令 (-1 到 1)
            force_open: 如果为True，强制执行打开指令，忽略状态检查
        """
        if self.gripper_mode == "binary":
            if pos <= -0.5:  # 关闭夹爪
                curl_command = [
                    "curl",
                    "-X", "POST",
                    self.url + "close_gripper",
                    "-H", "Content-Type: application/json"
                ]
                subprocess.run(curl_command)
            elif pos >= 0.5 or force_open:  # 打开夹爪（包括强制打开）
                if force_open:
                    print("强制打开夹爪...")
                curl_command = [
                    "curl",
                    "-X", "POST",
                    self.url + "open_gripper",
                    "-H", "Content-Type: application/json"
                ]
                subprocess.run(curl_command)
            else:
                return
        elif self.gripper_mode == "continuous":
            raise NotImplementedError("Continuous gripper control is optional")

    def force_open_gripper(self):
        """强制打开夹爪，忽略所有状态检查"""
        print("执行强制打开夹爪...")
        # 首先尝试使用专门的强制打开接口
        try:
            curl_command = [
                "curl",
                "-X", "POST",
                self.url + "force_open_gripper",
                "-H", "Content-Type: application/json"
            ]
            result = subprocess.run(curl_command, capture_output=True, text=True)
            if result.returncode == 0:
                print("使用force_open_gripper接口成功")
                return
        except Exception as e:
            print(f"force_open_gripper接口失败: {e}")

        # 如果专门接口不可用，使用常规接口
        self.send_gripper_command(1.0, force_open=True)

    def send_pos_command(self, pos: np.ndarray):
        """发送位置命令"""
        arr = np.array(pos).astype(np.float32)
        data = {"arr": arr.tolist()}
        json_data = json.dumps(data)
        
        curl_command = [
            "curl",
            "-X", "POST", 
            self.url + "pose",
            "-H", "Content-Type: application/json",
            "-d", json_data
        ]
        
        subprocess.run(curl_command)

    def step(self, action: np.ndarray):
        """执行动作"""
        xyz_delta = action[:3]
        currpos = self.get_currpos()

        nextpos = currpos.copy()
        nextpos[:3] = nextpos[:3] + xyz_delta * self.ACTION_SCALE[0]

        # 从动作获取方向
        nextpos[3:] = (
            Rotation.from_euler("xyz", action[3:6] * self.ACTION_SCALE[1])
            * Rotation.from_quat(currpos[3:])
        ).as_quat()

        gripper_action = action[6] * self.ACTION_SCALE[2]

        self.send_gripper_command(gripper_action)
        self.send_pos_command(nextpos)


class DataPreprocessor:
    """数据预处理器 - 与训练时保持一致"""
    
    def __init__(self, target_shape=(128, 128)):
        self.target_shape = target_shape
        
        # 相机内参（与convert.py保持一致）
        self.intrinsics = {
            'fx': 647.855,
            'fy': 646.244,
            'cx': 635.578,
            'cy': 400.158
        }
        
        # 相机外参（从convert.py复制）
        self.T_world2cam = np.array([
            [2.03941332e-02, 9.99768953e-01, 6.79112293e-03, 2.84985196e-02],
            [3.39805311e-01, -5.43138745e-04, -9.40495643e-01, -1.24901921e-01],
            [-9.40274657e-01, 2.14882531e-02, -3.39737877e-01, 1.22303861e+00],
            [0.0, 0.0, 0.0, 1.0]
        ])
        self.T_cam2world = np.linalg.inv(self.T_world2cam)

    def depth_to_xyz(self, depth: np.ndarray) -> np.ndarray:
        """
        将深度图转换为世界坐标系下的点云
        与convert.py中的depth_to_xyz函数保持一致
        """
        K = np.array([
            [self.intrinsics['fx'], 0, self.intrinsics['cx']],
            [0, self.intrinsics['fy'], self.intrinsics['cy']],
            [0, 0, 1]
        ])
        
        H, W = depth.shape
        u, v = np.meshgrid(np.arange(W), np.arange(H), indexing='xy')
        
        z = depth
        x = (u - K[0, 2]) * z / K[0, 0]
        y = (v - K[1, 2]) * z / K[1, 1]
        pcd_camera = np.stack((x, y, z), axis=-1)  # (H, W, 3)

        # 齐次坐标
        ones = np.ones_like(z)[..., None]
        pcd_camera_homo = np.concatenate([pcd_camera, ones], axis=-1)  # (H, W, 4)

        # 相机坐标系 → 世界坐标系
        pcd_world = np.einsum('ij,hwj->hwi', self.T_cam2world, pcd_camera_homo)  # (H, W, 4)
        
        return pcd_world[..., :3]  # 去掉齐次维度

    def preprocess_observation(self, frame: np.ndarray) -> Dict[str, torch.Tensor]:
        """
        预处理观测数据，与训练时的格式保持一致
        参考convert.py中的convert_transitions_to_obs_tensors函数
        """
        # 分离RGB和深度
        rgb = frame[..., :3].astype(np.uint8)
        depth = frame[..., 3].astype(np.float32) / 1000.0  # 转换为米
        
        # 裁剪图像（与训练时保持一致）
        cropped_rgb = rgb[0:720, 200:1080]
        cropped_depth = depth[0:720, 200:1080]
        
        # 调整大小到目标尺寸
        resized_rgb = cv2.resize(cropped_rgb, self.target_shape)
        resized_depth = cv2.resize(cropped_depth, self.target_shape, interpolation=cv2.INTER_NEAREST)
        
        # RGB预处理：转换为[-1, 1]范围（与convert.py一致）
        rgb_tensor = torch.from_numpy(resized_rgb.transpose(2, 0, 1)).float() / 255.0  # (3, H, W)
        rgb_tensor = (rgb_tensor - 0.5) * 2  # [-1, 1]
        
        # 深度转点云
        xyz = self.depth_to_xyz(resized_depth)  # (H, W, 3)
        xyz_tensor = torch.from_numpy(xyz).float().permute(2, 0, 1)  # (3, H, W)
        
        # 检查NaN/Inf
        if torch.isnan(xyz_tensor).any() or torch.isinf(xyz_tensor).any():
            print("⚠️ Warning: xyz contains NaN or Inf")
            # 用零填充NaN/Inf值
            xyz_tensor = torch.nan_to_num(xyz_tensor, nan=0.0, posinf=0.0, neginf=0.0)
        
        # 组合成obs_tensor: shape (1, 2, 3, H, W)
        # 其中第0维是RGB，第1维是XYZ
        frame_tensor = torch.stack([rgb_tensor, xyz_tensor], dim=0).unsqueeze(0)  # (1, 2, 3, H, W)
        
        # 添加相机维度
        obs_tensor = frame_tensor.unsqueeze(1)  # (1, 1, 2, 3, H, W)
        
        return {
            "rgb": obs_tensor[:, :, 0].to(self.device),  # (1, 1, 3, H, W)
            "depth": obs_tensor[:, :, 1].to(self.device)  # (1, 1, 3, H, W)
        }

    def set_device(self, device):
        """设置计算设备"""
        self.device = device


class RealRobotTester:
    """真机测试器"""
    
    def __init__(self, args):
        self.args = args
        self.device = torch.device(args.device)
        
        # 初始化数据预处理器
        self.preprocessor = DataPreprocessor(target_shape=tuple(int(x) for x in args.image_size.split(",")))
        self.preprocessor.set_device(self.device)
        
        # 初始化模型
        self.model = self._load_model()
        self.model.eval()
        
        # 初始化相机
        self.camera = self._init_camera()
        
        # 初始化机器人控制器
        self.robot = RobotController(
            args.robot_url,
            [0.01, 0.06, 1.0],  # action_scale
            "binary"  # gripper_mode
        )
        
        print("真机测试器初始化完成")

    def _load_model(self) -> DiffuserActor:
        """加载预训练模型"""
        print(f"加载模型: {self.args.checkpoint}")
        
        # 加载位置边界
        with open(self.args.gripper_loc_bounds, 'r') as f:
            gripper_loc_bounds = json.load(f)
        
        # 创建模型
        model = DiffuserActor(
            backbone=self.args.backbone,
            image_size=tuple(int(x) for x in self.args.image_size.split(",")),
            embedding_dim=self.args.embedding_dim,
            num_vis_ins_attn_layers=self.args.num_vis_ins_attn_layers,
            use_instruction=bool(self.args.use_instruction),
            fps_subsampling_factor=self.args.fps_subsampling_factor,
            gripper_loc_bounds=gripper_loc_bounds,
            rotation_parametrization=self.args.rotation_parametrization,
            quaternion_format=self.args.quaternion_format,
            diffusion_timesteps=self.args.diffusion_timesteps,
            nhist=self.args.num_history,
            relative=bool(self.args.relative_action),
            lang_enhanced=bool(self.args.lang_enhanced)
        )
        
        # 加载权重
        checkpoint = torch.load(self.args.checkpoint, map_location="cpu")
        if "weight" in checkpoint:
            state_dict = checkpoint["weight"]
            # 移除模块前缀
            model_weights = {}
            for key in state_dict:
                if key.startswith("module."):
                    _key = key[7:]
                else:
                    _key = key
                model_weights[_key] = state_dict[key]
            model.load_state_dict(model_weights)
        else:
            model.load_state_dict(checkpoint)
        
        model = model.to(self.device)
        return model

    def _init_camera(self) -> VideoCapture:
        """初始化相机"""
        print(f"初始化RealSense相机: {self.args.camera_serial}")
        
        rs_cap = RSCapture(
            name="front",
            serial_number=self.args.camera_serial,
            dim=(1280, 720),
            exposure=40000
        )
        
        return VideoCapture(rs_cap)

    def _get_robot_state(self) -> torch.Tensor:
        """获取机器人状态"""
        currpos = self.robot.get_currpos()
        
        # 转换为张量格式
        gripper_state = torch.from_numpy(currpos).float()
        gripper_state = gripper_state.unsqueeze(0).unsqueeze(0)  # (1, 1, 7)
        
        return gripper_state.to(self.device)

    def _create_dummy_instruction(self) -> torch.Tensor:
        """创建虚拟指令（当不使用指令时）"""
        if self.args.use_instruction:
            raise NotImplementedError("指令编码需要实现")
        else:
            # 创建零张量作为虚拟指令
            return torch.zeros(1, 53, 512).to(self.device)

    def _predict_action(self, observation: Dict[str, torch.Tensor], 
                       robot_state: torch.Tensor) -> np.ndarray:
        """预测动作"""
        with torch.no_grad():
            # 创建轨迹掩码
            trajectory_mask = torch.full(
                [1, self.args.interpolation_length - 1], False
            ).to(self.device)
            
            # 创建虚拟轨迹
            fake_trajectory = torch.full(
                [1, self.args.interpolation_length - 1, 7], 0
            ).to(self.device)
            
            # 创建虚拟指令
            instruction = self._create_dummy_instruction()
            
            # 模型推理
            trajectory = self.model(
                fake_trajectory,
                trajectory_mask,
                observation["rgb"],
                observation["depth"],
                instruction,
                robot_state,
                run_inference=True
            )
            
            # 提取第一个动作
            action = trajectory[0, 0].cpu().numpy()
            return action

    def run_test(self):
        """运行测试"""
        print("开始真机测试...")
        print(f"最大步数: {self.args.max_steps}")
        print(f"动作延迟: {self.args.action_delay}")
        print("按 Ctrl+C 紧急停止")
        print("")
        
        try:
            step_count = 0
            max_steps = self.args.max_steps
            
            while step_count < max_steps:
                print(f"\n步骤 {step_count + 1}/{max_steps}")
                
                # 1. 获取观测
                print("获取相机观测...")
                ret, frame = self.camera.read()
                if not ret:
                    print("警告：无法获取相机帧")
                    time.sleep(0.1)
                    continue
                
                # 2. 预处理观测（使用与训练时一致的格式）
                print("预处理观测数据...")
                observation = self.preprocessor.preprocess_observation(frame)
                
                # 3. 获取机器人状态
                print("获取机器人状态...")
                robot_state = self._get_robot_state()
                
                # 4. 预测动作
                print("模型推理...")
                action = self._predict_action(observation, robot_state)
                
                print(f"预测动作: {action}")
                
                # 5. 执行动作
                print("执行动作...")
                self.robot.step(action)
                
                # 6. 等待动作完成
                time.sleep(self.args.action_delay)
                
                step_count += 1
                
                # 检查是否完成任务（这里需要根据具体任务实现）
                if self._check_task_completion():
                    print("任务完成！")
                    break
                    
        except KeyboardInterrupt:
            print("\n测试被用户中断")
        except Exception as e:
            print(f"测试过程中出现错误: {e}")
            raise
        finally:
            self.cleanup()

    def _check_task_completion(self) -> bool:
        """检查任务是否完成（需要根据具体任务实现）"""
        # 这里需要根据具体任务实现完成条件检查
        return False

    def cleanup(self):
        """清理资源"""
        print("清理资源...")
        if hasattr(self, 'camera'):
            self.camera.close()


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="更新版真机测试脚本")
    
    # 模型参数
    parser.add_argument('--checkpoint', type=str, required=True,
                      help='模型检查点路径')
    parser.add_argument('--gripper_loc_bounds', type=str, required=True,
                      help='夹爪位置边界文件路径')
    parser.add_argument('--backbone', type=str, default='clip',
                      help='骨干网络')
    parser.add_argument('--image_size', type=str, default='128,128',
                      help='图像尺寸')
    parser.add_argument('--embedding_dim', type=int, default=120,
                      help='嵌入维度')
    parser.add_argument('--num_vis_ins_attn_layers', type=int, default=2,
                      help='视觉指令注意力层数')
    parser.add_argument('--use_instruction', type=int, default=0,
                      help='是否使用指令')
    parser.add_argument('--fps_subsampling_factor', type=int, default=5,
                      help='FPS子采样因子')
    parser.add_argument('--rotation_parametrization', type=str, default='6D',
                      help='旋转参数化')
    parser.add_argument('--quaternion_format', type=str, default='xyzw',
                      help='四元数格式')
    parser.add_argument('--diffusion_timesteps', type=int, default=100,
                      help='扩散时间步数')
    parser.add_argument('--num_history', type=int, default=3,
                      help='历史步数')
    parser.add_argument('--relative_action', type=int, default=0,
                      help='相对动作')
    parser.add_argument('--lang_enhanced', type=int, default=0,
                      help='语言增强')
    parser.add_argument('--interpolation_length', type=int, default=2,
                      help='插值长度')
    
    # 硬件参数
    parser.add_argument('--device', type=str, default='cuda',
                      help='计算设备')
    parser.add_argument('--camera_serial', type=str, required=True,
                      help='RealSense相机序列号')
    parser.add_argument('--robot_url', type=str, required=True,
                      help='机器人API URL')
    
    # 测试参数
    parser.add_argument('--max_steps', type=int, default=100,
                      help='最大测试步数')
    parser.add_argument('--action_delay', type=float, default=1.0,
                      help='动作执行延迟')
    
    return parser.parse_args()


def main():
    """主函数"""
    args = parse_arguments()

    # 创建测试器
    tester = RealRobotTester(args)

    # 启动键盘监听
    keyboard_listener = KeyboardListener(tester.robot_controller)
    keyboard_listener.start()

    try:
        # 运行测试
        tester.run_test()
    finally:
        # 停止键盘监听
        keyboard_listener.stop()


if __name__ == "__main__":
    main() 