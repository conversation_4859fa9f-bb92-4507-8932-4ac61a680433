"""Main script for trajectory optimization."""

import io
import os
from pathlib import Path
import random
from typing import Tuple, Optional
import argparse
import json

import cv2
from matplotlib import pyplot as plt
import numpy as np
import torch
import torch.distributed as dist
from torch.nn import functional as F
import open3d as o3d

from datasets.dataset_engine import RLBenchDataset
from engine import BaseTrainTester
from diffuser_actor import DiffuserActor

from utils.common_utils import (
    load_instructions, count_parameters, get_gripper_loc_bounds
)


def save_pcd_ply(pcd_tensor, save_path, max_points=100000):
    """
    Save point cloud tensor as PLY file with XYZ coordinate mapping to RGB colors.
    Based on the function from save_pointclouds_as_ply.py
    """
    if pcd_tensor is None:
        print(f"Warning: No point cloud data to save PLY to {save_path}")
        return

    # Normalize shape to (3, H, W)
    if len(pcd_tensor.shape) == 5:
        pcd_tensor = pcd_tensor[0, 0]
    elif len(pcd_tensor.shape) == 4:
        pcd_tensor = pcd_tensor[0]

    xyz = pcd_tensor.cpu().numpy().reshape(3, -1).T  # (N,3)

    # Filter invalid points
    valid = ~np.all(np.isclose(xyz, 0), axis=1)
    xyz = xyz[valid]

    if xyz.shape[0] == 0:
        print(f"Warning: No valid points found for {save_path}")
        return

    # Subsample if too many points
    if xyz.shape[0] > max_points:
        indices = np.random.choice(xyz.shape[0], max_points, replace=False)
        xyz = xyz[indices]

    # Map XYZ coordinates to RGB colors
    # Normalize XYZ to [0,1] range
    xyz_min = xyz.min(axis=0, keepdims=True)
    xyz_max = xyz.max(axis=0, keepdims=True)
    xyz_range = xyz_max - xyz_min
    xyz_range[xyz_range == 0] = 1  # Avoid division by zero
    xyz_normalized = (xyz - xyz_min) / xyz_range

    # Map XYZ to RGB
    colors = xyz_normalized  # X->R, Y->G, Z->B

    # Create Open3D point cloud
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(xyz.astype(np.float32))
    pcd.colors = o3d.utility.Vector3dVector(colors.astype(np.float32))

    # Ensure directory exists
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    o3d.io.write_point_cloud(save_path, pcd)
    print(f"Saved PLY point cloud (XYZ→RGB coloring): {save_path}")


def save_args_to_json(args, filepath):
    """保存参数到JSON文件"""
    # 创建一个可序列化的参数字典
    args_dict = {}
    for key, value in vars(args).items():
        if isinstance(value, Path):
            args_dict[key] = str(value)
        elif isinstance(value, tuple):
            args_dict[key] = list(value)
        elif isinstance(value, np.ndarray):
            args_dict[key] = value.tolist()  # 将numpy数组转换为列表
        elif hasattr(value, '__call__'):  # 跳过函数/方法
            continue
        else:
            try:
                # 尝试序列化，如果失败则转换为字符串
                json.dumps(value)
                args_dict[key] = value
            except (TypeError, ValueError):
                args_dict[key] = str(value)

    # 保存到JSON文件
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(args_dict, f, indent=2, ensure_ascii=False)


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="Main script for trajectory optimization.")
    
    # Camera and basic settings
    parser.add_argument('--cameras', nargs='*', default=["wrist", "left_shoulder", "right_shoulder"],
                      help='Camera names to use')
    parser.add_argument('--image_size', type=str, default="256,256",
                      help='Image size as "width,height"')
    parser.add_argument('--max_episodes_per_task', type=int, default=100,
                      help='Maximum episodes per task')
    parser.add_argument('--instructions', type=str, default="instructions.pkl",
                      help='Path to instructions file')
    parser.add_argument('--seed', type=int, default=0,
                      help='Random seed')
    parser.add_argument('--tasks', nargs='+', required=True,
                      help='Task names (required)')
    parser.add_argument('--variations', nargs='*', type=int, default=[0],
                      help='Task variations')

    # Checkpoint and training settings
    parser.add_argument('--checkpoint', type=str, default=None,
                      help='Path to checkpoint file')
    parser.add_argument('--save_freq', type=int, default=500,
                      help='Checkpoint save frequency (steps)')
    
    parser.add_argument('--accumulate_grad_batches', type=int, default=1,
                      help='Number of gradient accumulation batches')
    parser.add_argument('--val_freq', type=int, default=500,
                      help='Validation frequency')
    parser.add_argument('--gripper_loc_bounds', type=str, default=None,
                      help='Gripper location bounds')
    parser.add_argument('--gripper_loc_bounds_buffer', type=float, default=0.04,
                      help='Gripper location bounds buffer')
    parser.add_argument('--eval_only', type=int, default=0,
                      help='Evaluation only mode')

    # Training and validation datasets
    parser.add_argument('--dataset', type=str, required=True,
                      help='Training dataset path (required)')
    parser.add_argument('--valset', type=str, required=True,
                      help='Validation dataset path (required)')
    parser.add_argument('--dense_interpolation', type=int, default=0,
                      help='Dense interpolation flag')
    parser.add_argument('--interpolation_length', type=int, default=100,
                      help='Interpolation length')

    # Logging settings
    parser.add_argument('--base_log_dir', type=str, 
                      default=str(Path(__file__).parent / "train_logs"),
                      help='Base logging directory')
    parser.add_argument('--exp_log_dir', type=str, default="exp",
                      help='Experiment log directory')
    parser.add_argument('--run_log_dir', type=str, default="run",
                      help='Run log directory')

    # Main training parameters
    parser.add_argument('--num_workers', type=int, default=1,
                      help='Number of data loading workers')
    parser.add_argument('--batch_size', type=int, default=16,
                      help='Training batch size')
    parser.add_argument('--batch_size_val', type=int, default=4,
                      help='Validation batch size')
    parser.add_argument('--cache_size', type=int, default=100,
                      help='Training cache size')
    parser.add_argument('--cache_size_val', type=int, default=100,
                      help='Validation cache size')
    parser.add_argument('--lr', type=float, default=1e-4,
                      help='Learning rate')
    parser.add_argument('--wd', type=float, default=5e-3,
                      help='Weight decay (used only for CALVIN)')
    parser.add_argument('--train_iters', type=int, default=200_000,
                      help='Number of training iterations')
    parser.add_argument('--val_iters', type=int, default=-1,
                      help='Number of validation iterations (-1 means heuristically-defined)')
    parser.add_argument('--max_episode_length', type=int, default=5,
                      help='Maximum episode length (-1 for no limit)')

    # Data augmentations
    parser.add_argument('--image_rescale', type=str, default="1.0,1.0",
                      help='Image rescale range as "min,max" (1.0,1.0 for no rescaling)')

    # Model parameters
    parser.add_argument('--backbone', type=str, default="clip", choices=["resnet", "clip"],
                      help='Backbone architecture')
    parser.add_argument('--embedding_dim', type=int, default=120,
                      help='Embedding dimension')
    parser.add_argument('--num_vis_ins_attn_layers', type=int, default=2,
                      help='Number of visual instruction attention layers')
    parser.add_argument('--use_instruction', type=int, default=0,
                      help='Use instruction flag')
    parser.add_argument('--rotation_parametrization', type=str, default='quat',
                      help='Rotation parametrization')
    parser.add_argument('--quaternion_format', type=str, default='wxyz',
                      help='Quaternion format')
    parser.add_argument('--diffusion_timesteps', type=int, default=100,
                      help='Number of diffusion timesteps')
    parser.add_argument('--keypose_only', type=int, default=0,
                      help='Keypose only flag')
    parser.add_argument('--num_history', type=int, default=0,
                      help='Number of history steps')
    parser.add_argument('--relative_action', type=int, default=0,
                      help='Relative action flag')
    parser.add_argument('--lang_enhanced', type=int, default=0,
                      help='Language enhanced flag')
    parser.add_argument('--fps_subsampling_factor', type=int, default=5,
                      help='FPS subsampling factor')

    args = parser.parse_args()
    
    # Convert string paths to Path objects
    if args.instructions:
        args.instructions = Path(args.instructions)
    if args.checkpoint:
        args.checkpoint = Path(args.checkpoint)
    args.dataset = Path(args.dataset)
    args.valset = Path(args.valset)
    args.base_log_dir = Path(args.base_log_dir)
    
    # Convert cameras list to tuple
    args.cameras = tuple(args.cameras)
    
    # Convert variations list to tuple
    args.variations = tuple(args.variations)
    
    # Convert tasks list to tuple
    args.tasks = tuple(args.tasks)
    
    # Add save method to args object
    args.save = lambda filepath: save_args_to_json(args, filepath)
    
    return args


class TrainTester(BaseTrainTester):
    """Train/test a trajectory optimization algorithm."""

    def __init__(self, args):
        """Initialize."""
        super().__init__(args)

    def get_datasets(self):
        """Initialize datasets."""
        # Load instruction, based on which we load tasks/variations

        instruction = load_instructions(
            self.args.instructions,
            tasks=self.args.tasks,
            variations=self.args.variations
        )
# 遍历 dataset 中的所有 task+var 文件夹
        if instruction is not None and self.args.use_instruction==1:
            '''taskvar = [
                (task, var)
                for task, var_instr in instruction.items()
                for var in var_instr.keys()
                
            ]'''
            taskvar = []
            valid_instructions = {}

            for task, var_instr in instruction.items():
                valid_var_instr = {}
                for var, instr in var_instr.items():
                    folder_name = f"{task}+{var}"
                    folder_path = self.args.dataset / folder_name
                    if folder_path.exists():
                        taskvar.append((task, var))
                        valid_var_instr[var] = instr
                    else:
                        print(f"Skipping missing folder: {folder_path}")
                if valid_var_instr:
                    valid_instructions[task] = valid_var_instr

            instruction = valid_instructions
        else:
            from collections import defaultdict
            instruction = defaultdict(dict)
            taskvar = []
            for folder_path in self.args.dataset.iterdir():
                if folder_path.is_dir():
                    name_parts = folder_path.name.split('+')
                    if len(name_parts) == 2:
                        task, var = name_parts
                        var = int(var)
                        taskvar.append((task, var))
                        instruction[task][var] = "dummy" # 无实际用处，只保留结构
            
            instruction=None
        # Initialize datasets with arguments
        train_dataset = RLBenchDataset(
            root=self.args.dataset,
            instructions=None,
            taskvar=taskvar,
            max_episode_length=self.args.max_episode_length,
            cache_size=self.args.cache_size,
            max_episodes_per_task=self.args.max_episodes_per_task,
            num_iters=self.args.train_iters,
            cameras=self.args.cameras,
            training=True,
            image_rescale=tuple(
                float(x) for x in self.args.image_rescale.split(",")
            ),
            return_low_lvl_trajectory=True,
            dense_interpolation=bool(self.args.dense_interpolation),
            interpolation_length=self.args.interpolation_length
        )
        test_dataset = RLBenchDataset(
            root=self.args.valset,
            instructions=None,
            taskvar=taskvar,
            max_episode_length=self.args.max_episode_length,
            cache_size=self.args.cache_size_val,
            max_episodes_per_task=self.args.max_episodes_per_task,
            cameras=self.args.cameras,
            training=False,
            image_rescale=tuple(
                float(x) for x in self.args.image_rescale.split(",")
            ),
            return_low_lvl_trajectory=True,
            dense_interpolation=bool(self.args.dense_interpolation),
            interpolation_length=self.args.interpolation_length
        )

        return train_dataset, test_dataset

    def get_model(self):
        """Initialize the model."""
        # Initialize model with arguments
        _model = DiffuserActor(
            backbone=self.args.backbone,
            image_size=tuple(int(x) for x in self.args.image_size.split(",")),
            embedding_dim=self.args.embedding_dim,
            num_vis_ins_attn_layers=self.args.num_vis_ins_attn_layers,
            use_instruction=bool(self.args.use_instruction),
            fps_subsampling_factor=self.args.fps_subsampling_factor,
            gripper_loc_bounds=self.args.gripper_loc_bounds,
            rotation_parametrization=self.args.rotation_parametrization,
            quaternion_format=self.args.quaternion_format,
            diffusion_timesteps=self.args.diffusion_timesteps,
            nhist=self.args.num_history,
            relative=bool(self.args.relative_action),
            lang_enhanced=bool(self.args.lang_enhanced)
        )
        print("Model parameters:", count_parameters(_model))

        return _model

    @staticmethod
    def get_criterion():
        return TrajectoryCriterion()

    def train_one_step(self, model, criterion, optimizer, step_id, sample):
        """Run a single training step."""
        if step_id % self.args.accumulate_grad_batches == 0:
            optimizer.zero_grad()

        if self.args.keypose_only:
            sample["trajectory"] = sample["trajectory"][:, [-1]]
            sample["trajectory_mask"] = sample["trajectory_mask"][:, [-1]]
        else:
            sample["trajectory"] = sample["trajectory"][:, 1:]
            sample["trajectory_mask"] = sample["trajectory_mask"][:, 1:]

        # Forward pass

        # curr_gripper = (
        #     sample["curr_gripper"] if self.args.num_history < 1
        #     else sample["curr_gripper_history"][:, -self.args.num_history:]
        # )
        curr_gripper = sample["trajectory_history"] #

        # 保存点云用于调试
        # print("Saving point clouds for debugging...")
        # pcds = sample["pcds"]
        # print(f"Point cloud tensor shape: {pcds.shape}")
        # print(f"Point cloud tensor dtype: {pcds.dtype}")
        # print(f"Point cloud tensor min: {pcds.min()}, max: {pcds.max()}")
        #
        # # 创建保存目录
        # save_dir = "debug_pointclouds"
        # os.makedirs(save_dir, exist_ok=True)
        #
        # # 将点云tensor保存为PLY文件
        # if pcds.dim() == 5:  # [batch, sequence, channels, height, width]
        #     batch_size, seq_len, channels, height, width = pcds.shape
        #     for b in range(batch_size):
        #         for s in range(seq_len):
        #             pcd_tensor = pcds[b, s]  # [C, H, W]
        #
        #             # 保存点云
        #             filename = f"pcd_batch{b}_seq{s}.ply"
        #             filepath = os.path.join(save_dir, filename)
        #             save_pcd_ply(pcd_tensor, filepath)
        #
        # print(f"Point clouds saved to {save_dir}/")
        # print("Terminating program after saving point clouds for debugging.")
        # exit(0)

        out = model(
            sample["trajectory"],
            sample["trajectory_mask"],
            sample["rgbs"],
            sample["pcds"],
            sample["instr"],
            curr_gripper
        )

        # Backward pass
        loss = criterion.compute_loss(out)
        loss.backward()

        # Update
        if step_id % self.args.accumulate_grad_batches == self.args.accumulate_grad_batches - 1:
            optimizer.step()

        # Log
        if dist.get_rank() == 0 and (step_id + 1) % self.args.val_freq == 0:
            self.writer.add_scalar("lr", self.args.lr, step_id)
            self.writer.add_scalar("train-loss/noise_mse", loss, step_id)

    @torch.no_grad()
    def evaluate_nsteps(self, model, criterion, loader, step_id, val_iters,
                        split='val'):
        """Run a given number of evaluation steps."""
        if self.args.val_iters != -1:
            val_iters = self.args.val_iters
        values = {}
        device = next(model.parameters()).device
        model.eval()

        for i, sample in enumerate(loader):
            if i == val_iters:
                break

            if self.args.keypose_only:
                sample["trajectory"] = sample["trajectory"][:, [-1]]
                sample["trajectory_mask"] = sample["trajectory_mask"][:, [-1]]
            else:
                sample["trajectory"] = sample["trajectory"][:, 1:]
                sample["trajectory_mask"] = sample["trajectory_mask"][:, 1:]

            curr_gripper = sample["trajectory_history"]  #

            '''
            if torch.isnan(sample["trajectory"]).any():
                print("NaN detected in trajectory")
            print("tra",sample["trajectory"])
            if torch.isnan(sample["trajectory_mask"]).any():
                print("NaN detected in trajectory_mask")
            print("tramask",sample["trajectory_mask"])
            if torch.isnan(sample["rgbs"]).any():
                print("NaN detected in rgbs")
            print("rgbs",sample["rgbs"])
            if torch.isnan(sample["pcds"]).any():
                print("NaN detected in pcds")
            print("pcds",sample["pcds"])
            if torch.isnan(sample["instr"]).any():
                print("NaN detected in instr")
            if torch.isnan(curr_gripper).any():
                print("NaN detected in curr_gripper")
            '''
            action = model(
                sample["trajectory"].to(device),
                sample["trajectory_mask"].to(device),
                sample["rgbs"].to(device),
                sample["pcds"].to(device),
                sample["instr"].to(device),
                curr_gripper.to(device),
                run_inference=True
            )
            if action is None:
                print("action is None")
            elif torch.isnan(action).any():
                print("NaN detected in action")
                #print("action",action)
            elif action.numel() == 0:
                print("action is empty")
            else:
                print("action is valid")
            losses, losses_B = criterion.compute_metrics(
                action,
                sample["trajectory"].to(device),
                sample["trajectory_mask"].to(device)
            )

            # Gather global statistics
            for n, l in losses.items():
                key = f"{split}-losses/mean/{n}"
                if key not in values:
                    values[key] = torch.Tensor([]).to(device)
                values[key] = torch.cat([values[key], l.unsqueeze(0)])

            # Gather per-task statistics
            tasks = np.array(sample["task"])
            for n, l in losses_B.items():
                for task in np.unique(tasks):
                    key = f"{split}-loss/{task}/{n}"
                    l_task = l[tasks == task].mean()
                    if key not in values:
                        values[key] = torch.Tensor([]).to(device)
                    values[key] = torch.cat([values[key], l_task.unsqueeze(0)])

            # Generate visualizations
            if i == 0 and dist.get_rank() == 0 and step_id > -1:
                viz_key = f'{split}-viz/viz'
                viz = generate_visualizations(
                    action,
                    sample["trajectory"].to(device),
                    sample["trajectory_mask"].to(device)
                )
                self.writer.add_image(viz_key, viz, step_id)

        # Log all statistics
        values = self.synchronize_between_processes(values)
        values = {k: v.mean().item() for k, v in values.items()}
        if dist.get_rank() == 0:
            if step_id > -1:
                for key, val in values.items():
                    self.writer.add_scalar(key, val, step_id)

            # Also log to terminal
            print(f"Step {step_id}:")
            for key, value in values.items():
                print(f"{key}: {value:.03f}")

        return values.get('val-losses/traj_pos_acc_001', None)


def traj_collate_fn(batch):
    keys = [
        "trajectory", "trajectory_mask", "trajectory_history",
        "rgbs", "pcds",
        "curr_gripper", "curr_gripper_history", "action", "instr"
    ]
    ret_dict = {
        key: torch.cat([
            item[key].float() if key != 'trajectory_mask' else item[key]
            for item in batch
        ]) for key in keys if key in batch[0]
    }

    ret_dict["task"] = []
    for item in batch:
        ret_dict["task"] += item['task']
    return ret_dict




class TrajectoryCriterion:

    def __init__(self):
        pass

    def compute_loss(self, pred, gt=None, mask=None, is_loss=True):
        if not is_loss:
            assert gt is not None and mask is not None
            return self.compute_metrics(pred, gt, mask)[0]['action_mse']
        return pred

    @staticmethod
    def compute_metrics(pred, gt, mask):
        # pred/gt are (B, L, 7), mask (B, L)
        #print(pred)
        pos_l2 = ((pred[..., :3] - gt[..., :3]) ** 2).sum(-1).sqrt()
        '''
        if torch.isnan(pos_l2).any():
            print("NaN detected in pos_l2:")
            print(f"pred[..., :3]: {pred[..., :3]}")
            print(f"gt[..., :3]: {gt[..., :3]}")
            print(f"pos_l2: {pos_l2}")
        '''
        # symmetric quaternion eval
        quat_l1 = (pred[..., 3:7] - gt[..., 3:7]).abs().sum(-1)
        '''
        if torch.isnan(quat_l1).any():
            print("NaN detected in quat_l1:")
            print(f"pred[..., 3:7]: {pred[..., 3:7]}")
            print(f"gt[..., 3:7]: {gt[..., 3:7]}")
            print(f"quat_l1: {quat_l1}")
        '''
        quat_l1_ = (pred[..., 3:7] + gt[..., 3:7]).abs().sum(-1)
        '''
        if torch.isnan(quat_l1_).any():
            print("NaN detected in quat_l1_:")
            print(f"pred[..., 3:7]: {pred[..., 3:7]}")
            print(f"gt[..., 3:7]: {gt[..., 3:7]}")
            print(f"quat_l1_: {quat_l1_}")
        '''
        select_mask = (quat_l1 < quat_l1_).float()
        quat_l1 = (select_mask * quat_l1 + (1 - select_mask) * quat_l1_)
        '''
        if torch.isnan(quat_l1).any():
            print("NaN detected in quat_l1 after select_mask update:")
            print(f"select_mask: {select_mask}")
            print(f"quat_l1: {quat_l1}")
        '''
        # gripper openess
        openess = ((pred[..., 7:] >= 0.5) == (gt[..., 7:] > 0.0)).bool()
        '''
        if torch.isnan(openess.float()).any():
            print("NaN detected in openess:")
            print(f"pred[..., 7:]: {pred[..., 7:]}")
            print(f"gt[..., 7:]: {gt[..., 7:]}")
            print(f"openess: {openess}")
        '''
        tr = 'traj_'

        # Trajectory metrics
        ret_1, ret_2 = {
            tr + 'action_mse': F.mse_loss(pred, gt),
            tr + 'pos_l2': pos_l2.mean(),
            tr + 'pos_acc_001': (pos_l2 < 0.01).float().mean(),
            tr + 'rot_l1': quat_l1.mean(),
            tr + 'rot_acc_0025': (quat_l1 < 0.025).float().mean(),
            tr + 'gripper': openess.flatten().float().mean()
        }, {
            tr + 'pos_l2': pos_l2.mean(-1),
            tr + 'pos_acc_001': (pos_l2 < 0.01).float().mean(-1),
            tr + 'rot_l1': quat_l1.mean(-1),
            tr + 'rot_acc_0025': (quat_l1 < 0.025).float().mean(-1)
        }

        # Keypose metrics
        pos_l2 = ((pred[:, -1, :3] - gt[:, -1, :3]) ** 2).sum(-1).sqrt()
        '''
        if torch.isnan(pos_l2).any():
            print("NaN detected in pos_l2 (keypose):")
            print(f"pred[:, -1, :3]: {pred[:, -1, :3]}")
            print(f"gt[:, -1, :3]: {gt[:, -1, :3]}")
            print(f"pos_l2: {pos_l2}")
        '''
        quat_l1 = (pred[:, -1, 3:7] - gt[:, -1, 3:7]).abs().sum(-1)
        '''
        if torch.isnan(quat_l1).any():
            print("NaN detected in quat_l1 (keypose):")
            print(f"pred[:, -1, 3:7]: {pred[:, -1, 3:7]}")
            print(f"gt[:, -1, 3:7]: {gt[:, -1, 3:7]}")
            print(f"quat_l1: {quat_l1}")
        '''
        quat_l1_ = (pred[:, -1, 3:7] + gt[:, -1, 3:7]).abs().sum(-1)
        '''
        if torch.isnan(quat_l1_).any():
            print("NaN detected in quat_l1_ (keypose):")
            print(f"pred[:, -1, 3:7]: {pred[:, -1, 3:7]}")
            print(f"gt[:, -1, 3:7]: {gt[:, -1, 3:7]}")
            print(f"quat_l1_: {quat_l1_}")
        '''
        select_mask = (quat_l1 < quat_l1_).float()
        quat_l1 = (select_mask * quat_l1 + (1 - select_mask) * quat_l1_)
        '''
        if torch.isnan(quat_l1).any():
            print("NaN detected in quat_l1 after select_mask update (keypose):")
            print(f"select_mask: {select_mask}")
            print(f"quat_l1: {quat_l1}")
        '''
        ret_1.update({
            'pos_l2_final': pos_l2.mean(),
            'pos_l2_final<0.01': (pos_l2 < 0.01).float().mean(),
            'rot_l1': quat_l1.mean(),
            'rot_l1<0025': (quat_l1 < 0.025).float().mean()
        })
        ret_2.update({
            'pos_l2_final': pos_l2,
            'pos_l2_final<0.01': (pos_l2 < 0.01).float(),
            'rot_l1': quat_l1,
            'rot_l1<0.025': (quat_l1 < 0.025).float(),
        })

        return ret_1, ret_2


def fig_to_numpy(fig, dpi=60):
    buf = io.BytesIO()
    fig.savefig(buf, format="png", dpi=dpi)
    buf.seek(0)
    img_arr = np.frombuffer(buf.getvalue(), dtype=np.uint8)
    buf.close()
    img = cv2.imdecode(img_arr, 1)
    return img


def generate_visualizations(pred, gt, mask, box_size=0.3):
    batch_idx = 0
    pred = pred[batch_idx].detach().cpu().numpy()
    gt = gt[batch_idx].detach().cpu().numpy()
    mask = mask[batch_idx].detach().cpu().numpy()

    fig = plt.figure(figsize=(10, 10))
    ax = plt.axes(projection='3d')
    ax.scatter3D(
        pred[~mask][:, 0], pred[~mask][:, 1], pred[~mask][:, 2],
        color='red', label='pred'
    )
    ax.scatter3D(
        gt[~mask][:, 0], gt[~mask][:, 1], gt[~mask][:, 2],
        color='blue', label='gt'
    )

    center = gt[~mask].mean(0)
    ax.set_xlim(center[0] - box_size, center[0] + box_size)
    ax.set_ylim(center[1] - box_size, center[1] + box_size)
    ax.set_zlim(center[2] - box_size, center[2] + box_size)
    ax.set_xticklabels([])
    ax.set_yticklabels([])
    ax.set_zticklabels([])
    plt.legend()
    fig.subplots_adjust(left=0, right=1, bottom=0, top=1)

    img = fig_to_numpy(fig, dpi=120)
    plt.close()
    return img.transpose(2, 0, 1)


if __name__ == '__main__':
    os.environ["TOKENIZERS_PARALLELISM"] = "false"
    # Arguments
    args = parse_arguments()
    print("Arguments:")
    print(args)
    print("-" * 100)
    if args.gripper_loc_bounds is None:
        args.gripper_loc_bounds = np.array([[-2, -2, -2], [2, 2, 2]]) * 1.0
    else:
        args.gripper_loc_bounds = get_gripper_loc_bounds(
            args.gripper_loc_bounds,
            task=args.tasks[0] if len(args.tasks) == 1 else None,
            buffer=args.gripper_loc_bounds_buffer,
        )
    log_dir = args.base_log_dir / args.exp_log_dir / args.run_log_dir
    args.log_dir = log_dir
    log_dir.mkdir(exist_ok=True, parents=True)
    print("Logging:", log_dir)
    print(
        "Available devices (CUDA_VISIBLE_DEVICES):",
        os.environ.get("CUDA_VISIBLE_DEVICES")
    )
    print("Device count", torch.cuda.device_count())
    args.local_rank = int(os.environ["LOCAL_RANK"])

    # Seeds
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)
    random.seed(args.seed)

    # DDP initialization
    torch.cuda.set_device(args.local_rank)
    torch.distributed.init_process_group(backend='nccl', init_method='env://')
    torch.backends.cudnn.enabled = True
    torch.backends.cudnn.benchmark = True
    torch.backends.cudnn.deterministic = True

    # Run
    train_tester = TrainTester(args)
    train_tester.main(collate_fn=traj_collate_fn)
