import pickle
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import os
import argparse
import torch

def depth_to_xyz(depth, intrinsics=None, auto_scale_intrinsics=True, auto_scale_depth=True):
    """
    Convert depth image to XYZ coordinates.

    Args:
        depth: (H, W) depth array
        intrinsics: Camera intrinsics matrix
        auto_scale_intrinsics: Whether to auto-scale intrinsics
        auto_scale_depth: Whether to auto-scale depth values

    Returns:
        xyz: (H, W, 3) XYZ coordinates
    """
    H, W = depth.shape

    # Default intrinsics if not provided
    if intrinsics is None:
        fx = fy = 128.0  # Default focal length
        cx, cy = W / 2, H / 2  # Image center
        if auto_scale_intrinsics:
            fx = fy = min(W, H) / 2
    else:
        fx, fy = intrinsics[0, 0], intrinsics[1, 1]
        cx, cy = intrinsics[0, 2], intrinsics[1, 2]

    # Auto-scale depth if needed
    if auto_scale_depth:
        depth = depth / 1000.0  # Convert mm to meters if needed

    # Create coordinate grids
    u, v = np.meshgrid(np.arange(W), np.arange(H))

    # Convert to 3D coordinates
    z = depth
    x = (u - cx) * z / fx
    y = (v - cy) * z / fy

    xyz = np.stack([x, y, z], axis=-1)
    return xyz

def get_point_cloud_ranges_from_depth(depth):
    """
    Get coordinate ranges from depth image by converting to XYZ.

    Args:
        depth: (H, W) depth array

    Returns:
        dict: Coordinate ranges
    """
    try:
        # Import the correct depth_to_xyz function from convert.py
        from convert import depth_to_xyz
        xyz = depth_to_xyz(depth, debug=False, validate_params=False,
                          auto_scale_intrinsics=True, auto_scale_depth=True)
    except ImportError:
        # Fallback to local implementation
        xyz = depth_to_xyz(depth)

    # Filter out invalid points (zero depth or NaN)
    valid_mask = (depth > 0) & (~np.isnan(depth)) & (~np.isinf(depth))
    if not np.any(valid_mask):
        return {"x": (0, 0), "y": (0, 0), "z": (0, 0)}

    valid_xyz = xyz[valid_mask]

    ranges = {
        "x": (float(valid_xyz[:, 0].min()), float(valid_xyz[:, 0].max())),
        "y": (float(valid_xyz[:, 1].min()), float(valid_xyz[:, 1].max())),
        "z": (float(valid_xyz[:, 2].min()), float(valid_xyz[:, 2].max()))
    }

    return ranges

def get_point_cloud_ranges_from_xyz(xyz_tensor):
    """
    Get coordinate ranges directly from XYZ point cloud tensor.
    This is used when we have XYZ data from episode format (dataset_engine.py style).

    Args:
        xyz_tensor: (3, H, W) or (H, W, 3) XYZ tensor/array

    Returns:
        dict: Coordinate ranges
    """
    # Handle different tensor formats
    if isinstance(xyz_tensor, torch.Tensor):
        xyz_array = xyz_tensor.numpy()
    else:
        xyz_array = xyz_tensor

    # Convert from (3, H, W) to (H, W, 3) if needed
    if xyz_array.shape[0] == 3 and len(xyz_array.shape) == 3:
        xyz_array = xyz_array.transpose(1, 2, 0)  # (3, H, W) -> (H, W, 3)

    # Filter out invalid points (NaN, Inf, or zero coordinates)
    valid_mask = (~np.isnan(xyz_array).any(axis=-1)) & \
                 (~np.isinf(xyz_array).any(axis=-1)) & \
                 (np.abs(xyz_array).sum(axis=-1) > 1e-6)  # Not all zeros

    if not np.any(valid_mask):
        return {"x": (0, 0), "y": (0, 0), "z": (0, 0)}

    valid_xyz = xyz_array[valid_mask]  # Shape: (N, 3)

    ranges = {
        "x": (float(valid_xyz[:, 0].min()), float(valid_xyz[:, 0].max())),
        "y": (float(valid_xyz[:, 1].min()), float(valid_xyz[:, 1].max())),
        "z": (float(valid_xyz[:, 2].min()), float(valid_xyz[:, 2].max()))
    }

    return ranges

def add_text_to_image(image, action, gripper_history, pc_ranges):
    """
    Create a larger image with the original image on top and text information below.

    Args:
        image: PIL Image
        action: Action array (8D)
        gripper_history: List of last 3 gripper states
        pc_ranges: Point cloud coordinate ranges

    Returns:
        PIL Image with annotations
    """
    # Resize the original image to be larger (512x512)
    original_size = image.size
    target_size = (512, 512)
    resized_image = image.resize(target_size, Image.Resampling.LANCZOS)

    # Try to use a larger font
    try:
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 16)
        small_font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 14)
    except:
        try:
            font = ImageFont.truetype("arial.ttf", 16)
            small_font = ImageFont.truetype("arial.ttf", 14)
        except:
            font = ImageFont.load_default()
            small_font = ImageFont.load_default()

    # Prepare text content
    text_lines = []

    # Action information
    if action is not None and len(action) >= 8:
        text_lines.append(f"Action Position: [{action[0]:.3f}, {action[1]:.3f}, {action[2]:.3f}]")
        text_lines.append(f"Action Rotation: [{action[3]:.3f}, {action[4]:.3f}, {action[5]:.3f}, {action[6]:.3f}]")
        text_lines.append(f"Action Gripper: {action[7]:.3f}")
        text_lines.append("")  # Empty line for spacing

    # Gripper history (last 3 states)

    text_lines.append("Gripper History (Position):")
    for i, gripper in enumerate(gripper_history[-3:]):
        if gripper is not None and len(gripper) >= 3:
            text_lines.append(f"  t{i-2}: [{gripper[0]:.5f}, {gripper[1]:.5f}, {gripper[2]:.5f}]")
    text_lines.append("")  # Empty line for spacing

    # Point cloud ranges
    # if pc_ranges:
    #     text_lines.append("Point Cloud Coordinate Ranges:")
    #     text_lines.append(f"  X: [{pc_ranges['x'][0]:.3f}, {pc_ranges['x'][1]:.3f}]")
    #     text_lines.append(f"  Y: [{pc_ranges['y'][0]:.3f}, {pc_ranges['y'][1]:.3f}]")
    #     text_lines.append(f"  Z: [{pc_ranges['z'][0]:.3f}, {pc_ranges['z'][1]:.3f}]")
    text_lines.append("Gripper History (Rot):")
    for i, gripper in enumerate(gripper_history[-3:]):
        if gripper is not None and len(gripper) >= 3:
            text_lines.append(f"  t{i-2}: [{gripper[3]:.5f}, {gripper[4]:.5f}, {gripper[5]:.5f},{gripper[6]:.5f}]")
    text_lines.append("")  # Empty line for spacing

    text_lines.append("Gripper History (Gripper):")
    for i, gripper in enumerate(gripper_history[-3:]):
        if gripper is not None and len(gripper) >= 3:
            text_lines.append(f"  t{i-2}: [{gripper[7]:.5f}]")
    text_lines.append("")  # Empty line for spacing

    # Calculate text area height
    line_height = 20
    text_area_height = max(200, len(text_lines) * line_height + 40)  # At least 200px for text

    # Create a new larger image: image on top, white area with text below
    total_height = target_size[1] + text_area_height
    final_image = Image.new('RGB', (target_size[0], total_height), color='white')

    # Paste the resized image at the top
    final_image.paste(resized_image, (0, 0))

    # Draw text in the white area below
    draw = ImageDraw.Draw(final_image)

    y_offset = target_size[1] + 20  # Start 20px below the image
    x_offset = 20  # 20px margin from left

    for line in text_lines:
        if line.strip():  # Non-empty line
            # Use larger font for headers, smaller for data
            current_font = font if any(header in line for header in ["Action", "Gripper History", "Point Cloud"]) else small_font
            draw.text((x_offset, y_offset), line, fill=(0, 0, 0), font=current_font)
        y_offset += line_height

    return final_image

def convert_episode_to_transitions(episode_data):
    """
    Convert episode format to transition format for processing.

    Episode format: [frame_ids, obs_tensors, actions, cameras, grippers, trajectories]
    Transition format: list of dicts with 'observations' and 'actions'

    Args:
        episode_data: Episode data in dataset_engine format

    Returns:
        list: List of transition dictionaries
    """
    try:
        frame_ids = episode_data[0]
        obs_tensors = episode_data[1]  # Shape: (T, n_cam, 2, 3, H, W)
        actions = episode_data[2]      # List of action tensors
        cameras = episode_data[3]      # Camera info
        grippers = episode_data[4]     # List of gripper tensors
        trajectories = episode_data[5] if len(episode_data) > 5 else None

        # Build gripper history from trajectories like dataset_engine.py _build_trajectory_history
        gripper_history_list = []

        if trajectories and len(trajectories) > 0:
            # Use trajectories (episode[5]) like in dataset_engine.py
            for i in range(len(frame_ids)):
                history = []

                if i < len(trajectories):
                    traj_segment = trajectories[i]  # Shape: (seq_len, 8)

                    # Convert to numpy if it's a tensor
                    if hasattr(traj_segment, 'numpy'):
                        traj_array = traj_segment.numpy()
                    else:
                        traj_array = np.array(traj_segment)

                    seq_len = traj_array.shape[0]

                    # Get last 3 trajectory points as history (like _build_trajectory_history)
                    for j in range(3):
                        if seq_len >= 3:
                            # Take last 3 frames as history
                            traj_point = traj_array[seq_len - 3 + j]
                            history.append(traj_point[:3])  # Position only
                        else:
                            # If not enough frames, use first frame to fill gaps
                            if j < (3 - seq_len):
                                traj_point = traj_array[0] if seq_len > 0 else np.zeros(8)
                                history.append(traj_point[:3])
                            else:
                                actual_idx = j - (3 - seq_len)
                                traj_point = traj_array[actual_idx]
                                history.append(traj_point[:3])
                else:
                    # No trajectory data, use zeros
                    history = [np.zeros(3), np.zeros(3), np.zeros(3)]

                gripper_history_list.append(history)
        else:
            # Fallback: use grippers like before
            for i in range(len(frame_ids)):
                history = []
                for offset in [2, 1, 0]:  # i-2, i-1, i
                    hist_idx = max(0, i - offset)
                    if hist_idx < len(grippers):
                        gripper_tensor = grippers[hist_idx]
                        if hasattr(gripper_tensor, 'numpy'):
                            gripper_array = gripper_tensor.numpy().flatten()
                        else:
                            gripper_array = np.array(gripper_tensor).flatten()
                        history.append(gripper_array[:3])  # Only position
                    else:
                        history.append(np.zeros(3))
                gripper_history_list.append(history)

        transitions = []

        for i, frame_id in enumerate(frame_ids):
            # Extract RGB and depth from obs_tensors
            if i < len(obs_tensors):
                obs_tensor = obs_tensors[i]  # Shape: (n_cam, 2, 3, H, W)

                # Assume front camera is the first one (index 0)
                front_obs = obs_tensor[0]  # Shape: (2, 3, H, W)
                rgb_tensor = front_obs[0]  # Shape: (3, H, W)
                xyz_tensor = front_obs[1]  # Shape: (3, H, W)

                # Convert RGB from tensor format to image format
                # From (3, H, W) with values in [-1, 1] to (H, W, 3) with values in [0, 255]
                rgb_array = rgb_tensor.transpose(1, 2, 0)  # (H, W, 3)
                rgb_array = ((rgb_array + 1) * 127.5).astype(np.uint8)  # [-1,1] -> [0,255]

                # Create a dummy depth from Z component of XYZ
                depth_array = xyz_tensor[2].astype(np.float32)  # (H, W)

                # Store XYZ data for point cloud range calculation
                xyz_data = xyz_tensor  # Shape: (3, H, W)

                # Create state from gripper info
                if i < len(grippers):
                    gripper_tensor = grippers[i]
                    if hasattr(gripper_tensor, 'numpy'):
                        gripper_array = gripper_tensor.numpy().flatten()
                    else:
                        gripper_array = np.array(gripper_tensor).flatten()
                    state = gripper_array.reshape(1, -1)  # Shape: (1, N)
                else:
                    state = np.zeros((1, 8))  # Default state

                # Get action
                if i < len(actions):
                    action_tensor = actions[i]
                    if hasattr(action_tensor, 'numpy'):
                        action_array = action_tensor.numpy().flatten()
                    else:
                        action_array = np.array(action_tensor).flatten()
                else:
                    action_array = np.zeros(8)  # Default action

                # Create transition
                transition = {
                    'observations': {
                        'front': [{
                            'rgb': rgb_array,
                            'depth': depth_array,
                            'xyz': xyz_data  # Add XYZ data for point cloud ranges
                        }],
                        'state': state
                    },
                    'actions': action_array,
                    'gripper_history': gripper_history_list[i] if i < len(gripper_history_list) else [np.zeros(3), np.zeros(3), np.zeros(3)]
                }

                transitions.append(transition)

        print(f"Converted episode to {len(transitions)} transitions")
        return transitions

    except Exception as e:
        print(f"Error converting episode to transitions: {e}")
        return []

def save_images_from_pickle(pickle_path, output_dir):
    """
    Reads a pickle file containing trajectory data and saves the RGB images with annotations.

    Args:
        pickle_path (str): Path to the input pickle file.
        output_dir (str): Directory to save the images.
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"Created output directory: {output_dir}")

    print(f"Loading pickle file from: {pickle_path}")
    try:
        with open(pickle_path, 'rb') as f:
            data = pickle.load(f)
    except FileNotFoundError:
        print(f"Error: Pickle file not found at '{pickle_path}'")
        return
    except Exception as e:
        print(f"Error loading pickle file: {e}")
        return

    # Check if data is in the expected format
    print(f"Data type: {type(data)}")

    # Handle different data formats
    if isinstance(data, list):
        if len(data) == 0:
            print("Error: Empty data list")
            return

        # Check if it's a list of transitions (dict format) or episode format (list format)
        first_item = data[0]
        print(f"First item type: {type(first_item)}")

        if isinstance(first_item, dict):
            # Standard transition format: list of dicts
            print(f"Found {len(data)} transitions in the file.")
            transitions = data

            # Debug: Check the structure of the first transition
            print(f"First transition keys: {list(first_item.keys())}")
            if 'observations' in first_item:
                obs = first_item['observations']
                print(f"Observations keys: {list(obs.keys())}")
                if 'state' in obs:
                    state_data = obs['state']
                    print(f"State data type: {type(state_data)}")
                    if isinstance(state_data, np.ndarray):
                        print(f"State shape: {state_data.shape}")
                    elif isinstance(state_data, list):
                        print(f"State list length: {len(state_data)}")
                        if len(state_data) > 0:
                            print(f"First state element type: {type(state_data[0])}")
                            if isinstance(state_data[0], np.ndarray):
                                print(f"First state element shape: {state_data[0].shape}")

        elif isinstance(first_item, list) or isinstance(first_item, int):
            # Episode format: [frame_ids, obs_tensors, actions, cameras, grippers, trajectories]
            print("Detected episode format (from dataset_engine.py)")
            print(f"Episode has {len(data)} components")

            if len(data) >= 6:
                frame_ids = data[0]
                obs_tensors = data[1]
                actions = data[2]
                cameras = data[3]
                grippers = data[4]
                trajectories = data[5]

                print(f"Frame IDs: {len(frame_ids) if isinstance(frame_ids, list) else 'Not a list'}")
                print(f"Obs tensors shape: {obs_tensors.shape if hasattr(obs_tensors, 'shape') else type(obs_tensors)}")
                print(f"Actions: {len(actions) if isinstance(actions, list) else type(actions)}")
                print(f"Grippers: {len(grippers) if isinstance(grippers, list) else type(grippers)}")

                # Convert episode format to transition format for processing
                transitions = convert_episode_to_transitions(data)
            else:
                print("Error: Episode format doesn't have enough components")
                return
        else:
            print(f"Error: Unexpected first item type: {type(first_item)}")
            return
    else:
        print(f"Error: Expected data to be a list, but got {type(data)}")
        return

    saved_count = 0

    # Build gripper history for all transitions using dataset_engine.py approach
    gripper_history_all = []

    # First, extract all gripper states (full 8D states, not just position)
    all_gripper_states = []
    for i, transition in enumerate(transitions):
        try:
            # Extract gripper state from observations['state'] or actions
            gripper_state = None

            # Try to get from state first (current gripper position)
            if 'observations' in transition and 'state' in transition['observations']:
                state_data = transition['observations']['state']

                if isinstance(state_data, np.ndarray):
                    if state_data.ndim == 2:
                        state = state_data[0]  # Shape: (N,) where N >= 8
                    else:
                        state = state_data  # Already 1D
                    gripper_state = state[:8] if len(state) >= 8 else np.pad(state, (0, max(0, 8-len(state))))
                elif isinstance(state_data, list) and len(state_data) > 0:
                    state = state_data[0]
                    if isinstance(state, np.ndarray):
                        gripper_state = state[:8] if len(state) >= 8 else np.pad(state, (0, max(0, 8-len(state))))

            # Fallback to actions if state not available
            if gripper_state is None and 'actions' in transition:
                action = transition['actions']
                if isinstance(action, np.ndarray) and len(action) >= 8:
                    gripper_state = action[:8]

            # Default to zeros if nothing found
            if gripper_state is None:
                gripper_state = np.zeros(8)

            all_gripper_states.append(gripper_state)

        except Exception as e:
            print(f"Warning: Could not extract gripper state for transition {i}: {e}")
            all_gripper_states.append(np.zeros(8))

    # Build gripper history using dataset_engine.py approach: [i-2, i-1, i]
    for i in range(len(transitions)):
        gripper_history = []

        # Get 3 historical states: max(0, i-2), max(0, i-1), i
        for offset in [2, 1, 0]:  # i-2, i-1, i
            hist_idx = max(0, i - offset)
            gripper_history.append(all_gripper_states[hist_idx][:3])  # Only position (first 3 elements)

        gripper_history_all.append(gripper_history)

    for i, transition in enumerate(transitions):
        try:
            # Following the structure from convert.py: obs -> front -> [0] -> rgb
            rgb_array = transition['observations']['front'][0]['rgb']

            # Extract point cloud ranges
            pc_ranges = None
            try:
                front_obs = transition['observations']['front'][0]

                # Try to get XYZ data first (from episode format)
                if 'xyz' in front_obs:
                    xyz_data = front_obs['xyz']
                    pc_ranges = get_point_cloud_ranges_from_xyz(xyz_data)
                # Fallback to depth data (from transition format)
                elif 'depth' in front_obs:
                    depth_array = front_obs['depth']
                    if isinstance(depth_array, np.ndarray):
                        pc_ranges = get_point_cloud_ranges_from_depth(depth_array)
                else:
                    print(f"Warning: No depth or XYZ data found for transition {i}")
            except KeyError as e:
                print(f"Warning: Could not extract point cloud data for transition {i}: {e}")
            except Exception as e:
                print(f"Warning: Error processing point cloud data for transition {i}: {e}")

            # Extract action information
            action = None
            try:
                action = transition['actions']
                if isinstance(action, np.ndarray):
                    action = action.flatten()  # Ensure it's 1D
            except KeyError:
                print(f"Warning: No action data found for transition {i}")

            # Get gripper history - try from transition first, then from pre-built list

            gripper_history = trajectories[i]

            # The RGB array is expected to be a numpy array (H, W, 3) with uint8 values
            if isinstance(rgb_array, np.ndarray) and rgb_array.dtype == np.uint8:
                image = Image.fromarray(rgb_array)

                # Add annotations to the image
                annotated_image = add_text_to_image(image, action, gripper_history, pc_ranges)

                image_path = os.path.join(output_dir, f"transition_{i:04d}.png")
                annotated_image.save(image_path)
                saved_count += 1
            else:
                print(f"Warning: Skipping transition {i}. RGB data is not a valid numpy uint8 array. Got type: {type(rgb_array)}, dtype: {rgb_array.dtype if hasattr(rgb_array, 'dtype') else 'N/A'}")

        except KeyError as e:
            print(f"Warning: Skipping transition {i}. KeyError: {e}. The data structure might be different than expected.")
        except Exception as e:
            print(f"An error occurred while processing transition {i}: {e}")

    print(f"Done. Saved {saved_count} images in '{output_dir}'.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Save RGB images from a trajectory pickle file.")
    parser.add_argument(
        '--pickle_file', 
        type=str, 
        required=True,
        help='Path to the input pickle file.'
    )
    parser.add_argument(
        '--output_dir', 
        type=str, 
        default='saved_images',
        help='Directory to save the output images.'
    )
    args = parser.parse_args()

    save_images_from_pickle(args.pickle_file, args.output_dir) 