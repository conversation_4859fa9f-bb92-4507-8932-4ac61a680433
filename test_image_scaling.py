#!/usr/bin/env python3
"""
测试图像缩放功能
验证convert.py和real_robot_test_complete.py中的图像处理是否一致
"""

import numpy as np
import cv2
import torch
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 导入我们修改的函数
from convert import crop_and_resize_image, crop_and_resize_pointcloud
from real_robot_test_complete import DataPreprocessor

def create_test_data():
    """创建测试数据"""
    # 创建424x240的测试图像
    H, W = 240, 424
    
    # RGB图像：渐变色彩
    rgb = np.zeros((H, W, 3), dtype=np.uint8)
    for i in range(H):
        for j in range(W):
            rgb[i, j, 0] = int(255 * i / H)  # 红色渐变
            rgb[i, j, 1] = int(255 * j / W)  # 绿色渐变
            rgb[i, j, 2] = 128  # 蓝色固定
    
    # 深度图：中心到边缘的距离
    depth = np.zeros((H, W), dtype=np.float32)
    center_y, center_x = H // 2, W // 2
    for i in range(H):
        for j in range(W):
            dist = np.sqrt((i - center_y)**2 + (j - center_x)**2)
            depth[i, j] = 0.5 + dist * 0.01  # 0.5米到2米的深度范围
    
    # 创建简单的点云（这里只是示例，实际应该通过depth_to_xyz生成）
    xyz = np.zeros((H, W, 3), dtype=np.float32)
    for i in range(H):
        for j in range(W):
            xyz[i, j, 0] = (j - center_x) * 0.001  # X坐标
            xyz[i, j, 1] = (i - center_y) * 0.001  # Y坐标
            xyz[i, j, 2] = depth[i, j]  # Z坐标
    
    return rgb, depth, xyz

def test_convert_functions():
    """测试convert.py中的函数"""
    print("=== 测试convert.py中的函数 ===")
    
    rgb, depth, xyz = create_test_data()
    target_size = (256, 256)
    
    print(f"原始数据形状:")
    print(f"  RGB: {rgb.shape}")
    print(f"  Depth: {depth.shape}")
    print(f"  XYZ: {xyz.shape}")
    
    # 测试RGB图像缩放
    rgb_resized, transform_info = crop_and_resize_image(rgb, target_size, debug=True)
    print(f"缩放后RGB形状: {rgb_resized.shape}")
    
    # 测试深度图缩放
    depth_resized, _ = crop_and_resize_image(depth, target_size, debug=False)
    print(f"缩放后Depth形状: {depth_resized.shape}")
    
    # 测试点云缩放
    xyz_resized = crop_and_resize_pointcloud(xyz, transform_info, debug=True)
    print(f"缩放后XYZ形状: {xyz_resized.shape}")
    
    return rgb_resized, depth_resized, xyz_resized, transform_info

def test_preprocessor_functions():
    """测试real_robot_test_complete.py中的DataPreprocessor"""
    print("\n=== 测试DataPreprocessor中的函数 ===")
    
    rgb, depth, xyz = create_test_data()
    target_size = (256, 256)
    
    # 创建预处理器
    preprocessor = DataPreprocessor(target_shape=target_size)
    
    # 测试RGB图像缩放
    rgb_resized, transform_info = preprocessor.crop_and_resize_image(rgb, target_size, debug=True)
    print(f"预处理器缩放后RGB形状: {rgb_resized.shape}")
    
    # 测试点云缩放
    xyz_resized = preprocessor.crop_and_resize_pointcloud(xyz, transform_info, debug=True)
    print(f"预处理器缩放后XYZ形状: {xyz_resized.shape}")
    
    return rgb_resized, xyz_resized, transform_info

def test_consistency():
    """测试两个实现的一致性"""
    print("\n=== 测试实现一致性 ===")
    
    rgb, depth, xyz = create_test_data()
    target_size = (256, 256)
    
    # convert.py的结果
    rgb1, depth1, xyz1, info1 = test_convert_functions()
    
    # DataPreprocessor的结果
    rgb2, xyz2, info2 = test_preprocessor_functions()
    
    # 比较结果
    rgb_diff = np.abs(rgb1.astype(np.float32) - rgb2.astype(np.float32)).mean()
    xyz_diff = np.abs(xyz1 - xyz2).mean()
    
    print(f"\n一致性检查:")
    print(f"  RGB平均差异: {rgb_diff:.6f}")
    print(f"  XYZ平均差异: {xyz_diff:.6f}")
    print(f"  变换信息一致: {info1 == info2}")
    
    if rgb_diff < 1e-6 and xyz_diff < 1e-6:
        print("✅ 两个实现完全一致!")
    else:
        print("❌ 两个实现存在差异!")
    
    return rgb_diff < 1e-6 and xyz_diff < 1e-6

def test_gripper_coordinate_transform():
    """测试gripper坐标变换"""
    print("\n=== 测试gripper坐标变换 ===")
    
    # 创建预处理器
    preprocessor = DataPreprocessor(target_shape=(256, 256))
    
    # 测试gripper位置
    test_gripper_poses = [
        torch.tensor([0.0, 0.0, 0.5]),  # 中心位置
        torch.tensor([0.1, 0.1, 0.6]),  # 右上
        torch.tensor([-0.1, -0.1, 0.4]),  # 左下
    ]
    
    for i, gripper_pose in enumerate(test_gripper_poses):
        u, v = preprocessor.obs_to_attn_from_transition(
            gripper_pose, 
            target_size=(256, 256), 
            original_size=(240, 424),
            debug=True
        )
        print(f"测试位置 {i+1}: 世界坐标 {gripper_pose.numpy()} -> 像素坐标 ({u}, {v})")

def save_test_images():
    """保存测试图像用于视觉验证"""
    print("\n=== 保存测试图像 ===")
    
    rgb, depth, xyz = create_test_data()
    
    # 保存原始图像
    cv2.imwrite("test_original_rgb.jpg", cv2.cvtColor(rgb, cv2.COLOR_RGB2BGR))
    
    # 缩放后的图像
    rgb_resized, _, _, _ = test_convert_functions()
    cv2.imwrite("test_resized_rgb.jpg", cv2.cvtColor(rgb_resized, cv2.COLOR_RGB2BGR))
    
    print("测试图像已保存:")
    print("  test_original_rgb.jpg - 原始424x240图像")
    print("  test_resized_rgb.jpg - 缩放后256x256图像")

if __name__ == "__main__":
    print("开始测试图像缩放功能...")
    
    try:
        # 运行所有测试
        test_convert_functions()
        test_preprocessor_functions()
        is_consistent = test_consistency()
        test_gripper_coordinate_transform()
        save_test_images()
        
        print(f"\n{'='*50}")
        if is_consistent:
            print("🎉 所有测试通过！图像缩放功能正常工作。")
        else:
            print("⚠️ 测试发现问题，请检查实现。")
        print(f"{'='*50}")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
