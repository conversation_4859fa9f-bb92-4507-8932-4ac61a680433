import pickle
import numpy as np
import matplotlib.pyplot as plt
from typing import List, Dict

def load_and_inspect_pkl(file_path: str):
    """加载并检查pickle文件内容"""
    try:
        with open(file_path, 'rb') as f:
            data: List[Dict] = pickle.load(f)
        
        print(f"=== 文件加载成功 ===")
        print(f"总Transition数量: {len(data)}")
        
        # 检查第一个Transition的结构
        first_trans = data[0]
        print("\n=== Transition结构 ===")
        print(f"Keys: {first_trans.keys()}")
        
        # 解析observations
        print("\n=== Observations内容 ===")
        obs = first_trans['observations']
        print(f"相机视角: {list(obs.keys())}")
        
        # 检查front相机数据
        front_data = obs['front']
        print(f"front相机数据类型: {type(front_data)}, 长度: {len(front_data)}")
        print(f"RGB图像形状: {front_data[0]['rgb'].shape if 'rgb' in front_data[0] else 'N/A'}")
        print(f"深度图形状: {front_data[0]['depth'].shape if 'depth' in front_data[0] else 'N/A'}")
        
        # 检查state数据
        state_data = obs['state']
        print(f"state数据形状: {state_data.shape}")
        print(f"示例state值: {state_data[0:9, :5]}...")
        
        # 检查actions
        print("\n=== Actions内容 ===")
        action = first_trans['actions']
        print(f"动作形状: {action.shape}")
        for i in range(0,20):
                print(f"动作内容: [x,y,z: {data[i]['actions'][:3]} | euler: {data[i]['actions'][3:6]} | gripper: {data[i]['actions'][6]}]")
                print('state',data[i]['observations']['state'])
        # 检查infos
        print("\n=== Infos内容 ===")
        infos = first_trans['infos']
        print(f"成功标记: {infos.get('succeed', 'N/A')}")
        print(f"左干预状态: {infos.get('left', 'N/A')}")
        print(f"右干预状态: {infos.get('right', 'N/A')}")
        for i in range(0,20):
                print('state',data[i]['observations']['state'])
        # 可视化示例
        if 'rgb' in front_data[0]:
            plt.figure(figsize=(10, 5))
            plt.subplot(121)
            plt.imshow(front_data[0]['rgb'])
            plt.title("RGB图像")
            
            if 'depth' in front_data[0]:
                plt.subplot(122)
                plt.imshow(front_data[0]['depth'], cmap='gray')
                plt.title("深度图")
            plt.show()
        
        return data
    
    except Exception as e:
        print(f"加载失败: {str(e)}")
        return None

# 使用示例
file_path = "/data/wangyiwen/3d_diffuser_actor/data/raw_data_try/train/pick_bread/ram_insertion_5_demos_2025-06-22_15-53-17.pkl"
transition_data = load_and_inspect_pkl(file_path)

# 如果需要进一步分析某个Transition
if transition_data is not None:
    print("\n=== 动作统计 ===")
    all_actions = np.array([t['actions'] for t in transition_data])
    print(f"X范围: [{all_actions[:, 0].min():.3f}, {all_actions[:, 0].max():.3f}]")
    print(f"Y范围: [{all_actions[:, 1].min():.3f}, {all_actions[:, 1].max():.3f}]")
    print(f"Z范围: [{all_actions[:, 2].min():.3f}, {all_actions[:, 2].max():.3f}]")
    print(f"夹爪操作次数: {(all_actions[:, 6] != 0).sum()}")