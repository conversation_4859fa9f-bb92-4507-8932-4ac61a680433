#!/usr/bin/env python3
"""
测试简化后的gripper逻辑
"""
import numpy as np
import sys
import os

# 添加当前目录到路径，以便导入convert模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from convert import convert_action_to_8d, reset_gripper_state, _gripper_state

def test_gripper_logic():
    """测试gripper状态变化逻辑"""
    print("🧪 测试gripper状态变化逻辑")
    print("=" * 50)
    
    # 模拟state数据 (19维)
    mock_state = np.zeros(19)
    mock_state[4:7] = [0.5, 0.3, 0.8]  # 位置 [x,y,z]
    mock_state[7:10] = [0.0, 0.0, 0.0]  # 欧拉角 [roll,pitch,yaw]
    
    # 重置gripper状态
    reset_gripper_state(0.5)
    print(f"初始gripper状态: {_gripper_state:.3f}")
    
    # 测试用例
    test_cases = [
        ("保持不变", [0.1, 0.1, 0.1, 0.0, 0.0, 0.0, 0.0]),  # gripper=0.0 (保持)
        ("保持不变", [0.1, 0.1, 0.1, 0.0, 0.0, 0.0, 0.05]), # gripper=0.05 (保持)
        ("闭合触发", [0.1, 0.1, 0.1, 0.0, 0.0, 0.0, -0.95]), # gripper=-0.95 (闭合)
        ("保持闭合", [0.1, 0.1, 0.1, 0.0, 0.0, 0.0, 0.2]),  # gripper=0.2 (保持)
        ("打开触发", [0.1, 0.1, 0.1, 0.0, 0.0, 0.0, 0.95]),  # gripper=0.95 (打开)
        ("保持打开", [0.1, 0.1, 0.1, 0.0, 0.0, 0.0, 0.1]),  # gripper=0.1 (保持)
        ("闭合触发", [0.1, 0.1, 0.1, 0.0, 0.0, 0.0, -1.0]),  # gripper=-1.0 (闭合)
    ]
    
    print("\n测试序列:")
    print("动作描述\t输入gripper\t输出gripper\t状态变化")
    print("-" * 60)
    
    for i, (description, action) in enumerate(test_cases):
        action_array = np.array(action)
        prev_state = _gripper_state
        
        # 调用转换函数
        result = convert_action_to_8d(action_array, mock_state)
        output_gripper = result[7]  # 第8个元素是gripper
        
        print(f"{description}\t{action[6]:+.2f}\t\t{output_gripper:.3f}\t\t{prev_state:.3f} -> {_gripper_state:.3f}")
    
    print("\n" + "=" * 50)
    
    # 验证范围约束
    print("\n🔍 验证gripper值范围:")
    reset_gripper_state(0.5)
    
    # 测试多次闭合触发，确保都在[0, 0.1]范围内
    close_values = []
    for _ in range(10):
        action = np.array([0, 0, 0, 0, 0, 0, -0.95])
        result = convert_action_to_8d(action, mock_state)
        close_values.append(result[7])
    
    print(f"闭合状态范围: [{min(close_values):.3f}, {max(close_values):.3f}] (期望: [0.000, 0.100])")
    
    # 测试多次打开触发，确保都在[0.9, 1.0]范围内  
    open_values = []
    for _ in range(10):
        action = np.array([0, 0, 0, 0, 0, 0, 0.95])
        result = convert_action_to_8d(action, mock_state)
        open_values.append(result[7])
        
    print(f"打开状态范围: [{min(open_values):.3f}, {max(open_values):.3f}] (期望: [0.900, 1.000])")
    
    # 验证状态保持
    print(f"\n🔄 验证状态保持:")
    reset_gripper_state(0.3)  # 设置一个中间状态
    action = np.array([0, 0, 0, 0, 0, 0, 0.1])  # 保持不变的动作
    result1 = convert_action_to_8d(action, mock_state)
    result2 = convert_action_to_8d(action, mock_state)
    print(f"连续保持动作: {result1[7]:.3f} -> {result2[7]:.3f} (应该相同)")
    
    print("\n✅ 测试完成!")

if __name__ == "__main__":
    test_gripper_logic()
