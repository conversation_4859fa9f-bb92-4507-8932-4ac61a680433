#!/usr/bin/env python3
"""
RGB彩色点云生成器
使用RGB图像为点云上色，并保存为PLY格式文件

作者: Assistant
日期: 2025-07-24
"""

import pickle
import numpy as np
import torch
import torch.nn.functional as F
from pathlib import Path
import os
import time
import argparse
from typing import Dict, List, Tuple, Optional


def depth_to_xyz_with_rgb(depth: np.ndarray, rgb: np.ndarray,
                         debug: bool = False,
                         validate_params: bool = True,
                         auto_scale_intrinsics: bool = True,
                         auto_scale_depth: bool = True) -> Tuple[np.ndarray, np.ndarray]:
    """
    将深度图转换为世界坐标系下的点云，并使用RGB图像上色。

    Args:
        depth (np.ndarray): (H, W) 深度图，可能需要缩放到米单位
        rgb (np.ndarray): (H, W, 3) RGB图像，值范围[0, 255]
        debug (bool): 是否输出调试信息
        validate_params (bool): 是否验证参数合理性
        auto_scale_intrinsics (bool): 是否自动缩放内参以匹配图像尺寸
        auto_scale_depth (bool): 是否自动缩放深度值到合理范围

    Returns:
        Tuple[np.ndarray, np.ndarray]:
            - pointcloud (np.ndarray): (H, W, 3) 世界系下的 XYZ 点云
            - colors (np.ndarray): (H, W, 3) 对应的RGB颜色值 [0, 255]
    """
    # 验证输入
    if depth.shape[:2] != rgb.shape[:2]:
        raise ValueError(f"深度图和RGB图像尺寸不匹配: {depth.shape[:2]} vs {rgb.shape[:2]}")

    # 深度值预处理和缩放
    depth_processed = depth.copy().astype(np.float32)

    if auto_scale_depth:
        # 检查深度值范围，判断是否需要缩放
        valid_depth_mask = depth_processed > 0
        if np.any(valid_depth_mask):
            depth_min = depth_processed[valid_depth_mask].min()
            depth_max = depth_processed[valid_depth_mask].max()

            if debug:
                print(f"原始深度值范围: [{depth_min:.4f}, {depth_max:.4f}]")

            # 如果深度值看起来像是16位整数（通常 > 10），则缩放到米
            if depth_max > 10.0:
                if depth_max > 1000:
                    # 可能是毫米单位，转换为米
                    depth_processed = depth_processed / 1000.0
                    if debug:
                        print("检测到毫米单位深度，转换为米")
                else:
                    # 可能是16位深度图，使用经验缩放
                    # 假设最大深度对应2米，进行线性缩放
                    depth_processed = depth_processed / depth_max * 2.0
                    if debug:
                        print(f"检测到16位深度图，缩放到0-2米范围")

                # 重新计算缩放后的范围
                valid_depth_mask = depth_processed > 0
                if np.any(valid_depth_mask):
                    new_min = depth_processed[valid_depth_mask].min()
                    new_max = depth_processed[valid_depth_mask].max()
                    if debug:
                        print(f"缩放后深度值范围: [{new_min:.4f}, {new_max:.4f}] 米")

            # 进一步限制深度范围到合理区间 (0.1m - 3.0m)
            depth_processed = np.clip(depth_processed, 0.0, 3.0)
            # 将过小的深度值设为0（无效）
            depth_processed[depth_processed < 0.05] = 0.0

    # 原始相机内参（标定于1280x720图像）
    original_intrinsics = {
        'fx': 647.855,
        'fy': 646.244,
        'cx': 635.578,
        'cy': 400.158
    }

    # 图像处理流程：1280x720 → 裁剪[0:720, 200:1080] → 缩放到目标尺寸
    original_width, original_height = 1280, 720
    crop_left, crop_top = 200, 0
    crop_width, crop_height = 880, 720  # 1080-200=880

    H, W = depth.shape

    # 根据实际的图像处理流程调整内参
    if auto_scale_intrinsics:
        # 步骤1: 调整内参以适应裁剪
        cropped_intrinsics = {
            'fx': original_intrinsics['fx'],
            'fy': original_intrinsics['fy'],
            'cx': original_intrinsics['cx'] - crop_left,
            'cy': original_intrinsics['cy'] - crop_top
        }

        # 步骤2: 调整内参以适应缩放
        scale_x = W / crop_width   # W / 880
        scale_y = H / crop_height  # H / 720

        intrinsics = {
            'fx': cropped_intrinsics['fx'] * scale_x,
            'fy': cropped_intrinsics['fy'] * scale_y,
            'cx': cropped_intrinsics['cx'] * scale_x,
            'cy': cropped_intrinsics['cy'] * scale_y
        }

        if debug:
            print(f"内参调整流程:")
            print(f"  原始图像: {original_width}x{original_height}")
            print(f"  裁剪区域: [{crop_top}:{crop_top+crop_height}, {crop_left}:{crop_left+crop_width}] → {crop_width}x{crop_height}")
            print(f"  最终尺寸: {W}x{H}")
            print(f"  缩放比例: X={scale_x:.4f}, Y={scale_y:.4f}")
    else:
        intrinsics = original_intrinsics.copy()

    # 验证内参与图像尺寸的合理性
    if validate_params:
        if intrinsics['cx'] > W or intrinsics['cy'] > H:
            print(f"⚠️ 警告: 主点坐标 ({intrinsics['cx']:.1f}, {intrinsics['cy']:.1f}) 超出图像尺寸 ({W}, {H})")

    K = np.array([
        [intrinsics['fx'], 0, intrinsics['cx']],
        [0, intrinsics['fy'], intrinsics['cy']],
        [0, 0, 1]
    ])

    # 使用与convert.py相同的高精度变换矩阵
    T_world2cam = np.array([
        [2.039413322245856031e-02, 9.997689532984076477e-01, 6.791122934426185864e-03, 2.849851955899636649e-02],
        [3.398053113376858780e-01, -5.431387454349623223e-04, -9.404956434705059598e-01, -1.249019213795134475e-01],
        [-9.402746565322296357e-01, 2.148825309114454907e-02, -3.397378772852505113e-01, 1.223038606263365580e+00],
        [0.0, 0.0, 0.0, 1.0]
    ])
    T_cam2world = np.linalg.inv(T_world2cam)

    u, v = np.meshgrid(np.arange(W), np.arange(H), indexing='xy')

    # 检查深度值的有效性
    if debug:
        print(f"图像尺寸: {H}x{W}")
        print(f"处理后深度值范围: {depth_processed.min():.4f} ~ {depth_processed.max():.4f} 米")
        print(f"有效深度像素: {np.sum(depth_processed > 0)} / {depth_processed.size} ({100*np.sum(depth_processed > 0)/depth_processed.size:.1f}%)")

    # 深度图转相机坐标系（标准针孔相机模型）
    z = depth_processed
    x = (u - K[0, 2]) * z / K[0, 0]
    y = (v - K[1, 2]) * z / K[1, 1]

    pcd_camera = np.stack((x, y, z), axis=-1)  # (H, W, 3)

    # 转换为齐次坐标
    ones = np.ones_like(z)[..., None]
    pcd_camera_homo = np.concatenate([pcd_camera, ones], axis=-1)  # (H, W, 4)

    # 相机坐标系 → 世界坐标系
    pcd_world = np.einsum('ij,hwj->hwi', T_cam2world, pcd_camera_homo)  # (H, W, 4)

    # 检查世界坐标系的有效性
    world_xyz = pcd_world[..., :3]
    if debug:
        valid_mask = depth_processed > 0
        if np.sum(valid_mask) > 0:
            print(f"世界坐标系范围:")
            print(f"  X: {world_xyz[valid_mask, 0].min():.4f} ~ {world_xyz[valid_mask, 0].max():.4f} 米")
            print(f"  Y: {world_xyz[valid_mask, 1].min():.4f} ~ {world_xyz[valid_mask, 1].max():.4f} 米")
            print(f"  Z: {world_xyz[valid_mask, 2].min():.4f} ~ {world_xyz[valid_mask, 2].max():.4f} 米")

    return world_xyz, rgb


def save_rgb_colored_pointcloud_as_ply(xyz: np.ndarray, 
                                      rgb: np.ndarray, 
                                      depth: np.ndarray,
                                      debug: bool = False, 
                                      save_dir: str = "rgb_pointclouds",
                                      filename_prefix: str = "rgb_pointcloud") -> Optional[str]:
    """
    保存RGB彩色点云为PLY格式文件
    
    Args:
        xyz (np.ndarray): (H, W, 3) 世界坐标系下的点云
        rgb (np.ndarray): (H, W, 3) RGB颜色图像，值范围[0, 255]
        depth (np.ndarray): (H, W) 原始深度图，用于确定有效点
        debug (bool): 是否输出调试信息
        save_dir (str): 保存目录
        filename_prefix (str): 文件名前缀
        
    Returns:
        Optional[str]: 保存的文件路径，如果保存失败则返回None
    """
    try:
        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)

        # 生成文件名（使用时间戳）
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"{filename_prefix}_{timestamp}.ply"
        filepath = os.path.join(save_dir, filename)

        # 获取有效点云（深度值大于0的点）
        valid_mask = depth > 0
        valid_xyz = xyz[valid_mask]
        valid_rgb = rgb[valid_mask]

        if len(valid_xyz) == 0:
            if debug:
                print("⚠️ 没有有效的点云数据，跳过保存")
            return None

        # 确保RGB值在[0, 255]范围内
        valid_rgb = np.clip(valid_rgb, 0, 255).astype(np.uint8)

        # 写入PLY文件
        with open(filepath, 'w') as f:
            # PLY文件头
            f.write("ply\n")
            f.write("format ascii 1.0\n")
            f.write(f"element vertex {len(valid_xyz)}\n")
            f.write("property float x\n")
            f.write("property float y\n")
            f.write("property float z\n")
            f.write("property uchar red\n")
            f.write("property uchar green\n")
            f.write("property uchar blue\n")
            f.write("end_header\n")

            # 写入点云数据
            for i in range(len(valid_xyz)):
                x, y, z = valid_xyz[i]
                r, g, b = valid_rgb[i]
                f.write(f"{x:.6f} {y:.6f} {z:.6f} {r} {g} {b}\n")

        if debug:
            print(f"✅ RGB彩色点云已保存到: {filepath}")
            print(f"   有效点数: {len(valid_xyz)}")
            print(f"   坐标范围: X[{valid_xyz[:, 0].min():.3f}, {valid_xyz[:, 0].max():.3f}]")
            print(f"            Y[{valid_xyz[:, 1].min():.3f}, {valid_xyz[:, 1].max():.3f}]")
            print(f"            Z[{valid_xyz[:, 2].min():.3f}, {valid_xyz[:, 2].max():.3f}]")
            print(f"   颜色范围: R[{valid_rgb[:, 0].min()}, {valid_rgb[:, 0].max()}]")
            print(f"            G[{valid_rgb[:, 1].min()}, {valid_rgb[:, 1].max()}]")
            print(f"            B[{valid_rgb[:, 2].min()}, {valid_rgb[:, 2].max()}]")

        return filepath

    except Exception as e:
        if debug:
            print(f"❌ 保存RGB彩色点云时出错: {e}")
        return None


def process_single_transition(transition: Dict, 
                            frame_idx: int = 0,
                            debug: bool = False,
                            save_dir: str = "rgb_pointclouds") -> Optional[str]:
    """
    处理单个transition，生成RGB彩色点云
    
    Args:
        transition (Dict): 单个transition数据
        frame_idx (int): 帧索引，用于文件命名
        debug (bool): 是否输出调试信息
        save_dir (str): 保存目录
        
    Returns:
        Optional[str]: 保存的文件路径，如果处理失败则返回None
    """
    try:
        # 提取观测数据
        obs = transition["observations"]["front"][0]
        rgb = obs["rgb"]  # (H, W, 3)
        depth = obs["depth"]  # (H, W)
        
        if debug:
            print(f"处理帧 {frame_idx}:")
            print(f"  RGB形状: {rgb.shape}, 数据类型: {rgb.dtype}")
            print(f"  深度形状: {depth.shape}, 数据类型: {depth.dtype}")
            print(f"  RGB值范围: [{rgb.min()}, {rgb.max()}]")
            print(f"  深度值范围: [{depth.min():.4f}, {depth.max():.4f}]")
        
        # 转换深度图为点云并获取颜色
        xyz, colors = depth_to_xyz_with_rgb(
            depth, rgb,
            debug=debug,
            validate_params=False,
            auto_scale_intrinsics=False,
            auto_scale_depth=True  # 启用深度自动缩放
        )
        
        # 保存RGB彩色点云
        filename_prefix = f"frame_{frame_idx:04d}"
        filepath = save_rgb_colored_pointcloud_as_ply(
            xyz, colors, depth,
            debug=debug,
            save_dir=save_dir,
            filename_prefix=filename_prefix
        )
        
        return filepath
        
    except Exception as e:
        if debug:
            print(f"❌ 处理帧 {frame_idx} 时出错: {e}")
        return None


def process_transitions_batch(transitions: List[Dict], 
                            max_frames: Optional[int] = None,
                            debug: bool = False,
                            save_dir: str = "rgb_pointclouds") -> List[str]:
    """
    批量处理transitions，生成RGB彩色点云
    
    Args:
        transitions (List[Dict]): transitions数据列表
        max_frames (Optional[int]): 最大处理帧数，None表示处理所有帧
        debug (bool): 是否输出调试信息
        save_dir (str): 保存目录
        
    Returns:
        List[str]: 成功保存的文件路径列表
    """
    saved_files = []
    
    # 确定处理的帧数
    total_frames = len(transitions)
    if max_frames is not None:
        total_frames = min(total_frames, max_frames)
    
    print(f"开始批量处理 {total_frames} 帧...")
    
    for i in range(total_frames):
        if debug:
            print(f"\n--- 处理第 {i+1}/{total_frames} 帧 ---")
        
        filepath = process_single_transition(
            transitions[i], 
            frame_idx=i,
            debug=debug,
            save_dir=save_dir
        )
        
        if filepath:
            saved_files.append(filepath)
            if not debug:
                print(f"✅ 帧 {i+1} 已保存: {os.path.basename(filepath)}")
        else:
            print(f"❌ 帧 {i+1} 处理失败")
    
    print(f"\n批量处理完成！成功保存 {len(saved_files)}/{total_frames} 个文件")
    return saved_files


def process_pickle_file(pickle_path: str,
                       output_dir: str = "rgb_pointclouds",
                       max_frames: Optional[int] = None,
                       debug: bool = False) -> List[str]:
    """
    处理pickle文件，生成RGB彩色点云

    Args:
        pickle_path (str): pickle文件路径
        output_dir (str): 输出目录
        max_frames (Optional[int]): 最大处理帧数
        debug (bool): 是否输出调试信息

    Returns:
        List[str]: 成功保存的文件路径列表
    """
    try:
        # 加载pickle文件
        with open(pickle_path, "rb") as f:
            transitions = pickle.load(f)

        print(f"加载pickle文件: {pickle_path}")
        print(f"包含 {len(transitions)} 个transitions")

        # 创建以文件名命名的子目录
        pickle_name = Path(pickle_path).stem
        save_dir = os.path.join(output_dir, pickle_name)

        # 批量处理
        saved_files = process_transitions_batch(
            transitions,
            max_frames=max_frames,
            debug=debug,
            save_dir=save_dir
        )

        return saved_files

    except Exception as e:
        print(f"❌ 处理pickle文件时出错: {e}")
        return []


def process_folder(input_dir: str,
                  output_dir: str = "rgb_pointclouds",
                  max_files: Optional[int] = None,
                  max_frames_per_file: Optional[int] = None,
                  debug: bool = False) -> Dict[str, List[str]]:
    """
    处理文件夹中的所有pickle文件

    Args:
        input_dir (str): 输入目录路径
        output_dir (str): 输出目录路径
        max_files (Optional[int]): 最大处理文件数
        max_frames_per_file (Optional[int]): 每个文件最大处理帧数
        debug (bool): 是否输出调试信息

    Returns:
        Dict[str, List[str]]: 文件名到保存路径列表的映射
    """
    input_path = Path(input_dir)
    if not input_path.exists():
        print(f"❌ 输入目录不存在: {input_dir}")
        return {}

    # 查找所有pickle文件
    pkl_files = sorted(input_path.glob("*.pkl"))
    if not pkl_files:
        print(f"❌ 在目录 {input_dir} 中未找到pickle文件")
        return {}

    # 限制处理文件数
    if max_files is not None:
        pkl_files = pkl_files[:max_files]

    print(f"找到 {len(pkl_files)} 个pickle文件")

    results = {}
    for i, pkl_file in enumerate(pkl_files):
        print(f"\n{'='*50}")
        print(f"处理文件 {i+1}/{len(pkl_files)}: {pkl_file.name}")
        print(f"{'='*50}")

        saved_files = process_pickle_file(
            str(pkl_file),
            output_dir=output_dir,
            max_frames=max_frames_per_file,
            debug=debug
        )

        results[pkl_file.name] = saved_files

    # 统计结果
    total_saved = sum(len(files) for files in results.values())
    print(f"\n{'='*50}")
    print(f"所有文件处理完成！")
    print(f"处理了 {len(pkl_files)} 个文件，总共保存了 {total_saved} 个点云文件")
    print(f"输出目录: {output_dir}")
    print(f"{'='*50}")

    return results


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="使用RGB图像为点云上色并保存为PLY格式",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 处理单个pickle文件
  python rgb_colored_pointcloud.py --input_file data.pkl --output_dir colored_clouds

  # 处理整个文件夹
  python rgb_colored_pointcloud.py --input_dir raw_data_try/pick_bread --output_dir colored_clouds

  # 限制处理数量
  python rgb_colored_pointcloud.py --input_dir raw_data_try/pick_bread --max_files 5 --max_frames 10

  # 开启调试模式
  python rgb_colored_pointcloud.py --input_file data.pkl --debug
        """
    )

    # 输入选项（互斥）
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument("--input_file", type=str, help="输入的pickle文件路径")
    input_group.add_argument("--input_dir", type=str, help="输入目录路径（包含多个pickle文件）")

    # 输出选项
    parser.add_argument("--output_dir", type=str, default="rgb_pointclouds",
                       help="输出目录路径 (默认: rgb_pointclouds)")

    # 限制选项
    parser.add_argument("--max_files", type=int, default=None,
                       help="最大处理文件数（仅在处理文件夹时有效）")
    parser.add_argument("--max_frames", type=int, default=None,
                       help="每个文件最大处理帧数")

    # 调试选项
    parser.add_argument("--debug", action="store_true", help="开启调试模式")

    args = parser.parse_args()

    try:
        if args.input_file:
            # 处理单个文件
            print("模式: 单文件处理")
            saved_files = process_pickle_file(
                args.input_file,
                output_dir=args.output_dir,
                max_frames=args.max_frames,
                debug=args.debug
            )

            if saved_files:
                print(f"\n✅ 成功处理！保存了 {len(saved_files)} 个点云文件")
                print("文件列表:")
                for filepath in saved_files:
                    print(f"  - {filepath}")
            else:
                print("\n❌ 处理失败，未保存任何文件")

        elif args.input_dir:
            # 处理文件夹
            print("模式: 文件夹批量处理")
            results = process_folder(
                args.input_dir,
                output_dir=args.output_dir,
                max_files=args.max_files,
                max_frames_per_file=args.max_frames,
                debug=args.debug
            )

            if results:
                print("\n✅ 批量处理完成！")
                for filename, saved_files in results.items():
                    print(f"  {filename}: {len(saved_files)} 个文件")
            else:
                print("\n❌ 批量处理失败")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    main()
