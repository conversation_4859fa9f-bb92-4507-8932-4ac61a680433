#!/bin/bash

# 真机测试启动脚本
# 使用方法: ./run_real_robot_test_complete.sh [配置文件路径] [测试步数]

# 默认参数
CONFIG_FILE=${1:-"real_robot_test_config.json"}
TEST_STEPS=${2:-100}

echo "=== 3D Diffuser Actor 真机测试 ==="
echo "配置文件: $CONFIG_FILE"
echo "测试步数: $TEST_STEPS"
echo "=================================="

# 检查配置文件是否存在
if [ ! -f "$CONFIG_FILE" ]; then
    echo "错误: 配置文件 $CONFIG_FILE 不存在"
    exit 1
fi

# 检查Python环境
if ! command -v python &> /dev/null; then
    echo "错误: Python环境未找到"
    exit 1
fi

# 检查必要的Python包
echo "检查依赖包..."
python -c "import torch, numpy, cv2, pyrealsense2; print('依赖检查通过')" || {
    echo "错误: 缺少必要的Python包"
    echo "请确保已安装: torch, numpy, opencv-python, pyrealsense2"
    exit 1
}

# 运行真机测试
echo "启动真机测试..."
python real_robot_test_complete.py --config "$CONFIG_FILE" --steps "$TEST_STEPS"

echo "测试完成!"