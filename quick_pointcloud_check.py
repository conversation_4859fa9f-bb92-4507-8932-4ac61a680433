#!/usr/bin/env python3
"""
快速检查点云坐标分布
简单快速地查看点云的坐标范围，不需要复杂的可视化

作者: Assistant
日期: 2025-07-24
"""

import numpy as np
from convert import depth_to_xyz


def preprocess_depth_for_analysis(depth: np.ndarray, debug: bool = False) -> np.ndarray:
    """
    预处理深度图，自动缩放到合理范围
    """
    depth_processed = depth.copy().astype(np.float32)

    # 检查深度值范围，判断是否需要缩放
    valid_depth_mask = depth_processed > 0
    if not np.any(valid_depth_mask):
        return depth_processed

    depth_min = depth_processed[valid_depth_mask].min()
    depth_max = depth_processed[valid_depth_mask].max()

    if debug:
        print(f"  原始深度值范围: [{depth_min:.4f}, {depth_max:.4f}]")

    # 如果深度值看起来像是16位整数（通常 > 10），则缩放到米
    if depth_max > 10.0:
        if depth_max > 1000:
            # 可能是毫米单位，转换为米
            depth_processed = depth_processed / 1000.0
            if debug:
                print("  检测到毫米单位深度，转换为米")
        else:
            # 可能是16位深度图，使用经验缩放
            # 假设最大深度对应2米，进行线性缩放
            depth_processed = depth_processed / depth_max * 2.0
            if debug:
                print(f"  检测到16位深度图，缩放到0-2米范围")

        # 重新计算缩放后的范围
        valid_depth_mask = depth_processed > 0
        if np.any(valid_depth_mask):
            new_min = depth_processed[valid_depth_mask].min()
            new_max = depth_processed[valid_depth_mask].max()
            if debug:
                print(f"  缩放后深度值范围: [{new_min:.4f}, {new_max:.4f}] 米")

    # 进一步限制深度范围到合理区间 (0.05m - 3.0m)
    depth_processed = np.clip(depth_processed, 0.0, 3.0)
    # 将过小的深度值设为0（无效）
    depth_processed[depth_processed < 0.05] = 0.0

    return depth_processed


def create_test_depth():
    """创建测试深度图"""
    H, W = 128, 128
    
    # 创建一个简单的深度图
    y, x = np.meshgrid(np.arange(H), np.arange(W), indexing='ij')
    
    # 方案1: 平面深度图
    depth1 = 0.8 + 0.4 * (x / W) + 0.2 * (y / H)  # 0.8-1.4米范围
    
    # 方案2: 球面深度图
    center_x, center_y = W // 2, H // 2
    radius = min(W, H) // 3
    dist_from_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)
    depth2 = 1.0 + 0.3 * np.sin(dist_from_center / radius * np.pi)
    
    # 方案3: 随机深度图
    np.random.seed(42)
    depth3 = 0.5 + 1.0 * np.random.random((H, W))
    
    return depth1, depth2, depth3


def quick_analyze_pointcloud(depth_raw, name="Unknown"):
    """快速分析单个点云"""
    print(f"\n=== {name} ===")
    print(f"深度图形状: {depth_raw.shape}")
    print(f"原始深度值范围: [{depth_raw.min():.3f}, {depth_raw.max():.3f}]")

    # 预处理深度图
    depth = preprocess_depth_for_analysis(depth_raw, debug=True)
    print(f"处理后深度值范围: [{depth.min():.3f}, {depth.max():.3f}] 米")

    # 转换为点云
    xyz = depth_to_xyz(depth, debug=False, validate_params=False, auto_scale_intrinsics=True)
    
    # 获取有效点
    valid_mask = depth > 0
    valid_xyz = xyz[valid_mask]
    
    print(f"有效点数: {len(valid_xyz):,} / {depth.size:,} ({len(valid_xyz)/depth.size:.1%})")
    
    if len(valid_xyz) > 0:
        print(f"世界坐标范围:")
        print(f"  X: [{valid_xyz[:, 0].min():.4f}, {valid_xyz[:, 0].max():.4f}] 米 (跨度: {valid_xyz[:, 0].max()-valid_xyz[:, 0].min():.4f})")
        print(f"  Y: [{valid_xyz[:, 1].min():.4f}, {valid_xyz[:, 1].max():.4f}] 米 (跨度: {valid_xyz[:, 1].max()-valid_xyz[:, 1].min():.4f})")
        print(f"  Z: [{valid_xyz[:, 2].min():.4f}, {valid_xyz[:, 2].max():.4f}] 米 (跨度: {valid_xyz[:, 2].max()-valid_xyz[:, 2].min():.4f})")
        
        print(f"坐标中心:")
        print(f"  X中心: {valid_xyz[:, 0].mean():.4f} ± {valid_xyz[:, 0].std():.4f}")
        print(f"  Y中心: {valid_xyz[:, 1].mean():.4f} ± {valid_xyz[:, 1].std():.4f}")
        print(f"  Z中心: {valid_xyz[:, 2].mean():.4f} ± {valid_xyz[:, 2].std():.4f}")
        
        # 距离统计
        distances = np.linalg.norm(valid_xyz, axis=1)
        print(f"距离原点:")
        print(f"  最近: {distances.min():.4f} 米")
        print(f"  最远: {distances.max():.4f} 米")
        print(f"  平均: {distances.mean():.4f} ± {distances.std():.4f} 米")
        
        # 检查异常值
        has_nan = np.any(np.isnan(valid_xyz))
        has_inf = np.any(np.isinf(valid_xyz))
        if has_nan or has_inf:
            print(f"⚠️ 异常值: NaN={has_nan}, Inf={has_inf}")
    else:
        print("❌ 没有有效的点云数据")
    
    return xyz


def check_coordinate_system():
    """检查坐标系的基本特性"""
    print("\n" + "="*60)
    print("坐标系特性检查")
    print("="*60)
    
    # 创建不同深度的测试点
    test_depths = [0.5, 1.0, 1.5, 2.0]  # 不同深度值
    H, W = 128, 128
    
    for depth_val in test_depths:
        print(f"\n--- 深度 {depth_val} 米的点云分布 ---")
        
        # 创建均匀深度图
        depth = np.full((H, W), depth_val)
        
        # 转换为点云
        xyz = depth_to_xyz(depth, debug=False, validate_params=False, auto_scale_intrinsics=True)
        
        # 分析四个角落的点
        corners = {
            "左上角": xyz[0, 0],
            "右上角": xyz[0, -1],
            "左下角": xyz[-1, 0],
            "右下角": xyz[-1, -1],
            "中心": xyz[H//2, W//2]
        }
        
        print(f"关键点坐标 (深度={depth_val}m):")
        for name, coord in corners.items():
            print(f"  {name}: ({coord[0]:.3f}, {coord[1]:.3f}, {coord[2]:.3f})")


def check_depth_scaling():
    """检查深度缩放的影响"""
    print("\n" + "="*60)
    print("深度缩放影响检查")
    print("="*60)
    
    H, W = 64, 64  # 使用较小的图像以便观察
    
    # 创建中心点深度图
    depth = np.zeros((H, W))
    center_y, center_x = H // 2, W // 2
    depth[center_y, center_x] = 1.0  # 中心点深度1米
    
    xyz = depth_to_xyz(depth, debug=False, validate_params=False, auto_scale_intrinsics=True)
    center_coord = xyz[center_y, center_x]
    
    print(f"图像尺寸: {H}x{W}")
    print(f"中心像素坐标: ({center_x}, {center_y})")
    print(f"中心点深度: 1.0 米")
    print(f"中心点世界坐标: ({center_coord[0]:.4f}, {center_coord[1]:.4f}, {center_coord[2]:.4f})")
    
    # 检查不同图像尺寸的影响
    sizes = [(32, 32), (64, 64), (128, 128), (256, 256)]
    print(f"\n不同图像尺寸的中心点坐标:")
    
    for h, w in sizes:
        depth_test = np.zeros((h, w))
        cy, cx = h // 2, w // 2
        depth_test[cy, cx] = 1.0
        
        xyz_test = depth_to_xyz(depth_test, debug=False, validate_params=False, auto_scale_intrinsics=True)
        center_test = xyz_test[cy, cx]
        
        print(f"  {h}x{w}: ({center_test[0]:.4f}, {center_test[1]:.4f}, {center_test[2]:.4f})")


def main():
    """主函数"""
    print("快速点云坐标分布检查")
    print("="*60)
    
    # 1. 测试不同类型的深度图
    depth1, depth2, depth3 = create_test_depth()
    
    quick_analyze_pointcloud(depth1, "平面深度图")
    quick_analyze_pointcloud(depth2, "球面深度图") 
    quick_analyze_pointcloud(depth3, "随机深度图")
    
    # 2. 检查坐标系特性
    check_coordinate_system()
    
    # 3. 检查深度缩放影响
    check_depth_scaling()
    
    print("\n" + "="*60)
    print("检查完成！")
    print("="*60)
    
    print("\n关键观察:")
    print("1. 世界坐标系的X、Y、Z范围")
    print("2. 不同深度值对应的坐标分布")
    print("3. 图像尺寸对坐标计算的影响")
    print("4. 坐标系的中心位置和方向")


if __name__ == "__main__":
    main()
