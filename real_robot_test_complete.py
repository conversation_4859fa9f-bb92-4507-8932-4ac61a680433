#!/usr/bin/env python3
"""
完整的真机测试脚本
基于训练pipeline和提供的代码组件构建
"""

import os
import json
import queue
import threading
import time
import subprocess
import numpy as np
import cv2
import torch
import torch.nn.functional as F
from scipy.spatial.transform import Rotation
import pyrealsense2 as rs
from pathlib import Path
import argparse

from diffuser_actor.trajectory_optimization.diffuser_actor import DiffuserActor

from utils.common_utils import (
    get_gripper_loc_bounds
)


class VideoCapture:
    """多线程视频捕获类"""
    def __init__(self, cap, name=None):
        if name is None:
            name = cap.name
        self.name = name
        self.q = queue.Queue()
        self.cap = cap
        self.t = threading.Thread(target=self._reader)
        self.t.daemon = False
        self.enable = True
        self.t.start()

    def _reader(self):
        while self.enable:
            ret, frame = self.cap.read()
            if not ret:
                break
            if not self.q.empty():
                try:
                    self.q.get_nowait()  # discard previous (unprocessed) frame
                except queue.Empty:
                    pass
            self.q.put(frame)

    def read(self):
        return self.q.get(timeout=5)

    def close(self):
        self.enable = False
        self.t.join()
        self.cap.close()


class RSCapture:
    """RealSense相机捕获类"""
    def get_device_serial_numbers(self):
        devices = rs.context().devices
        return [d.get_info(rs.camera_info.serial_number) for d in devices]

    def __init__(self, name, serial_number, dim=(424, 240), fps=15, depth=True, exposure=40000):
        self.name = name
        assert serial_number in self.get_device_serial_numbers()
        self.serial_number = serial_number
        self.depth = depth
        self.pipe = rs.pipeline()
        self.cfg = rs.config()
        self.cfg.enable_device(self.serial_number)
        self.cfg.enable_stream(rs.stream.color, dim[0], dim[1], rs.format.bgr8, fps)
        if self.depth:
            self.cfg.enable_stream(rs.stream.depth, dim[0], dim[1], rs.format.z16, fps)
        self.profile = self.pipe.start(self.cfg)
        self.s = self.profile.get_device().query_sensors()[0]
        self.s.set_option(rs.option.exposure, exposure)

        # Create an align object
        align_to = rs.stream.color
        self.align = rs.align(align_to)

    def read(self):
        frames = self.pipe.wait_for_frames()
        aligned_frames = self.align.process(frames)
        color_frame = aligned_frames.get_color_frame()
        if self.depth:
            depth_frame = aligned_frames.get_depth_frame()

        if color_frame.is_video_frame():
            image = np.asarray(color_frame.get_data())
            if self.depth and depth_frame.is_depth_frame():
                depth = np.expand_dims(np.asarray(depth_frame.get_data()), axis=2)
                return True, np.concatenate((image, depth), axis=-1)
            else:
                return True, image
        else:
            return False, None

    def close(self):
        self.pipe.stop()
        self.cfg.disable_all_streams()


class RobotController:
    def __init__(self, robot_url):
        self.url = robot_url
        # self.action_scale = (0.01, 0.06, 1)

        # RelativeFrame相关属性 - 仅用于状态获取，不用于动作执行
        self.reset_pose = None  # 重置位姿，用作相对坐标系原点
        self.T_r_o_inv = None   # 从世界坐标系到重置位姿坐标系的变换矩阵

    def set_reset_pose(self, reset_pose=None):
        """设置重置位姿作为相对坐标系原点 - 仅用于状态获取"""
        if reset_pose is None:
            # 使用当前位姿作为重置位姿
            reset_pose = self.get_currpos()

        self.reset_pose = reset_pose.copy()

        # 构建变换矩阵（复现RelativeFrame逻辑）
        from scipy.spatial.transform import Rotation

        # 构建齐次变换矩阵
        T_reset = np.eye(4)
        T_reset[:3, 3] = reset_pose[:3]  # 位置
        T_reset[:3, :3] = Rotation.from_quat(reset_pose[3:7]).as_matrix()  # 旋转

        # 计算逆变换矩阵
        # self.T_r_o_inv = np.linalg.inv(T_reset)

        print(f"重置位姿已设置: {reset_pose}")

    def get_currpos(self):
        """获取当前机器人位姿"""
        curl_command = [
            "curl",
            "-X", "POST", 
            self.url + "getstate",
            "-H", "Content-Type: application/json",
            "-s"
        ]
        
        result = subprocess.run(curl_command, capture_output=True, text=True)
        
        if result.returncode != 0:
            raise Exception("Failed to get robot state. Curl command failed.")
        
        ps = json.loads(result.stdout)
        currpos = np.array(ps["pose"])
        gripper = np.array(ps["gripper_pos"])
        currpos = np.concatenate([currpos, [gripper]])
        return currpos

    def get_relative_pose_with_gripper(self):
        """获取相对于重置位姿的相对位姿，包含夹爪状态 - 仅用于状态获取"""
        if self.reset_pose is None:
            raise ValueError("请先调用set_reset_pose()设置重置位姿")

        # 获取当前绝对位姿
        current_pose = self.get_currpos()

        # # 构建当前位姿的齐次变换矩阵
        # T_current = np.eye(4)
        # T_current[:3, 3] = current_pose[:3]
        # T_current[:3, :3] = Rotation.from_quat(current_pose[3:7]).as_matrix()
        #
        # # 计算相对变换
        # T_relative = self.T_r_o_inv @ T_current
        #
        # # 提取相对位置和姿态
        # relative_pos = T_relative[:3, 3]
        # relative_quat = Rotation.from_matrix(T_relative[:3, :3]).as_quat()

        # 获取夹爪状态
        # if len(current_pose) > 7:
        #     gripper = current_pose[-1]
        # else:
        #     print("⚠️ 当前位姿中不包含夹爪状态，使用默认值0.0")
        #     gripper = 0.0

        # 构建完整的相对状态：[x,y,z,qx,qy,qz,qw,gripper]
        # relative_state = np.concatenate([relative_pos, relative_quat, [gripper]])

        return current_pose

    def create_training_compatible_gripper_state(self):
        """创建与训练时完全兼容的轨迹状态 - 仅用于模型推理"""
        # 直接获取包含夹爪的相对状态
        relative_state = self.get_relative_pose_with_gripper()  # [x,y,z,qx,qy,qz,qw,gripper]

        # 无需额外转换，直接返回8维状态
        # 这已经与convert.py中的轨迹构建逻辑一致了
        return relative_state

    def send_gripper_command(self, pos: float, mode="binary"):
        """发送夹爪指令"""
        if mode == "binary":
            if pos <= 0.5:  # close gripper 范围是0-1，0为闭合，1为开启
                curl_command = [
                    "curl",
                    "-X", "POST", 
                    self.url + "close_gripper",
                    "-H", "Content-Type: application/json"
                ]
                subprocess.run(curl_command)
            elif pos >= 0.5:  # open gripper
                curl_command = [
                    "curl",
                    "-X", "POST", 
                    self.url + "open_gripper",
                    "-H", "Content-Type: application/json"
                ]
                subprocess.run(curl_command)
        else:
            raise NotImplementedError("Continuous gripper control is optional")

    def send_pos_command(self, pos: np.ndarray):
        """发送位置指令"""
        arr = np.array(pos).astype(np.float32)
        data = {"arr": arr.tolist()}
        json_data = json.dumps(data)
        
        curl_command = [
            "curl",
            "-X", "POST", 
            self.url + "pose",
            "-H", "Content-Type: application/json",
            "-d", json_data
        ]
        
        subprocess.run(curl_command)

    def step(self, model_action: np.ndarray):
        """
        执行模型预测的动作，正确处理旋转格式转换

        Args:
            model_action: 模型输出的8维动作 [x, y, z, qx, qy, qz, qw, gripper]
        """
        if len(model_action) != 8:
            raise ValueError(f"期望8维模型输出，但得到{len(model_action)}维")

        gripper_scaled = model_action[7]
        #
        # # 1. 位置处理：位置增量
        # xyz_action = model_action[:3]
        #
        # # 2. 旋转处理：欧拉角增量
        # action_quat = model_action[3:7]  # [qx,qy,qz,qw]
        #
        # # 计算相对旋转：R_delta = R_target * R_current^(-1)
        # action_rot = Rotation.from_quat(action_quat)
        #
        # # 转换为欧拉角增量（与数据收集时一致）
        # euler_delta = action_rot.as_euler('xyz')  # [roll, pitch, yaw]
        #
        # # 3. 夹爪处理
        # gripper_prob = model_action[7]
        # gripper_action = 1.0 if gripper_prob > 0.5 else -1.0
        #
        # currpos = self.get_currpos()  # [x,y,z,qx,qy,qz,qw]
        # nextpos = currpos.copy()
        # nextpos[:3] = nextpos[:3] + xyz_action * self.action_scale[0]
        #
        # nextpos[3:7] = (
        #         Rotation.from_euler("xyz", euler_delta * self.action_scale[1])
        #         * Rotation.from_quat(currpos[3:7])
        # ).as_quat()
        #
        # gripper_scaled = gripper_action * self.action_scale[2]

        self.send_gripper_command(gripper_scaled)
        self.send_pos_command(model_action[:7])


class DataPreprocessor:
    """数据预处理类 - 与训练脚本convert.py保持完全一致"""
    def __init__(self, target_shape=(256, 256)):
        self.target_shape = target_shape
        self.image_crop = lambda img: img[0:720, 200:1080]  # 与训练时一致

        # 原始相机内参（标定于424x240图像）- 与convert.py完全一致
        self.original_intrinsics = {
            'fx': 216.809,
            'fy': 216.809,
            'cx': 212.494,
            'cy': 118.398
        }
        #
        # # 图像处理参数 - 与convert.py一致
        # self.original_width, self.original_height = 1280, 720
        # self.crop_left, self.crop_top = 200, 0
        # self.crop_width, self.crop_height = 880, 720  # 1080-200=880

        # 使用与convert.py完全相同的高精度变换矩阵
        self.T_world2cam = np.array([
            [2.039413322245856031e-02, 9.997689532984076477e-01, 6.791122934426185864e-03, 2.849851955899636649e-02],
            [3.398053113376858780e-01, -5.431387454349623223e-04, -9.404956434705059598e-01, -1.249019213795134475e-01],
            [-9.402746565322296357e-01, 2.148825309114454907e-02, -3.397378772852505113e-01, 1.223038606263365580e+00],
            [0.0, 0.0, 0.0, 1.0]
        ])
        self.T_cam2world = np.linalg.inv(self.T_world2cam)

    def crop_and_resize_image(self, image, target_size=None, debug=False):
        """
        将图像先裁剪成正方形，再缩放到目标尺寸 - 与convert.py完全一致

        Args:
            image (np.ndarray): 输入图像，形状为 (H, W) 或 (H, W, C)
            target_size (tuple): 目标尺寸 (height, width)
            debug (bool): 是否输出调试信息

        Returns:
            np.ndarray: 处理后的图像
            dict: 包含裁剪和缩放信息的字典
        """
        if target_size is None:
            target_size = self.target_shape

        original_shape = image.shape
        H, W = original_shape[:2]

        if debug:
            print(f"原始图像尺寸: {H}x{W}")

        # 计算裁剪参数以获得正方形
        if H < W:
            # 高度较小，以高度为准裁剪宽度
            crop_size = H
            crop_left = (W - H) // 2
            crop_top = 0
            crop_right = crop_left + H
            crop_bottom = H
        else:
            # 宽度较小，以宽度为准裁剪高度
            crop_size = W
            crop_left = 0
            crop_top = (H - W) // 2
            crop_right = W
            crop_bottom = crop_top + W

        # 执行裁剪
        if len(original_shape) == 3:  # RGB图像
            cropped_image = image[crop_top:crop_bottom, crop_left:crop_right, :]
        else:  # 深度图
            cropped_image = image[crop_top:crop_bottom, crop_left:crop_right]

        # 缩放到目标尺寸
        if len(original_shape) == 3:  # RGB图像
            resized_image = cv2.resize(cropped_image, target_size, interpolation=cv2.INTER_LINEAR)
        else:  # 深度图，使用最近邻插值避免深度值被平滑
            resized_image = cv2.resize(cropped_image, target_size, interpolation=cv2.INTER_NEAREST)

        # 返回处理信息
        transform_info = {
            'original_size': (H, W),
            'crop_region': (crop_top, crop_bottom, crop_left, crop_right),
            'crop_size': crop_size,
            'target_size': target_size,
            'scale_factor': target_size[0] / crop_size  # 假设target_size是正方形
        }

        if debug:
            print(f"裁剪区域: [{crop_top}:{crop_bottom}, {crop_left}:{crop_right}] -> {crop_size}x{crop_size}")
            print(f"缩放到: {target_size[0]}x{target_size[1]}")
            print(f"缩放比例: {transform_info['scale_factor']:.4f}")

        return resized_image, transform_info

    def crop_and_resize_pointcloud(self, pointcloud, transform_info, debug=False):
        """
        根据图像变换信息对点云进行相应的裁剪和缩放 - 与convert.py完全一致

        Args:
            pointcloud (np.ndarray): 原始点云，形状为 (H, W, 3)
            transform_info (dict): 从crop_and_resize_image返回的变换信息
            debug (bool): 是否输出调试信息

        Returns:
            np.ndarray: 处理后的点云，形状为 (target_H, target_W, 3)
        """
        crop_top, crop_bottom, crop_left, crop_right = transform_info['crop_region']
        target_size = transform_info['target_size']

        # 裁剪点云（与图像裁剪保持一致）
        cropped_pointcloud = pointcloud[crop_top:crop_bottom, crop_left:crop_right, :]

        # 对点云进行缩放
        # 注意：这里我们使用最近邻插值来避免点云坐标被平滑
        resized_pointcloud = np.zeros((target_size[0], target_size[1], 3), dtype=pointcloud.dtype)

        for i in range(3):  # 对每个坐标轴分别处理
            resized_pointcloud[:, :, i] = cv2.resize(
                cropped_pointcloud[:, :, i],
                target_size,
                interpolation=cv2.INTER_NEAREST
            )

        if debug:
            print(f"点云裁剪: {pointcloud.shape} -> {cropped_pointcloud.shape} -> {resized_pointcloud.shape}")

        return resized_pointcloud

    def obs_to_attn_from_transition(self, gripper_pose, target_size=None, original_size=(240, 424), debug=False):
        """
        计算gripper在缩放后图像中的像素坐标 - 与convert.py完全一致

        Args:
            gripper_pose: gripper的世界坐标
            target_size: 目标图像尺寸 (height, width)
            original_size: 原始图像尺寸 (height, width)
            debug: 是否输出调试信息

        Returns:
            tuple: (u, v) 在缩放后图像中的像素坐标
        """
        if target_size is None:
            target_size = self.target_shape

        # 计算图像变换参数
        H_orig, W_orig = original_size
        H_target, W_target = target_size

        # 计算裁剪参数（裁剪成正方形）
        if H_orig < W_orig:
            # 高度较小，以高度为准裁剪宽度
            crop_size = H_orig
            crop_left = (W_orig - H_orig) // 2
            crop_top = 0
        else:
            # 宽度较小，以宽度为准裁剪高度
            crop_size = W_orig
            crop_left = 0
            crop_top = (H_orig - W_orig) // 2

        # 调整内参以适应图像变换
        # 步骤1: 调整内参以适应裁剪
        cropped_intrinsics = {
            'fx': self.original_intrinsics['fx'],
            'fy': self.original_intrinsics['fy'],
            'cx': self.original_intrinsics['cx'] - crop_left,
            'cy': self.original_intrinsics['cy'] - crop_top
        }

        # 步骤2: 调整内参以适应缩放
        scale_factor = H_target / crop_size  # 假设target_size是正方形

        adjusted_intrinsics = {
            'fx': cropped_intrinsics['fx'] * scale_factor,
            'fy': cropped_intrinsics['fy'] * scale_factor,
            'cx': cropped_intrinsics['cx'] * scale_factor,
            'cy': cropped_intrinsics['cy'] * scale_factor
        }

        K = torch.tensor([
            [adjusted_intrinsics['fx'], 0, adjusted_intrinsics['cx'], 0],
            [0, adjusted_intrinsics['fy'], adjusted_intrinsics['cy'], 0],
            [0, 0, 1, 0]
        ])  # shape (3, 4)

        cam_T_world = torch.tensor([
            [2.039413322245856031e-02, 9.997689532984076477e-01, 6.791122934426185864e-03, 2.849851955899636649e-02],
            [3.398053113376858780e-01, -5.431387454349623223e-04, -9.404956434705059598e-1, -1.249019213795134475e-01],
            [-9.402746565322296357e-01, 2.148825309114454907e-02, -3.397378772852505113e-01, 1.223038606263365580e+00],
            [0.0, 0.0, 0.0, 1.0]
        ], dtype=torch.float32)

        # gripper 世界系坐标 → 相机像素坐标
        gripper_pos_3 = gripper_pose[:3].clone().detach().float()

        gripper_pos_4 = F.pad(gripper_pos_3, (0, 1), value=1).unsqueeze(1)  # (4,1)
        gripper_cam = cam_T_world @ gripper_pos_4  # (4,1)

        uvw = K @ gripper_cam  # shape (3,1)
        u = int((uvw[0] / uvw[2]).round().item())
        v = int((uvw[1] / uvw[2]).round().item())

        if debug:
            print(f"Gripper世界坐标: {gripper_pos_3}")
            print(f"调整后内参: fx={adjusted_intrinsics['fx']:.2f}, fy={adjusted_intrinsics['fy']:.2f}")
            print(f"            cx={adjusted_intrinsics['cx']:.2f}, cy={adjusted_intrinsics['cy']:.2f}")
            print(f"像素坐标: ({u}, {v})")

        return u, v

    def depth_to_xyz(self, depth, debug=False):
        """
        将深度图转换为世界坐标系下的点云 - 与convert.py完全一致

        Args:
            depth (np.ndarray): (H, W) 深度图，可能需要缩放到米单位
            debug (bool): 是否输出调试信息
        Returns:
            pointcloud (np.ndarray): (H, W, 3) 世界系下的 XYZ 点云
        """
        # ==================== 深度值预处理和缩放 ====================
        depth_processed = depth.copy().astype(np.float32)

        # 自动检测深度值范围并进行适当缩放
        valid_depth_mask = depth_processed > 0

        if np.any(valid_depth_mask):
            depth_min = depth_processed[valid_depth_mask].min()
            depth_max = depth_processed[valid_depth_mask].max()

            if debug:
                print(f"原始深度值范围: [{depth_min:.4f}, {depth_max:.4f}]")

            # 深度值缩放策略：与convert.py一致
            if depth_max > 1000:
                depth_processed = depth_processed / 1000.0
                if debug:
                    print("✓ 检测到毫米单位深度，转换为米 (除以1000)")

            # 重新计算缩放后的深度范围
            valid_depth_mask = depth_processed > 0
            if np.any(valid_depth_mask):
                new_min = depth_processed[valid_depth_mask].min()
                new_max = depth_processed[valid_depth_mask].max()
                if debug:
                    print(f"缩放后深度值范围: [{new_min:.4f}, {new_max:.4f}] 米")

        # 最终安全检查：限制深度范围到物理合理区间
        depth_processed = np.clip(depth_processed, 0.0, 2.5)
        depth_processed[depth_processed < 0.05] = 0.0  # 过近的点设为无效

        # ==================== 相机内参设置 ====================
        H, W = depth_processed.shape
        intrinsics=self.original_intrinsics
        #
        # # 根据实际的图像处理流程调整内参 - 与convert.py一致
        # # 步骤1: 调整内参以适应裁剪
        # cropped_intrinsics = {
        #     'fx': self.original_intrinsics['fx'],
        #     'fy': self.original_intrinsics['fy'],
        #     'cx': self.original_intrinsics['cx'] - self.crop_left,
        #     'cy': self.original_intrinsics['cy'] - self.crop_top
        # }
        #
        # # 步骤2: 调整内参以适应缩放
        # scale_x = W / self.crop_width   # W / 880
        # scale_y = H / self.crop_height  # H / 720
        #
        # intrinsics = {
        #     'fx': cropped_intrinsics['fx'] * scale_x,
        #     'fy': cropped_intrinsics['fy'] * scale_y,
        #     'cx': cropped_intrinsics['cx'] * scale_x,
        #     'cy': cropped_intrinsics['cy'] * scale_y
        # }
        #
        # if debug:
        #     print(f"内参调整流程:")
        #     print(f"  原始图像: {self.original_width}x{self.original_height}")
        #     print(f"  裁剪区域: [{self.crop_top}:{self.crop_top+self.crop_height}, {self.crop_left}:{self.crop_left+self.crop_width}] → {self.crop_width}x{self.crop_height}")
        #     print(f"  最终尺寸: {W}x{H}")
        #     print(f"  缩放比例: X={scale_x:.4f}, Y={scale_y:.4f}")
        #     print(f"  最终内参: fx={intrinsics['fx']:.2f}, fy={intrinsics['fy']:.2f}")
        #     print(f"            cx={intrinsics['cx']:.2f}, cy={intrinsics['cy']:.2f}")

        K = np.array([
            [intrinsics['fx'], 0, intrinsics['cx']],
            [0, intrinsics['fy'], intrinsics['cy']],
            [0, 0, 1]
        ])

        # ==================== 深度图转点云计算 ====================
        u, v = np.meshgrid(np.arange(W), np.arange(H), indexing='xy')

        # 深度图转相机坐标系（标准针孔相机模型）
        z = depth_processed
        x = (u - K[0, 2]) * z / K[0, 0]
        y = (v - K[1, 2]) * z / K[1, 1]

        pcd_camera = np.stack((x, y, z), axis=-1)  # (H, W, 3)

        # 转换为齐次坐标
        ones = np.ones_like(z)[..., None]
        pcd_camera_homo = np.concatenate([pcd_camera, ones], axis=-1)  # (H, W, 4)

        # 相机坐标系 → 世界坐标系
        pcd_world = np.einsum('ij,hwj->hwi', self.T_cam2world, pcd_camera_homo)  # (H, W, 4)

        # 检查世界坐标系的有效性
        world_xyz = pcd_world[..., :3]
        if debug:
            valid_mask = depth_processed > 0
            if np.sum(valid_mask) > 0:
                print(f"世界坐标系范围:")
                print(f"  X: {world_xyz[valid_mask, 0].min():.4f} ~ {world_xyz[valid_mask, 0].max():.4f} 米")
                print(f"  Y: {world_xyz[valid_mask, 1].min():.4f} ~ {world_xyz[valid_mask, 1].max():.4f} 米")
                print(f"  Z: {world_xyz[valid_mask, 2].min():.4f} ~ {world_xyz[valid_mask, 2].max():.4f} 米")

                # 坐标合理性检查
                max_coord = max(abs(world_xyz[valid_mask, 0].max()), abs(world_xyz[valid_mask, 0].min()),
                               abs(world_xyz[valid_mask, 1].max()), abs(world_xyz[valid_mask, 1].min()),
                               abs(world_xyz[valid_mask, 2].max()), abs(world_xyz[valid_mask, 2].min()))
                if max_coord > 10:
                    print(f"⚠️ 警告: 最大坐标值 {max_coord:.2f} 米可能过大，请检查深度缩放")
                else:
                    print(f"✓ 坐标范围合理，最大坐标值: {max_coord:.2f} 米")

        return world_xyz

    def validate_data_consistency(self, frame):
        """
        验证数据处理与训练脚本的一致性
        用于调试和确保推理时的数据格式与训练时完全一致
        """
        print("=== 数据一致性验证 ===")

        # 处理数据
        observation = self.preprocess_observation(frame, debug=True)

        # 验证张量形状
        rgb_shape = observation["rgb"].shape
        xyz_shape = observation["xyz"].shape

        print(f"✓ RGB张量形状: {rgb_shape} (期望: (3, {self.target_shape[0]}, {self.target_shape[1]}))")
        print(f"✓ XYZ张量形状: {xyz_shape} (期望: (3, {self.target_shape[0]}, {self.target_shape[1]}))")

        # 验证数值范围
        rgb_min, rgb_max = observation["rgb"].min().item(), observation["rgb"].max().item()
        xyz_min, xyz_max = observation["xyz"].min().item(), observation["xyz"].max().item()

        print(f"✓ RGB值范围: [{rgb_min:.4f}, {rgb_max:.4f}] (期望: [-1, 1])")
        print(f"✓ XYZ值范围: [{xyz_min:.4f}, {xyz_max:.4f}] (期望: 合理的世界坐标)")

        # 验证数据类型
        print(f"✓ RGB数据类型: {observation['rgb'].dtype} (期望: torch.float32)")
        print(f"✓ XYZ数据类型: {observation['xyz'].dtype} (期望: torch.float32)")

        # 检查异常值
        rgb_nan_count = torch.isnan(observation["rgb"]).sum().item()
        xyz_nan_count = torch.isnan(observation["xyz"]).sum().item()
        rgb_inf_count = torch.isinf(observation["rgb"]).sum().item()
        xyz_inf_count = torch.isinf(observation["xyz"]).sum().item()

        print(f"✓ RGB异常值: NaN={rgb_nan_count}, Inf={rgb_inf_count}")
        print(f"✓ XYZ异常值: NaN={xyz_nan_count}, Inf={xyz_inf_count}")

        # 验证与训练数据格式的一致性
        expected_rgb_shape = (3, self.target_shape[0], self.target_shape[1])
        expected_xyz_shape = (3, self.target_shape[0], self.target_shape[1])

        consistency_check = (
            rgb_shape == expected_rgb_shape and
            xyz_shape == expected_xyz_shape and
            -1.1 <= rgb_min <= -0.9 and 0.9 <= rgb_max <= 1.1 and  # RGB应该在[-1,1]范围内
            rgb_nan_count == 0 and xyz_nan_count == 0 and
            rgb_inf_count == 0 and xyz_inf_count == 0
        )

        if consistency_check:
            print("✅ 数据处理与训练脚本完全一致！")
        else:
            print("❌ 数据处理存在不一致，请检查上述输出")

        print("========================\n")

        return observation, consistency_check



    def convert_model_action_to_robot_action(self, model_action):
        """
        将模型输出的动作转换为机器人控制器期望的格式

        根据DiffuserActor的实现分析：
        - 模型内部已经处理了旋转参数化转换（unconvert_rot函数）
        - 模型最终输出8维动作：[x, y, z, qx, qy, qz, qw, gripper]
        - 位置已经反归一化到真实世界坐标
        - 旋转已经是四元数格式（根据quaternion_format='xyzw'）
        - 夹爪状态已经通过sigmoid转换为概率

        机器人控制器期望格式：
        - 位置增量(3) + 欧拉角增量(3) + 夹爪(1) = 7维

        Args:
            model_action: 模型输出的8维动作 [x, y, z, qx, qy, qz, qw, gripper]

        Returns:
            robot_action: 7维动作 [dx, dy, dz, roll, pitch, yaw, gripper]
        """
        if len(model_action) != 8:
            raise ValueError(f"模型输出应该是8维，但得到了{len(model_action)}维")

        # 8维格式：[x, y, z, qx, qy, qz, qw, gripper]
        pos = model_action[:3]
        quat = model_action[3:7]  # [qx, qy, qz, qw] 格式（quaternion_format='xyzw'）
        gripper = model_action[7]

        # 四元数转欧拉角
        # 注意：根据训练配置，quaternion_format='xyzw'，所以是[qx, qy, qz, qw]
        euler = Rotation.from_quat(quat).as_euler('xyz')

        robot_action = np.concatenate([pos, euler, [gripper]])

        return robot_action

    def preprocess_observation(self, frame, debug=False):
        """
        预处理观察数据 - 与训练脚本convert.py完全一致，包含图像缩放功能

        Args:
            frame: 原始帧数据，包含RGB和深度
            debug: 是否输出调试信息
        """
        # 分离RGB和深度 - 注意深度处理与convert.py一致
        rgb = frame[..., :3].astype(np.uint8)
        depth = frame[..., 3].astype(np.float32)  # 不在这里除以1000，让depth_to_xyz自动处理

        if debug:
            print(f"原始数据形状: RGB {rgb.shape}, Depth {depth.shape}")
            print(f"原始深度值范围: [{depth.min():.4f}, {depth.max():.4f}]")

        # RGB预处理 - 与convert.py完全一致
        # BGR转RGB
        rgb = cv2.cvtColor(rgb, cv2.COLOR_BGR2RGB)

        # 步骤1: 先生成完整的点云（使用原始深度图）
        xyz = self.depth_to_xyz(depth, debug=debug)  # (H, W, 3)

        # 步骤2: 对RGB图像进行裁剪和缩放
        rgb_resized, transform_info = self.crop_and_resize_image(rgb, self.target_shape, debug=debug)

        # 步骤3: 对点云进行相同的裁剪和缩放
        xyz_resized = self.crop_and_resize_pointcloud(xyz, transform_info, debug=debug)

        # 步骤4: 转换为张量格式
        rgb_tensor = torch.from_numpy(rgb_resized.transpose(2, 0, 1)).float() / 255.0  # (3, H, W)
        # rgb_tensor = (rgb_tensor - 0.5) * 2  # [-1, 1] 与convert.py一致

        xyz_tensor = torch.from_numpy(xyz_resized).float().permute(2, 0, 1)  # (3, H, W)

        # 检查异常值
        if torch.isnan(xyz_tensor).any() or torch.isinf(xyz_tensor).any():
            print("⚠️ Warning: xyz contains NaN or Inf")

        if debug:
            print(f"✅ 最终张量形状: RGB {rgb_tensor.shape}, XYZ {xyz_tensor.shape}")

        return {
            "rgb": rgb_tensor,
            "depth": depth,
            "xyz": xyz_tensor
        }


class ModelInference:
    def __init__(self, model_path, device='cuda', gripper_loc_bounds_path=None):
        self.device = device
        self.gripper_loc_bounds_path = gripper_loc_bounds_path
        self.model = self.load_model(model_path)
        self.model.eval()
        
    def load_model(self, model_path):
        checkpoint = torch.load(model_path, map_location=self.device)
    
        self.gripper_loc_bounds = None
      
        
        # 模型参数必须与训练脚本完全一致
        model_args = {
            'backbone': 'clip',
            'image_size': (256, 256),  # 训练脚本默认值
            'embedding_dim': 120,      # 训练脚本中C=120
            'num_vis_ins_attn_layers': 2,  # 训练脚本默认值
            'use_instruction': False,  # 训练脚本中use_instruction=0
            'fps_subsampling_factor': 5,   # 训练脚本默认值
            'gripper_loc_bounds': self.gripper_loc_bounds,
            'rotation_parametrization': '6D',  # 训练脚本中rotation_parametrization=6D
            'quaternion_format': 'xyzw',       # 训练脚本中quaternion_format=xyzw
            'diffusion_timesteps': 100,       # 训练脚本中diffusion_timesteps=100
            'nhist': 3,                       # 训练脚本中num_history=3
            'relative': False,                # 训练脚本默认值
            'lang_enhanced': False            # 训练脚本默认值
        }
        
        model = DiffuserActor(**model_args)
        model_dict_weight={}
        for key in checkpoint["weight"]:
            _key=key[7:]
            model_dict_weight[_key]=checkpoint["weight"][key]
        model.load_state_dict(model_dict_weight)
        model.eval()
        model.to(self.device)
        
        return model
        
    def predict(self, observation, gripper_history):
        """进行模型推理

        Args:
            observation: 预处理后的观察数据
            gripper_history: 夹爪历史状态，shape为(3, 8)，包含最近3个时间步的状态
        """
        with torch.no_grad():
            batch_size = 1
            trajectory_length = 2  # 根据训练脚本的interpolation_length=2

            # 调试信息
            print(f"Input shapes - RGB: {observation['rgb'].shape}, XYZ: {observation['xyz'].shape}")
            print(f"Gripper history shape: {gripper_history.shape}")

            # RGB和点云数据 - 注意图像尺寸应该与训练时一致
            rgb = observation["rgb"].unsqueeze(0).unsqueeze(0).to(self.device)  # (1, 1, 3, H, W)
            xyz = observation["xyz"].unsqueeze(0).unsqueeze(0).to(self.device)  # (1, 1, 3, H, W)

            print(f"Model input shapes - RGB: {rgb.shape}, XYZ: {xyz.shape}")

            # 轨迹掩码 - 指定生成轨迹的长度
            trajectory_mask = torch.ones(batch_size, trajectory_length).to(self.device)

            # 夹爪历史状态 - 使用真实的历史状态
            # gripper_history shape: (3, 8) -> 转换为 (1, 3, 8) 用于模型输入
            curr_gripper = torch.from_numpy(gripper_history).float().unsqueeze(0).to(self.device)  # (1, 3, 8)

            print(f"Model gripper history shape: {curr_gripper.shape}")
            print(f"Gripper history content:\n{gripper_history}")

            # 创建指令（训练时use_instruction=0，创建零张量）
            instr = torch.zeros(batch_size, 53, 512).to(self.device)

            # 推理时gt_trajectory会被忽略，但仍需要传入（可以是None或任意值）
            gt_trajectory = None

            print("开始模型推理...")

            # 模型推理
            predictions = self.model(
                gt_trajectory,
                trajectory_mask,
                rgb,
                xyz,
                instr,
                curr_gripper,
                run_inference=True
            )

            print(f"模型输出形状: {predictions.shape}")

            # 提取预测的轨迹 - DiffuserActor返回完整轨迹
            # 取第一个时间步的动作作为下一步执行的动作
            predicted_trajectory = predictions.cpu().numpy()  # (1, trajectory_length, action_dim)
            predicted_action = predicted_trajectory[0, 0]  # 取第一个时间步的动作

            print(f"模型原始输出: {predicted_action}")
            print(f"输出维度: {len(predicted_action)}")

            return predicted_action


class RealRobotTester:
    """真机测试主类"""
    def __init__(self, config_path):
        self.config = self.load_config(config_path)
        self.setup_components()
        # 初始化夹爪历史缓存，维护最近3个状态
        self.gripper_history_buffer = []
        self.max_history_length = 3

        # 重要：初始化相对坐标系（仅用于状态获取）
        self.initialize_relative_coordinate_system()
        
    def load_config(self, config_path):
        """加载配置文件"""
        with open(config_path, 'r') as f:
            config = json.load(f)
        return config

    def initialize_relative_coordinate_system(self):
        """初始化相对坐标系，复现训练时的RelativeFrame效果 - 仅用于状态获取"""
        print("初始化相对坐标系...")

        # 使用当前位姿作为重置位姿
        self.robot_controller.set_reset_pose()

        # 如果有预定义的重置位姿，可以使用：
        # reset_pose = np.array([0.588, -0.036, 0.328, 0, 0, 0, 1])  # 示例
        # self.robot_controller.set_reset_pose(reset_pose)

        print("相对坐标系初始化完成")

    def setup_components(self):
        """初始化所有组件"""
        # 相机设置
        camera_config = self.config["camera"]
        self.cap = VideoCapture(
            RSCapture(
                name="front",
                serial_number=camera_config["serial_number"],
                dim=camera_config["dim"],
                exposure=camera_config["exposure"]
            )
        )
        
        # 机器人控制器
        self.robot_controller = RobotController(self.config["robot"]["url"])
        
        # 数据预处理器 - 使用与训练一致的256x256尺寸
        target_shape = self.config.get("preprocessing", {}).get("target_shape", [256, 256])
        self.preprocessor = DataPreprocessor(
            target_shape=tuple(target_shape)
        )
        
        # 模型推理
        self.model_inference = ModelInference(
            self.config["model"]["checkpoint_path"],
            self.config["model"]["device"],
            self.config.get("gripper_loc_bounds_path")
        )

    def update_gripper_history(self, current_gripper_state):
        """更新夹爪历史状态缓存"""
        # 添加当前状态到历史缓存
        self.gripper_history_buffer.append(current_gripper_state.copy())

        # 保持缓存大小不超过最大历史长度
        if len(self.gripper_history_buffer) > self.max_history_length:
            self.gripper_history_buffer.pop(0)

    def get_gripper_history(self):
        """获取格式化的夹爪历史状态，与训练数据格式一致

        返回格式与训练数据一致：
        - history[0]: 2步前的状态 (max(0, i-2))
        - history[1]: 1步前的状态 (max(0, i-1))
        - history[2]: 当前状态 (i)
        """
        if len(self.gripper_history_buffer) == 0:
            # 如果没有历史状态，返回零状态
            return np.zeros((self.max_history_length, 8))

        # 根据训练数据的构造方式创建历史状态
        history = []
        current_idx = len(self.gripper_history_buffer) - 1  # 当前状态的索引

        # 构造3个历史状态：[i-2, i-1, i]
        for offset in [2, 1, 0]:  # 对应 i-2, i-1, i
            hist_idx = max(0, current_idx - offset)  # 使用max(0, ...)防止负索引
            history.append(self.gripper_history_buffer[hist_idx])

        result = np.stack(history, axis=0)  # shape: (3, 8)

        # 调试信息
        print(f"Gripper history buffer length: {len(self.gripper_history_buffer)}")
        print(f"Current gripper state: {self.gripper_history_buffer[-1] if self.gripper_history_buffer else 'None'}")

        return result
        
    def initialize_gripper_history(self):
        """初始化夹爪历史状态，使用相对坐标（与训练时一致）"""
        print("初始化夹爪历史状态...")

        # 使用相对坐标创建初始状态（与训练时一致）
        initial_gripper_state = self.robot_controller.create_training_compatible_gripper_state()

        # 用初始状态填充历史缓存
        for _ in range(self.max_history_length):
            self.update_gripper_history(initial_gripper_state)

        print(f"夹爪历史状态已初始化，初始状态: {initial_gripper_state}")

    def validate_data_processing(self):
        """验证数据处理与训练脚本的一致性"""
        print("开始验证数据处理一致性...")

        # 获取一帧数据进行验证
        frame = self.cap.read()
        observation, is_consistent = self.preprocessor.validate_data_consistency(frame)

        if is_consistent:
            print("✅ 数据处理验证通过，与训练脚本完全一致")
        else:
            print("❌ 数据处理验证失败，存在不一致之处")
            print("请检查上述详细输出，确保所有参数与convert.py一致")

        return is_consistent

    def run_test(self, num_steps=100, validate_first=False):
        """运行真机测试"""
        print("开始真机测试...")
        '''
        # 可选：验证数据处理一致性
        if validate_first:
            print("\n=== 数据处理一致性验证 ===")
            # is_consistent = self.validate_data_processing()
            
            if not is_consistent:
                print("⚠️ 警告：数据处理存在不一致，建议先修复后再继续测试")
                response = input("是否继续测试？(y/n): ")
                if response.lower() != 'y':
                    print("测试已取消")
                    return
            
            print("=== 验证完成，开始正式测试 ===\n")
        '''

        # 初始化夹爪历史状态
        self.initialize_gripper_history()

        try:
            for step in range(num_steps):
                print(f"Step {step + 1}/{num_steps}")

                # 1. 获取观察数据
                frame = self.cap.read()
                # 在第一步启用调试信息，后续步骤可以关闭以减少输出
                debug_mode = (step == 0)
                observation = self.preprocessor.preprocess_observation(frame, debug=debug_mode)

                # 2. 获取当前机器人状态（使用相对坐标，与训练时一致）
                current_gripper_state = self.robot_controller.create_training_compatible_gripper_state()

                # 3. 更新夹爪历史状态
                self.update_gripper_history(current_gripper_state)

                # 4. 获取格式化的夹爪历史
                gripper_history = self.get_gripper_history()

                # 5. 模型推理（传入历史状态而不是单个状态）
                model_predicted_action = self.model_inference.predict(
                    observation,
                    gripper_history
                )

                # 6. 转换模型动作为机器人控制器格式
                # robot_action = self.preprocessor.convert_model_action_to_robot_action(model_predicted_action)
                robot_action = model_predicted_action
                print(f"转换后的机器人动作: {robot_action}")

                # 7. 执行动作（注意：动作执行直接在绝对坐标系中进行，不使用相对坐标）
                self.robot_controller.step(robot_action)

                # 8. 短暂等待
                time.sleep(1) # 稍微延长一些

        except Exception as e:
            print(f"测试过程中发生错误: {e}")
            raise
        finally:
            self.cleanup()
            
    def cleanup(self):
        """清理资源"""
        self.cap.close()
        print("资源已清理")


def main():
    parser = argparse.ArgumentParser(description='真机测试脚本')
    parser.add_argument('--config', type=str, required=True, 
                       help='配置文件路径')
    parser.add_argument('--steps', type=int, default=100000,
                       help='测试步数')
    
    args = parser.parse_args()
    
    # 检查配置文件存在性
    if not os.path.exists(args.config):
        raise FileNotFoundError(f"配置文件不存在: {args.config}")
    
    # 创建测试实例
    tester = RealRobotTester(args.config)
    
    # 运行测试
    tester.run_test(args.steps)


if __name__ == "__main__":
    main()
